## .dockerignore
# Ignore node_modules directory to prevent copying unnecessary dependencies
node_modules

# Ignore local editor settings
.vscode

# Ignore Git repository files
.git

# Ignore build folder
dist
build

# Ignore ESLint, Prettier, and other configuration files that aren't needed in the final image
.eslint*
.prettier*
.editorconfig
.DS_Store

# Ignore environment files - o Vite irá usar o modo correto
# Note: os arquivos .env ainda serão copiados para o build do Vite funcionar
# mas você pode descomentar as linhas abaixo se quiser excluí-los
# .env.development
# .env.test
# .env.production

# Ignore test files and coverage
coverage
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
test-results.json

# Ignore documentation
docs
README.md

# Ignore GitHub workflows
.github