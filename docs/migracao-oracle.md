# Migração Oracle - Pendências de Implementação

## Contexto
Este documento detalha os componentes do fluxo de aprovação Oracle que precisam ser migrados do projeto legado Web.Portal para o novo projeto BRF Central de Aprovações Web.

## Processos Oracle TMS Pendentes

Até o momento, apenas o processo TMS base foi implementado. Os seguintes processos ainda precisam ser migrados:

| # | Nome do Processo | Endpoint | Status |
|---|-----------------|----------|--------|
| 1 | Freight Rate Approvals (TMS) | `/FreightRateApprovalsRateFactor` | ✅ Implementado |
| 2 | TMS Base Project (TMS2) | `/FreightRateApprovalsRateRecord` | ✅ Implementado |
| 3 | TMS Accessorial Cost (TMS3) | `/FreightRateApprovalsAccessorialCost` | ✅ Implementado |
| 4 | TMS Freight Surcharge (TMS4) | `/FreightRateApprovalsFreightSurcharges` | ✅ Implementado |
| 5 | TMS Cost Batch (TMS5) | `/FreightRateApprovalsRRCostBatch` | ✅ Implementado |
| 6 | TMS Cost Batch Rep (TMS6) | `/FreightRateApprovalsRRCostBatchRep` | ✅ Implementado |

## Estrutura de Implementação Necessária

Para cada processo pendente, precisamos implementar:

### 1. Definição do Processo
- Criar arquivo em `/src/constant/processesProps/oracle/[PROCESSO].tsx`
- Implementar interface `ReturnProps<IOracle.ItemProps>`
- Definir propriedades:
  - `title`: Título do processo 
  - `origin`: 'oracle'
  - `type`: Tipo do processo
  - `permission`: Permissão necessária
  - `headerColumns`: Colunas para exibição na tabela
  - `documentDetailHtml`: Template de detalhes
  - `selector`: Função para selecionar o ID do item

### 2. Atualização do Index
- Exportar novos processos em `/src/constant/processesProps/oracle/index.ts`
- Adicionar processos ao objeto `processesConfig` em `/src/constant/processesProps/index.tsx`

### 3. Rotas de API
- Verificar se as rotas para todos os processos estão definidas em `/src/api/routes/oracle.ts`

### 4. Interface de Dados
- Revisar e expandir a interface `IOracle` caso necessário para comportar dados específicos de cada processo

### 5. Testes de Integração
- Testar cada processo individualmente
- Garantir que o fluxo de aprovação/reprovação funcione corretamente
- Validar a exibição de dados e comportamento conforme o sistema legado

## Detalhes de Implementação

### Estrutura do Oracle Service
Os serviços Oracle já implementados são:

- `getPendingApprovals`: Obter aprovações pendentes
- `approveOracle`: Aprovar um documento
- `reproveOracle`: Reprovar um documento
- `updateDataAfterApproval`: Atualizar dados após aprovação/reprovação

### Estrutura das Ações Massivas
Para suportar ações massivas, deve-se verificar se o serviço atual já comporta as funcionalidades de:

- Aprovação em massa
- Reprovação em massa
- Manipulação específica para cada tipo de processo

## Próximos Passos

1. Implementar processo TMS2 (FreightRateApprovalsRateRecord)
2. Implementar processo TMS3 (FreightRateApprovalsAccessorialCost)
3. Implementar processo TMS4 (FreightRateApprovalsFreightSurcharges)
4. Implementar processo TMS5 (FreightRateApprovalsRRCostBatch)
5. Implementar processo TMS6 (FreightRateApprovalsRRCostBatchRep)
6. Testar todos os processos
7. Atualizar documentação
