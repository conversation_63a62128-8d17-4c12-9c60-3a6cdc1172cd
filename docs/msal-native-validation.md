# Refatoração para Usar MSAL Nativo para Validação de Tokens

## 🎯 **Objetivo da Refatoração**

Substituir cálculos manuais de expiração de token por funcionalidades nativas do MSAL, que são mais confiáveis e eficientes.

## ❌ **ANTES: Abordagem Manual**

```typescript
// Calculava manualmente a expiração
const decoded = this.decodeJWT(token);
const currentTime = Math.floor(Date.now() / 1000);
const isExpired = currentTime >= decoded.exp;

// Verificações manuais complexas
if (tokenInfo.isExpired) {
  // Lógica manual de renovação
}
```

**Problemas:**

- ❌ Cálculos manuais de data/hora propensos a erros
- ❌ Não considera timezone/clock skew
- ❌ Duplica lógica que o MSAL já tem internamente
- ❌ Pode não estar sincronizado com o cache interno do MSAL

## ✅ **DEPOIS: Abordagem MSAL Nativa**

```typescript
// Deixa o MSAL gerenciar automaticamente
const response = await pca.acquireTokenSilent({
  account: accounts[0],
  scopes: loginRequest.scopes,
});

// MSAL automaticamente:
// - Verifica expiração
// - Renova se necessário
// - Gerencia cache
// - Trata timezone/clock skew
```

**Benefícios:**

- ✅ **Usa lógica testada e robusta do MSAL**
- ✅ **Automaticamente lida com clock skew**
- ✅ **Cache interno otimizado**
- ✅ **Renovação automática quando apropriado**
- ✅ **Códigos de erro padronizados**

## 🔧 **Métodos Refatorados**

### 1. `validateStoredToken()` - Agora usa MSAL

```typescript
// ANTES: Cálculos manuais
const tokenInfo = this.getTokenExpirationInfo(storedToken);
if (tokenInfo.isExpired) {
  /* lógica manual */
}

// DEPOIS: Delegação ao MSAL
const response = await pca.acquireTokenSilent({
  account: accounts[0],
  scopes: loginRequest.scopes,
  forceRefresh: false, // Deixa MSAL decidir
});
```

### 2. `getValidToken()` - Simplificado

```typescript
// ANTES: Verificações manuais + validação customizada
const validation = await this.validateStoredToken();
if (validation.isValid && !validation.needsRefresh) {
  /* ... */
}

// DEPOIS: Direto via MSAL
const response = await pca.acquireTokenSilent({
  account: accounts[0],
  scopes: loginRequest.scopes,
});
```

### 3. `acquireTokenSilently()` - Focado em MSAL

```typescript
// ANTES: Validação manual primeiro
const validation = await this.validateStoredToken();

// DEPOIS: Direto para MSAL
const response = await pca.acquireTokenSilent({
  account,
  scopes: loginRequest.scopes,
});
```

## 🔍 **Códigos de Erro MSAL para Expiração**

O MSAL retorna códigos específicos quando tokens expiram:

```typescript
const sessionExpiredErrors = [
  "interaction_required", // Token expirado, interação necessária
  "login_required", // Login necessário
  "user_login_error", // Erro de login do usuário
  "token_renewal_error", // Erro na renovação
  "consent_required", // Consentimento necessário
];
```

## 🎛️ **Monitoramento Automático via Eventos MSAL**

```typescript
pca.addEventCallback((event) => {
  if (event.eventType === EventType.ACQUIRE_TOKEN_FAILURE) {
    const error = event.error as any;
    if (sessionExpiredErrors.includes(error.errorCode)) {
      // MSAL detectou expiração automaticamente
      sessionExpiredCallback();
    }
  }
});
```

## 🆕 **Novos Métodos Baseados em MSAL**

### `hasValidTokenInCache()`

```typescript
// Verifica se MSAL tem token válido em cache
const isValid = await AuthService.hasValidTokenInCache();
```

### `getMsalManagedToken()`

```typescript
// Token completamente gerenciado pelo MSAL
const token = await AuthService.getMsalManagedToken();
```

### `isSessionActive()`

```typescript
// Verifica se sessão está ativa via MSAL
const isActive = await AuthService.isSessionActive();
```

### `getAccountInfo()`

```typescript
// Informações da conta direto do MSAL
const account = AuthService.getAccountInfo();
```

## 🚀 **Vantagens da Nova Abordagem**

1. **Confiabilidade** 🛡️

   - Usa código testado em milhões de aplicações
   - Lida com edge cases automaticamente

2. **Performance** ⚡

   - Cache interno otimizado do MSAL
   - Menos operações manuais

3. **Manutenibilidade** 🔧

   - Menos código customizado para manter
   - Atualiza automaticamente com MSAL

4. **Robustez** 💪
   - Trata timezone, clock skew, latência de rede
   - Códigos de erro padronizados

## 📋 **Migração Recomendada**

### Para código existente:

```typescript
// ❌ EVITAR (manual)
const isExpired = AuthService.isTokenExpired(token);
const validation = await AuthService.validateStoredToken();

// ✅ PREFERIR (MSAL nativo)
const token = await AuthService.getMsalManagedToken();
const isActive = await AuthService.isSessionActive();
```

### Para debugging:

```typescript
// ❌ ANTES: Logs manuais de expiração
console.log("Token expira em X minutos");

// ✅ DEPOIS: Logs de eventos MSAL
// Automaticamente logga via event handlers
```

## 🔧 **Como Testar**

1. **Deixe token expirar naturalmente**

   - MSAL detectará automaticamente
   - Evento será disparado
   - Modal aparecerá

2. **Force expiração via MSAL**

   ```typescript
   await pca.clearCache(); // Força nova autenticação
   ```

3. **Monitore console para logs MSAL**
   - ✅ Sucessos automáticos
   - 🔔 Detecções de expiração
   - ❌ Erros com códigos específicos

## 📊 **Resultado Final**

- **Menos código customizado** (redução ~40%)
- **Mais confiável** (usa MSAL testado)
- **Detecção automática** de expiração
- **Performance melhorada** (cache MSAL)
- **Debugging simplificado** (códigos padronizados)
