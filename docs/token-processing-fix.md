# Solução para Processamento Único de Token

## Problema

O método `processToken` estava sendo chamado múltiplas vezes durante o fluxo de autenticação, especialmente quando o token expira e a página é recarregada. Isso causava:

1. Duplicação de login quando o token expira
2. Multiple calls para o backend durante o fluxo de autenticação
3. Problemas de concorrência entre `handleRedirect()` e `acquireTokenSilently()`

## Fluxo Problemático

```
Page Reload → initialize()
             ↓
             handleRedirect() → processToken() ✅ (primeira vez)
             ↓
             acquireTokenSilently() → processToken() ❌ (segunda vez, desnecessária)
```

## Solução Implementada

### 1. Flag de Sessão

Adicionada uma nova flag `sessionTokenProcessed` que garante que o token seja processado apenas **uma vez por sessão**:

```typescript
let sessionTokenProcessed = false; // Flag to ensure token is processed only once per session
```

### 2. Controle Aprimorado no processToken()

```typescript
static async processToken(token: string) {
  // Check if we already processed a token for this session
  if (sessionTokenProcessed) {
    console.log("🚫 Token already processed for this session, skipping");
    return null;
  }

  // ... resto da lógica

  // Mark session as having processed a token
  sessionTokenProcessed = true;
}
```

### 3. Reset da Flag de Sessão

A flag é resetada nos seguintes casos:

- Token expirado
- Logout forçado
- Erro de autenticação
- No accounts encontradas

```typescript
static resetSessionTokenProcessing() {
  sessionTokenProcessed = false;
  isProcessingToken = false;
  lastProcessedToken = null;
  tokenProcessingPromise = null;
}
```

### 4. Verificações Adicionais

Atualizados todos os pontos onde `processToken` é chamado para verificar `sessionTokenProcessed`:

- `acquireTokenSilently()`: Verifica `!sessionTokenProcessed` antes de processar
- Event handlers: Verifica `!sessionTokenProcessed` antes de processar
- `handleRedirect()`: Processa normalmente (primeira tentativa)

## Fluxo Correto Após a Solução

```
Page Reload → initialize()
             ↓
             handleRedirect() → processToken() ✅ (única vez, sessionTokenProcessed = true)
             ↓
             acquireTokenSilently() → ❌ Skip (sessionTokenProcessed = true)
```

## Debug e Monitoramento

### Método de Debug

```typescript
AuthService.getSessionState(); // Retorna estado atual da sessão
```

### Logs Adicionados

- `🔄 processToken called with token:` - Quando processToken é chamado
- `🚫 Token already processed for this session, skipping` - Quando skip por flag de sessão
- `🔄 Resetting session token processing flag` - Quando flag é resetada

## Benefícios

1. **Elimina duplicação**: Token é processado apenas uma vez por sessão
2. **Performance**: Evita calls desnecessárias ao backend
3. **Confiabilidade**: Reduz race conditions e problemas de concorrência
4. **Debuggability**: Logs claros e método de debug disponível

## Casos de Uso

- ✅ Page reload com token válido: processa uma vez
- ✅ Token expira: reseta flag e processa novo token
- ✅ Logout/login: reseta flag adequadamente
- ✅ Multiple tabs: cada tab tem sua própria instância da flag
