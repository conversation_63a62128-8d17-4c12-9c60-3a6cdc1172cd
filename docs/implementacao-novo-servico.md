# Guia para Implementação de Novo Serviço

## Visão Geral

Este documento descreve os passos necessários para implementar um novo serviço de fluxo de aprovação no projeto BRF Central de Aprovações Web. O projeto utiliza o padrão Vite para variáveis de ambiente, diferente do padrão anterior React, e esta documentação está atualizada para os padrões atuais do projeto.

## Sistema Legado Web.Portal

### Localização

O sistema legado Web.Portal está localizado em:

```
/home/<USER>/projects/meta/Web.Portal
```

### Arquivos Principais para Referência

Ao migrar um novo fluxo, recomenda-se analisar os seguintes arquivos do projeto Web.Portal:

1. **Componentes de Card**:

   - `/src/components/Card/[Nome do Serviço]Axios/index.tsx` - Contém lógica de aprovação/reprovação

2. **Rotas**:

   - `/src/routes/cardRoutes.ts` - Define os endpoints utilizados por cada serviço

3. **Ações Massivas**:

   - `/src/utils/MassiveActions.ts` - Contém funções para processamento em lote

4. **Configurações de Colunas**:
   - `/src/utils/columns/[Nome do Serviço]/index.ts` - Define as colunas para exibição nas tabelas

## Processos Intranet Implementados

Os seguintes processos do Intranet foram implementados no sistema, seguindo o padrão estabelecido:

| Código | Nome                   | Descrição                                     |
| ------ | ---------------------- | --------------------------------------------- |
| IN     | Intranet               | Processo padrão do Intranet                   |
| CP     | CreditCardInstallments | Processo de parcelamento de cartão de crédito |
| CS/C1  | Contract               | Processo de contratos                         |
| C2     | ContractProcuration    | Processo de procuração de contratos           |
| PM     | MasterPlan             | Plano mestre                                  |
| TR     | Tribute                | Processo de tributos                          |
| SVA    | SavAdvance             | Adiantamentos                                 |
| SVAC   | SavAccountability      | Prestação de contas                           |
| SVSE   | SavSmallExpense        | Pequenas despesas                             |
| NO     | Norm                   | Normativos                                    |
| NR     | RevogationNorm         | Revogação de normativos                       |
| CR     | CreditCardRequest      | Solicitação de cartão de crédito              |

Cada processo foi implementado como um componente React em `src/constant/processesProps/intranet/`, definindo:

## Processos Procurement Implementados

Os seguintes processos do Procurement foram implementados no sistema:

| Código  | Nome                      | Descrição                             |
| ------- | ------------------------- | ------------------------------------- |
| PR      | Procurement               | Processo padrão do Procurement        |
| MX      | Approval of debts         | Aprovação de dívidas acima do capital |
| ME      | Critical Documentation    | Aprovação de documentação crítica     |
| JR      | Juridic Resolve           | Resolução Jurídica                    |
| JR_CONF | Confrontational           | Confronto Jurídico                    |
| JR_DEP  | Update Of Monthly Deposit | Atualização de Depósito Mensal        |
| JR_CIRC | Circularization           | Circularização                        |

Cada processo foi implementado como um componente React em `src/constant/processesProps/procurement/`, definindo:

## Processos HR Implementados

Os seguintes processos do RH (Human Resources) foram implementados no sistema:

| Código | Nome                               | Descrição                                                      |
| ------ | ---------------------------------- | -------------------------------------------------------------- |
| MOB    | Mobilidade                         | Processos de mobilidade de funcionários                        |
| BRAVO  | Bravo                              | Sistema de reconhecimento e recompensas                        |
| SUFA   | Suplementação de Férias Assis      | Suplementação de férias                                        |
| LMS    | Learning Management System         | Sistema de gestão de aprendizagem                              |
| EPI    | Solicitação de Exceção             | Solicitação de exceção para Equipamento de Proteção Individual |
| PPE    | Equipamento de Proteção Individual | Gestão de Equipamentos de Proteção Individual                  |

Cada processo foi implementado como um componente React em `src/constant/processesProps/hr/`, definindo:

- Colunas para exibição com traduções via i18next
- Formatação de valores monetários utilizando `formatToBRLFloatAmount`
- Visualização detalhada em HTML
- Links para o portal baseados em variáveis de ambiente
- Tipagem consistente com `IIntranetItem`
- Sinalizadores para itens de aprovação, rotas de detalhes e modais

Todos os processos foram integrados ao sistema central através do arquivo `src/constant/processesProps/index.tsx`.

## Falhas Encontradas e Lições Aprendidas

Durante a implementação dos processos de HR e Procurement, algumas falhas e desafios foram identificados e corrigidos:

### Problemas de Tipagem

1. **Cast Explícito Necessário**:

   - **Problema**: O TypeScript apresentava erros de tipagem ao adicionar novos processos ao objeto principal.
   - **Solução**: Foi necessário utilizar cast explícito (`as unknown as ReturnProps<unknown>`) ao registrar os processos no arquivo `src/constant/processesProps/index.tsx`.

2. **Interfaces Faltantes**:
   - **Problema**: Processos como EPI e PPE necessitavam de interfaces que não existiam no sistema.
   - **Solução**: Criação do arquivo `src/interfaces/hr.ts` com todas as interfaces necessárias para os processos de HR.

### Inconsistências de Exportação

1. **Exportações Duplicadas**:

   - **Problema**: No arquivo de índice do HR, havia exportações duplicadas gerando erros de lint.
   - **Solução**: Consolidação das exportações em um único formato para evitar redundâncias.

2. **Estrutura de Arquivos**:
   - **Problema**: Alguns processos não estavam devidamente estruturados em seus respectivos diretórios.
   - **Solução**: Organização dos componentes em diretórios específicos (hr/, procurement/, intranet/)

### Problemas de Documentação

1. **Processos Incompletos**:

   - **Problema**: A documentação inicial não listava todos os processos Procurement e HR necessários.
   - **Solução**: Consulta ao sistema legado para identificar todos os processos e atualização da documentação.

2. **Erros de Formatação**:
   - **Problema**: Tabelas markdown com formatação incorreta (falta de pipes finais).
   - **Solução**: Correção dos erros de formatação para manter consistência.

### Melhores Práticas Implementadas

1. **Padronização de Componentes**:

   - Todos os novos processos seguem o mesmo padrão estrutural dos existentes.
   - Uso consistente de traduções via i18next.

2. **Documentação Abrangente**:

   - Todos os processos implementados foram documentados com código, nome e descrição.
   - Criação de interfaces claras e específicas para cada tipo de processo.

3. **Tratamento de Ambientes**:
   - Uso correto de variáveis de ambiente Vite (`import.meta.env`) com fallback para o formato antigo (`import.meta.env`).

Essas lições são importantes para futuras implementações de novos processos no sistema.

## Fluxos de Implementação

### 1. Definição da Interface de Dados

- **Arquivo**: `/src/interfaces/I[Nome do Serviço].ts`
- **Objetivo**: Criar uma interface TypeScript que represente a estrutura de dados do novo serviço.
- **Etapas**:
  - Identificar campos obrigatórios (id, status, documento, data, requisitante)
  - Adicionar campos específicos do serviço
  - Incluir index signature para campos dinâmicos se necessário

**Exemplo**:

```typescript
export interface INovoServico {
  id: string;
  documento: string;
  dataCriacao: string;
  requisitante: string;
  status: string;
  descricao?: string;
  [key: string]: any;
}
```

### 2. Implementação do Serviço

- **Arquivo**: `/src/services/[nome-servico].ts`
- **Objetivo**: Criar métodos para interagir com a API do novo serviço
- **Métodos Essenciais**:
  - getPendingApprovals(): Obter aprovações pendentes
  - approveDocument(): Aprovar documento
  - reprovalDocument(): Reprovar documento
  - updateDataAfterApproval(): Atualizar dados após aprovação

**⚠️ IMPORTANTE**: Utilize as rotas importadas do arquivo de rotas API e não URLs diretas

**Exemplo**:

```typescript
import * as routes from "../api/routes/novo-servico";

export const NovoServicoService = {
  async getPendingApprovals(): Promise<
    IResponse<{ total: number; tasks: INovoServico[] }>
  > {
    try {
      const { data, status } = await api.get(routes.getPendingApprovals());
      return {
        data: { total: data?.length || 0, tasks: data || [] },
        success: status === 200,
      };
    } catch (error) {
      return handleApiError(error, "NovoServico");
    }
  },
  // Outros métodos...
};
```

### 3. Configuração de Rotas API

- **Arquivo**: `/src/api/routes/[nome-servico].ts`
- **Objetivo**: Definir endpoints para operações do serviço
- **Etapas**:
  - Criar uma constante baseUrl usando variáveis de ambiente Vite
  - Criar funções para cada endpoint necessário
  - Utilizar o baseUrl para construir os endpoints

**⚠️ IMPORTANTE**: Sempre use o padrão Vite (`import.meta.env.VITE_*`) para variáveis de ambiente, não o padrão React (`import.meta.env.VITE_*`)

**Exemplo**:

```typescript
const baseUrl = `${import.meta.env.VITE_NOVO_SERVICO}`;

export const getPendingApprovals = () => `${baseUrl}/getPendingApprovals`;
export const approve = () => `${baseUrl}/approve`;
export const reproval = () => `${baseUrl}/reproval`;
export const getDetails = () => `${baseUrl}/getDetails`;
```

### 4. Definição dos Processos

- **Diretório**: `/src/constant/processesProps/[nome-servico]/`
- **Objetivo**: Criar componentes React para cada processo do serviço
- **Etapas**:
  - Criar arquivo para cada processo (`Processo1.tsx`, `Processo2.tsx`, etc.)
  - Implementar interface `ReturnProps<I[Nome do Serviço]>`
  - Definir propriedades essenciais

**Exemplo**:

```typescript
export const Processo1: ReturnProps<INovoServico> = {
  title: 'Nome do Processo',
  origin: 'novo-servico',
  type: 'TIPO_DO_PROCESSO',
  permission: 'permissao_necessaria',

  headerColumns: [
    // Definição das colunas da tabela
  ],

  documentDetailHtml: (row) => (
    // Template HTML para detalhamento
  ),

  selector: (row) => row.id,
};
```

### 5. Configuração de Exportação

- **Arquivo**: `/src/constant/processesProps/[nome-servico]/index.ts`
- **Objetivo**: Exportar todos os processos do serviço
- **Exemplo**:

```typescript
export { Processo1 } from "./Processo1";
export { Processo2 } from "./Processo2";
// Outros processos...
```

### 6. Integração com Sistema Central

- **Arquivo**: `/src/constant/processesProps/index.tsx`
- **Objetivo**: Adicionar processos ao sistema central
- **Etapas**:
  - Importar processos do novo serviço
  - Criar objeto de configuração para o serviço
  - Adicionar ao objeto processesConfig

**Exemplo**:

```typescript
import { Processo1, Processo2 } from "./[nome-servico]";

const novoServicoProcesses = {
  Processo1: { ...Processo1 },
  Processo2: { ...Processo2 },
  // Outros processos...
};

const processesConfig: Record<string, ReturnProps<any>> = {
  ...sapProcesses,
  ...aribaProcesses,
  ...commercialProcesses,
  ...serviceNowProcesses,
  ...oracleProcesses,
  ...novoServicoProcesses, // Novo serviço adicionado aqui
};
```

### 7. Configuração de Variáveis de Ambiente

- **Arquivo**: `.env` e `.env.example`
- **Objetivo**: Definir URLs base para API do serviço
- **Exemplo**:

```
VITE_NOVO_SERVICO=https://api.exemplo.com/novo-servico
```

## Testes e Validação

### 1. Testes Unitários dos Serviços

- Verificar chamadas de API
- Testar tratamento de erros
- Validar transformação de dados

### 2. Testes de Integração

- Verificar fluxo completo de aprovação/reprovação
- Testar exibição correta dos dados
- Validar comportamento do UI

### 3. Validações Específicas

- Verificar se todos os endpoints estão disponíveis e funcionando
- Confirmar que permissões estão corretamente configuradas
- Testar cenários de erro e recuperação

## Boas Práticas

1. **Consistência**: Manter o mesmo padrão de nomes e estruturas dos outros serviços
2. **Tipagem**: Utilizar TypeScript para garantir tipagem correta dos dados
3. **Modularização**: Separar componentes lógicos em arquivos distintos
4. **Reutilização**: Aproveitar componentes e funções existentes quando possível
5. **Documentação**: Documentar funcionalidades específicas e comportamentos não óbvios
