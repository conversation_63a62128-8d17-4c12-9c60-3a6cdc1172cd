# Sistema de Gerenciamento de Sessão Expirada

## Visão Geral

O sistema foi refatorado para fornecer um gerenciamento robusto de sessão e tokens MSAL. A<PERSON><PERSON>, quando uma sessão expira, ao invés de fazer login automático, a aplicação mostra um modal informando ao usuário que a sessão expirou.

## Funcionalidades Implementadas

### 1. AuthService Melhorado

#### Novos Métodos:

- `decodeJWT(token: string)`: Decodifica tokens JWT
- `isTokenExpired(token: string)`: Verifica se um token está expirado
- `willTokenExpireSoon(token: string, minutesThreshold?: number)`: Verifica se o token vai expirar em breve
- `getTokenExpirationInfo(token: string)`: Retorna informações detalhadas sobre a expiração do token
- `validateStoredToken()`: Valida o token armazenado no localStorage
- `getValidToken()`: Sempre retorna um token válido ou null
- `setSessionExpiredCallback(callback)`: Configura callback para notificar quando a sessão expira
- `manualLogin()`: Método para login manual quando solicitado pelo usuário

#### Comportamento Modificado:

- **Não faz mais login automático** quando a sessão expira
- **Notifica o hook de auth** através do callback quando a sessão expira
- **Valida tokens** antes de usá-los em requisições
- **Logs detalhados** para debugging

### 2. Hook useAuth Atualizado

#### Novos Estados e Métodos:

- `isSessionExpired`: Boolean indicando se a sessão expirou
- `handleSessionExpired()`: Callback chamado pelo AuthService quando a sessão expira
- `startNewLogin()`: Inicia um novo processo de login manualmente

#### Integração com AuthService:

```typescript
// O hook agora configura callbacks no AuthService
AuthService.setSessionExpiredCallback(handleSessionExpired);
```

### 3. Modal de Sessão Expirada

Componente `SessionExpiredModal` que:

- Mostra quando `isSessionExpired` é true
- Oferece opções para o usuário:
  - **Fazer login novamente**: Chama `startNewLogin()`
  - **Recarregar página**: Reinicia a aplicação
- **Não pode ser fechado** sem uma ação do usuário

## Como Usar

### 1. Configuração Automática

O sistema já está configurado automaticamente:

- O `AuthProvider` configura os callbacks necessários
- O `App.tsx` inclui o modal de sessão expirada
- Todos os métodos do AuthService usam o novo sistema

### 2. Verificação Manual de Token

```typescript
import { AuthService } from "./services/authService";

// Verificar se um token está válido
const token = localStorage.getItem("employeeToken");
if (token && AuthService.isTokenExpired(token)) {
  console.log("Token expirado!");
}

// Obter informações de expiração
const info = AuthService.getTokenExpirationInfo(token);
console.log("Expira em:", info.expiresInMinutes, "minutos");

// Obter sempre um token válido
const validToken = await AuthService.getValidToken();
if (validToken) {
  // Usar o token
} else {
  // Sessão expirou, modal será mostrado automaticamente
}
```

### 3. Personalizando o Modal

Para personalizar o modal de sessão expirada, edite:
`src/components/SessionExpiredModal/index.tsx`

### 4. Adicionando Callbacks Personalizados

```typescript
// No seu componente
import { useAuth } from "./hooks/auth";

const MyComponent = () => {
  const { isSessionExpired, startNewLogin } = useAuth();

  // Reagir a mudanças na sessão
  useEffect(() => {
    if (isSessionExpired) {
      // Fazer algo quando a sessão expira
      console.log("Sessão expirou!");
    }
  }, [isSessionExpired]);

  // Iniciar login manualmente
  const handleLogin = () => {
    startNewLogin();
  };

  return (
    <div>
      {isSessionExpired && <div>Sessão expirada!</div>}
      <button onClick={handleLogin}>Login Manual</button>
    </div>
  );
};
```

## Fluxo de Expiração de Sessão

1. **Token expira** ou falha na renovação silenciosa
2. **AuthService detecta** a expiração durante tentativa de uso
3. **Callback é chamado** notificando o hook useAuth
4. **Estado `isSessionExpired`** é definido como true
5. **Modal é exibido** automaticamente
6. **Usuário escolhe** uma ação (login ou reload)
7. **Sistema processa** a escolha do usuário

## Benefícios

- ✅ **Não há mais redirecionamentos automáticos**
- ✅ **Usuário tem controle** sobre quando fazer login
- ✅ **Experiência mais clara** com modal explicativo
- ✅ **Sistema robusto** de validação de tokens
- ✅ **Logs detalhados** para debugging
- ✅ **Fácil personalização** do comportamento

## Tradução

Adicione as seguintes chaves no seu sistema de tradução:

```json
{
  "Session.Expired": "Sessão Expirada",
  "Your.session.has.expired.Please.login.again.to.continue": "Sua sessão expirou. Faça login novamente para continuar.",
  "Reload.Page": "Recarregar Página",
  "Login.Again": "Fazer Login"
}
```

## Debug

Para debugar problemas de token, verifique o console do navegador. O sistema agora logga:

- Informações de expiração de token
- Tentativas de renovação
- Falhas de autenticação
- Callbacks de sessão expirada

## Migração

Se você tinha código que dependia do comportamento anterior de login automático, você pode:

1. **Usar `manualLogin()`** quando quiser forçar um login
2. **Monitorar `isSessionExpired`** para reagir à expiração
3. **Usar `getValidToken()`** ao invés de `getIdToken()` para garantir tokens válidos
