import type { Config } from "jest";

const config: Config = {
  testEnvironment: "jsdom",
  setupFilesAfterEnv: ["<rootDir>/src/__tests__/setupTests.ts"],
  moduleNameMapper: {
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "^@/(.*)$": "<rootDir>/src/$1",
  },
  transform: {
    "^.+\\.[tj]sx?$": "@swc/jest", // Usando SWC para transformar arquivos TS/JS
  },
  transformIgnorePatterns: ["/node_modules/(?!(.*\\.mjs$))"],
  testMatch: ["**/__tests__/**/*.test.[jt]s?(x)"],
  extensionsToTreatAsEsm: [".ts", ".tsx"],
  reporters: [
    "default",
    [
      "jest-summary-reporter",
      {
        failuresOnly: false,
        outputPath: "/coverage",
      },
    ],
  ],
  collectCoverageFrom: [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/main.tsx",
    "!src/vite-env.d.ts",
  ],
  coverageDirectory: "coverage",
  coverageReporters: ["text", "lcov", "html"],
};

export default config;
