import { IResponse } from "@/interfaces";
import { getLocalStorageItem } from "@/utils/storage";
import { api } from "./api";
import { RouteAccountConfig } from "./routes";

export const registerAccountConfig = async (
  accountId: string,
  languageOption: string
): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.put(
      RouteAccountConfig.registerAccountConfig,
      {},
      {
        params: {
          accountId,
          languageOption,
          keyJobs: import.meta.env.VITE_KEY_JOBS,
        },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    console.error("Error registering account config:", error);
    return { data: null, success: false };
  }
};

export const doneTutorial = async () => {
  try {
    const { status } = await api.put(`${RouteAccountConfig.doneTutorial}`, {});
    return { success: status === 200 };
  } catch (e) {
    return {
      data: undefined,
      success: false,
      message: "Falha na requisição, tente novamente.",
    };
  }
};
