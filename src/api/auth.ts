import { IResponse, IUser } from "../interfaces";
import { ILoginReporting } from "../interfaces/loginReporting";
import { handleTerminal } from "../utils/auth";
import { api, objectCatch } from "./api";
import { RouteAuth } from "./routes";

const terminal = handleTerminal();

export const login = async (
  token: string
): Promise<IResponse.Default<IUser>> => {
  const url = `${RouteAuth.login}?terminal=${terminal}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Ocp-Apim-Subscription-Key": import.meta.env.VITE_APIM_SUBSCRIPTION_KEY,
  };

  try {
    const { data, status } = await api.get(url, { headers });
    return { data, success: status === 200 };
  } catch (error: any) {
    return { ...objectCatch, data: {} as IUser };
  }
};

export const loginReporting = async (
  userId: string | number
): Promise<IResponse.Default<ILoginReporting>> => {
  try {
    const { data, status } = await api.post(
      `${RouteAuth.urlLoginRepresenting}`,
      {
        authorizationKey: import.meta.env.VITE_AUTH_KEY,
        userId,
        terminal,
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    return { ...objectCatch, data: {} as ILoginReporting };
  }
};
