import { IResponse, ITranslation } from "../interfaces";
import { api, arrayCatch } from "./api";
import { RouteTranslation } from "./routes";

export const getTranslation = async (
  language: string,
  token: string
): Promise<IResponse.Default<ITranslation[]>> => {
  try {
    const { data, status } = await api.get(
      `${RouteTranslation.translation(language)}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (e) {
    return { ...arrayCatch, data: [] as ITranslation[] };
  }
};
