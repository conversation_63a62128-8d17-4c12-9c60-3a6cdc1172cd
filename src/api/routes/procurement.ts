export const procurementRoutesGet: any = {
  JR_MAN: "EnterpriseSubjectManagement/GetManageLegal",
  JR_CONF: "Confronting/GetConfronting",
  JR_CIRC: "Circularization/GetCircularization",
  JR_DEP: "UpdateMonthlyDeposit/GetMonthlyDeposit",
  MX: "EletronicMarket/GetOverDebits",
  ME: "EletronicMarket/GetCriticalDocumentation",
};

export const procurementRoutesUpdate: any = {
  JR_MAN: "EnterpriseSubjectManagement/UpdateManageLegal",
  JR_CONF: "Confronting/UpdateConfronting",
  JR_CIRC: "Circularization/UpdateCircularization",
  JR_DEP: "UpdateMonthlyDeposit/UpdateMonthlyDeposit",
  MX: "EletronicMarket/UpdateOverDebits",
  ME: "EletronicMarket/UpdateCriticalDocumentation",
};

const baseUrl = import.meta.env.VITE_PROCUREMENT;

export const getPendingApprovals = (processType: string) => {
  const routeAcronim = procurementRoutesGet[processType];
  return `${baseUrl}/${routeAcronim}`;
};

export const getCircularizationDetail = (documentNumber: number) => {
  return `${baseUrl}/Circularization/GetCircularizationDetail/${documentNumber}`;
};

export const approvalReprovalJRDocument = (processType: string) => {
  const routeAcronim = procurementRoutesUpdate[processType];
  return `${baseUrl}/${routeAcronim}`;
};

export const approvalReprovalExtMarketDocument = (
  processType: string,
  documentNumber: string
) => {
  const routeAcronim = procurementRoutesUpdate[processType];
  return `${baseUrl}/${routeAcronim}/${documentNumber}`;
};
