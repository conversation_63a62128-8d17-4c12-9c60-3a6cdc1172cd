export const searchUsersRepresenting = (value: string, page?: number) => {
  const baseUrl = `${
    import.meta.env.VITE_DOMAIN
  }/Account/GetReportAccounts/${value}?showPermissions=true`;
  return page !== undefined ? `${baseUrl}&page=${page}` : baseUrl;
};

export const searchUsersSubordinates = (
	value: string,
	userId: string,
  page?: number,
) => {
  const baseUrl = `${
    import.meta.env.VITE_INTEGRATION
  }/PI/GetSubordinates?searchText=${value}&userId=${userId}&showPermissions=true`;
  return page !== undefined ? `${baseUrl}&page=${page}` : baseUrl;
};

export const representateAccount = `${
  import.meta.env.VITE_DOMAIN
}/Account/RepresentateAccount`;
