import { IIntranet } from "@/interfaces";

const baseUrl = import.meta.env.VITE_INTRANET;

const intranetRoutes: Record<
  IIntranet.ProcessKey,
  {
    get: string;
    approve: string;
    disapprove: string;
    detail?: string;
  }
> = {
  CS: {
    get: "CreditCardRequests",
    approve: "ApproveCreditCardRequests",
    disapprove: "DisapproveCreditCardRequests",
  },
  CP: {
    get: "CreditCardInstallments",
    approve: "ApproveCreditCardInstallments",
    disapprove: "DisapproveCreditCardInstallments",
  },
  C1: {
    get: "Contracts",
    approve: "ApproveContracts",
    disapprove: "DisapproveContracts",
  },
  C2: {
    get: "ContractsProcurations",
    approve: "ApproveContractsProcurations",
    disapprove: "DisapproveContractsProcurations",
  },
  PM: {
    get: "MasterPlans",
    approve: "ApproveMasterPlans",
    disapprove: "DisapproveMasterPlans",
    detail: "MasterPlanDetail",
  },
  TR_INTRANET: {
    get: "Tributes",
    approve: "ApproveTributes",
    disapprove: "DisapproveTributes",
    detail: "TributeDetail",
  },
  SV_1: {
    get: "SavAdvances",
    approve: "ApproveSavAdvances",
    disapprove: "DisapproveSavAdvances",
  },
  SV_2: {
    get: "SavAccountabilities",
    approve: "ApproveSavAccountabilities",
    disapprove: "DisapproveSavAccountabilities",
  },
  SV_3: {
    get: "SavSmallExpenses",
    approve: "ApproveSavSmallExpenses",
    disapprove: "DisapproveSavSmallExpenses",
    detail: "SavSmallExpenseDetail",
  },
  NO: {
    get: "Norms",
    approve: "ApproveNorm",
    disapprove: "DisapproveNorm",
    detail: "NormDetail",
  },
  NR: {
    get: "RevogationNorms",
    approve: "ApproveRevogationNorm",
    disapprove: "DisapproveRevogationNorm",
  },
};

export const getPendingApprovals = (process: IIntranet.ProcessKey) => {
  const routeAcronim = intranetRoutes[process].get;
  return `${baseUrl}/${routeAcronim}`;
};

export const getDocumentDetails = (
  documentNumber: string,
  process: IIntranet.ProcessKey,
  language = "pt"
) =>
  `${baseUrl}/detalhes/${documentNumber}?processo=${process}&idioma=${language}`;

export const approvalReprovalDocument = (
  params: IIntranet.ApprovalReprovalParams,
  processType: IIntranet.ProcessKey
) => {
  const { status, docId, comments } = params;

  console.log(processType);
  const routeAcronim =
    status === "A"
      ? intranetRoutes[processType].approve
      : intranetRoutes[processType].disapprove;

  return status === "A"
    ? `${baseUrl}/${routeAcronim}/${docId}`
    : `${baseUrl}/${routeAcronim}/${docId}/${comments}`;
};
