const baseUrl = `${import.meta.env.VITE_SEO_DIGITAL}/SeoDigital`;

export const getPendingApprovals = `${baseUrl}/PendingList`;

export const approve = (typeOfApproval: number, processCode: string) => {
  return `${baseUrl}/ChangeStatus/${typeOfApproval}/${processCode}/A`;
};

export const reproval = (
  typeOfApproval: number,
  processCode: string,
  reason?: string
) => {
  return `${baseUrl}/ChangeStatus/${typeOfApproval}/${processCode}/R?reason=${reason}`;
};

export const getDetails = (processType?: string) => {
  return processType
    ? `${baseUrl}/${processType}/getDetails`
    : `${baseUrl}/getDetails`;
};

export const getPortalLink = (topicId: string, documentId: string) => {
  return `${
    import.meta.env.VITE_PORTAL_AUTO_CONTROLE_MANU
  }/${topicId}/${documentId}`;
};

export const getMassiveApprovals = () => {
  return `${baseUrl}/getMassiveApprovals`;
};

export const massiveApprove = () => {
  return `${baseUrl}/massiveApprove`;
};

export const massiveReproval = () => {
  return `${baseUrl}/massiveReproval`;
};
