export const hrRoutes: any = {
  SUFA_1: "Transference",
  SUFA_2: "DemissionRequest",
  SUFA_3: "SalaryUpdate",
  SUFA_4: "PositionCreate",
  EPI: "ExceptionRequestPPE",
  LM: "LMS",
};

export const getPendingApprovals = (processType: string) => {
  const routeAcronim = hrRoutes[processType];
  return `${import.meta.env.VITE_HR}/${routeAcronim}`;
};

export const getDocumentDetail = (processType: string, documentId: string) => {
  const routeAcronim = hrRoutes[processType];
  return `${import.meta.env.VITE_HR}/${routeAcronim}/${documentId}`;
};

// export const approvalReprovalDocument = (params: {

export const approvalReprovalDocument = (
  processType: string,
  documentId?: string,
  status?: "A" | "R" | boolean,
  comment?: string
) => {
  const routeAcronim = hrRoutes[processType];

  if (processType !== "EPI") {
    return !!comment
      ? `${
          import.meta.env.VITE_HR
        }/${routeAcronim}/${documentId}/${status}?comment=${comment}`
      : `${import.meta.env.VITE_HR}/${routeAcronim}/${documentId}/${status}`;
  }

  return `${import.meta.env.VITE_HR}/${routeAcronim}`;
};
