import { <PERSON><PERSON><PERSON><PERSON> } from "@/interfaces";

const oracleParamRoute: any = {
  TMS_1: "RMS051_PENDING_APPROVAL_ITEMS",
  TMS_2: "RMS051_2_PENDIN_APPROV_ITEMS",
  TMS_3: "RMS051_3_PENDIN_APPROV_ITEMS",
  TMS_4: "RMS051_4_PENDIN_APPROV_ITEMS",
  TMS_5: "RMS051_5_PENDIN_APPROV_ITEMS",
  TMS_6: "RMS051_6_PENDIN_APPROV_ITEMS",
};

export const oracleTypeRoute: any = {
  TMS_1: "rateFactor",
  TMS_2: "rateRecord",
  TMS_3: "AccessorialCost",
  TMS_4: "FreightSurcharge",
  TMS_5: "RRCostBatch",
  TMS_6: "RRCostBatchRep",
};

const baseUrl = `${import.meta.env.VITE_ORACLE}/Oracle`;

export const getPendingApprovals = (
  processType: IOracle.OracleTypesProcess
) => {
  const param = oracleParamRoute[processType];
  const type = oracleTypeRoute[processType];

  return `${baseUrl}/PendingList?param=${param}&type=${type}`;
};

export const approvalReprovalDocument = `${baseUrl}/ApprovalStatus`;
