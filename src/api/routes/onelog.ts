const baseUrl = `${import.meta.env.VITE_ONELOG}/OneLog/Pendencies`;

export const getDocuments = `${baseUrl}`;

export const getDocumentDetails = (documentNumber: string) =>
  `${baseUrl}/${documentNumber}`;

export const approvalReprovalDocument = (status: "A" | "R") => {
  return status === "A" ? `${baseUrl}/approval` : `${baseUrl}/disapproval`;
};

export const approvalReprovalDetailDocument = (status: "A" | "R") => {
  return status === "A"
    ? `${baseUrl}/approvaldetail`
    : `${baseUrl}/disapprovaldetail`;
};
