import i18n from "../../hooks/translation";

const baseUrl = `${import.meta.env.VITE_SAP}/Sap`;

export const getPendingApprovals = `${baseUrl}`;

export const getDocumentDetails = (
  documentNumber: string,
  process: string,
  additional1?: string
) =>
  `${baseUrl}/Document/${documentNumber}?language=${i18n.language}&process=${process}&additional1=${additional1}`;

export const approvalReprovalDocument = (documentNumber: string) =>
  `${baseUrl}/update`;
