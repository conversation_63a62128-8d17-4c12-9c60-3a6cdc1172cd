const baseUrl = `${import.meta.env.VITE_COMMERCIAL}/sales`;

export const commercialRoutesGet: any = {
  GCC: "tradespend",
};

export const getDocuments = (process: string) =>
  process === "SALES"
    ? `${baseUrl}`
    : `${baseUrl}/${commercialRoutesGet[process]}`;

export const approvalReprovalDocumentSales = `${baseUrl}/update`;

export const approvalReprovalDocumentGcc = (documentCode: string) =>
  `${baseUrl}/tradespend/update/${documentCode}`;
