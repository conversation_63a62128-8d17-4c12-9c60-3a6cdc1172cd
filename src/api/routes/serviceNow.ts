import i18n from "../../hooks/translation";

const baseUrl = `${import.meta.env.VITE_SERVICENOW}/ServiceNow`;

export const getDocuments = `${baseUrl}/PenddingApproval`;

export const getDocumentDetails = (documentNumber: string) =>
  `${baseUrl}/ViewAproval/${documentNumber}?language=${i18n.language}`;

export const approvalReprovalDocument = (
  registerId: string,
  status: "A" | "R",
  comment?: string
) => {
  return status === "A"
    ? `${baseUrl}/ChangeStatus/${status}/${registerId}`
    : `${baseUrl}/ChangeStatus/${status}/${registerId}?comment=${comment}`;
};
