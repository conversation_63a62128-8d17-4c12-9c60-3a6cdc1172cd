import { AllProcessesTypes } from "@/components/AccordionTable";
import { api } from "../api";
import {
  AptusApprovalService,
  AribaApprovalService,
  AvipamApprovalService,
  CommercialApprovalService,
  HRApprovalService,
  IntranetApprovalService,
  OnelogApprovalService,
  OracleApprovalService,
  ProcurementApprovalService,
  SapApprovalService,
  ServiceNowApprovalService,
} from "./strategy";
import {
  BaseApprovalService,
  IApprovalResponse,
} from "./strategy/base.service";

const strategy: Partial<Record<ApprovalOrigin, any>> = {
  SAP: SapApprovalService,
  ARIBA: AribaApprovalService,
  COMMERCIAL: CommercialApprovalService,
  SERVICENOW: ServiceNowApprovalService,
  ORACLE: OracleApprovalService,
  APTUS: AptusApprovalService,
  HR: HRApprovalService,
  PROCUREMENT: ProcurementApprovalService,
  AVIPAM: AvipamApprovalService,
  ONELOG: OnelogApprovalService,
  INTRANET: IntranetApprovalService,
} as const;
interface IApprovalRequest {
  documentId: string;
  reason: string;
}

export interface IApprovalService {
  origin: ApprovalOrigin;
  rowData: AllProcessesTypes;
  status: string | boolean | number;
  reason?: string;
  process?: string;
}

export interface ApprovalResponse<T = any> {
  success: boolean;
  message?: string;
  data?: {
    header: T;
    detail?: T;
  };
}

export type ApprovalOrigin =
  | "SAP"
  | "ARIBA"
  | "AVIPAM"
  | "HR"
  | "ONELOG"
  | "INTRANET"
  | "PROCUREMENT"
  | "APTUS"
  | "DOCUSIGN"
  | "COMMERCIAL"
  | "SERVICENOW"
  | "ORACLE"
  | "SEODIGITAL";

export class ApprovalService<T> {
  private apiService: BaseApprovalService<T>;

  constructor(private document: IApprovalService) {
    this.document = document;
    this.apiService = new strategy[this.document.origin](this.document);
  }

  async approveReproveDocument(): Promise<IApprovalResponse<T>> {
    return this.apiService.approveReproveDocument();
  }

  public static getApprovalStatus(
    origin: ApprovalOrigin,
    isApproval: boolean,
    isNcProcess: boolean = false  
  ): string {
    const statusMap: Record<
      ApprovalOrigin,
      { approve: string; reject: string; return?: string }
    > = {
      SAP: { approve: "A", reject: "R", return: "B" },
      ARIBA: { approve: "A", reject: "D" },
      AVIPAM: { approve: "A", reject: "R" },
      HR: { approve: "A", reject: "R" },
      ONELOG: { approve: "A", reject: "R" },
      INTRANET: { approve: "A", reject: "R" },
      PROCUREMENT: { approve: "1", reject: "0" },
      APTUS: { approve: "1", reject: "2" },
      DOCUSIGN: { approve: "A", reject: "R" },
      COMMERCIAL: { approve: "A", reject: "R" },
      SERVICENOW: { approve: "A", reject: "R" },
      ORACLE: { approve: "APPROVED", reject: "REJECTED" },
      SEODIGITAL: { approve: "A", reject: "R" },
    };

    if (!statusMap[origin]) {
      throw new Error(`Invalid origin: ${origin}`);
    }

    if (isNcProcess) {
      return statusMap[origin].return ?? statusMap[origin].reject;
    }

    return isApproval ? statusMap[origin].approve : statusMap[origin].reject;
  }

  async disapproveDocument(
    data: IApprovalRequest
  ): Promise<IApprovalResponse<T>> {
    const response = await api.post<IApprovalResponse<T>>(
      "/approvals/disapprove",
      data
    );
    return response.data;
  }

  async getApprovals(): Promise<IApprovalResponse<T>[]> {
    const response = await api.get<IApprovalResponse<T>[]>("/approvals");
    return response.data;
  }
}
