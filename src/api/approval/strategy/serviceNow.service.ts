import { APIServiceNow } from "@/api";
import { IServiceNow } from "@/interfaces";
import { ServiceNowService } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class ServiceNowApprovalService extends BaseApprovalService<IServiceNow.ItemProps> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IServiceNow.ItemProps>
  > {
    const { rowData, status, reason } = this.document;

    const params: IServiceNow.ApprovalReprovalParams = {
      registerId: rowData.Registro,
      status: status as "A" | "R",
      comment: reason,
    };

    const response = await ServiceNowService.approvalReprovalDocument(params);

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: `${
        params.status === "A" ? t("Success.in.approving") : t("Failure.success")
      } ${t("Document")} ${params.registerId}`,
      success: response.success,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<IServiceNow.ItemProps[]>> {
    try {
      const response = await APIServiceNow.getPendingApprovals();

      if (!response.success || !response.data) {
        return {
          data: { header: [], detail: [] },
          message: "Error fetching pending approvals",
          success: false,
        };
      }

      const preparedData = {
        header: response.data.header || [],
        detail: response.data.detail || [],
      };

      return {
        data: preparedData,
        message: "",
        success: true,
      };
    } catch (error) {
      console.error("Error getting approvals:", error);
      return {
        data: [],
        message: "Error getting approvals",
        success: false,
      };
    }
  }
}
