import { IHR } from "@/interfaces";
import { HRDocument } from "@/interfaces/IHR";
import { HRService } from "@/services/hr";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class HRApprovalService extends BaseApprovalService<IHR.HRDocument> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<IApprovalResponse<IHR.HRDocument>> {
    const { rowData, status, reason, process } = this.document;
    let response;

    if (process === "EPI") {
      const epiRow = rowData as IHR.IHRExceptionRequest;
      const params: IHR.EPIApprovalParams = {
        code: epiRow.CODIGO,
        requestType: epiRow.TIPOREQUISICAO,
        requestId: epiRow.IDREQUISICAO,
        epiId: epiRow.IDEPI,
        socManagerCode: epiRow.CODIGOGESTORSOC,
        rejectionReason: status !== "A" && status !== true ? reason : "",
        status: status as boolean,
      };

      response = await HRService.approvalReprovalDocumentEPI(params);
    } else {
      const documentId =
        rowData.Codigo || (rowData as IHR.IHRLearningManagement).documentNumber;

      response = await HRService.approvalReprovalDocument(
        documentId,
        process || "",
        status as "A" | "R" | boolean,
        reason
      );
    }

    return {
      data: response.data,
      message: "",
      success: response.success,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<HRDocument[]>> {
    try {
      if (!this.document.process) {
        return {
          data: [],
          message: "Process type is required",
          success: false,
        };
      }

      const response = await HRService.getPendingApprovals(
        this.document.process
      );

      return {
        data: response.data,
        message: "",
        success: response.success,
      };
    } catch (error) {
      console.error("Error getting approvals:", error);
      return {
        data: [],
        message: "Error getting approvals",
        success: false,
      };
    }
  }
}
