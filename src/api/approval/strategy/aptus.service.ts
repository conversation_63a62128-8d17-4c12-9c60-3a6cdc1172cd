import { api } from "@/api/api";
import { IAptus } from "@/interfaces";
import { AptusProcessType, AptusService } from "@/services/aptus";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AptusApprovalService extends BaseApprovalService<IAptus.IAptusBaseDocument> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IAptus.IAptusBaseDocument>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: IAptus.ApprovalReprovalParams = {
      codigo: rowData.Codigo,
      isApprove: status === "1",
      processType: process as AptusProcessType,
      comentary: reason,
    };

    const response = await AptusService.approvalReprovalDocument(params);
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<
    IApprovalResponse<IAptus.IAptusBaseDocument[]>
  > {
    const response = await api.get<
      IApprovalResponse<IAptus.IAptusBaseDocument[]>
    >("/approvals");
    return {
      success: true,
      message: "",
      data: response?.data?.data || [],
    };
  }
}
