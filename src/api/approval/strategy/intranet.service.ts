import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { IntranetService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class IntranetApprovalService extends BaseApprovalService<IIntranet.ItemProps> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IIntranet.ItemProps>
  > {
    const { rowData, status, reason, process } = this.document;

    const params = {
      docId: rowData.CODIGO,
      status: status as "A" | "R",
      comments: reason,
    };

    const response = await IntranetService.approvalReprovalDocument(
      params,
      process as IIntranet.ProcessKey
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<IIntranet.ItemProps[]>> {
    const response = await api.get<IApprovalResponse<IIntranet.ItemProps[]>[]>(
      "/approvals"
    );
    return {
      success: true,
      message: "",
      data: response.data[0]?.data || [],
    };
  }
}
