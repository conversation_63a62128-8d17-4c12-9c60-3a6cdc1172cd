import { APIOnelog } from "@/api";
import { IOnelog } from "@/interfaces";
import { handleApiError, OneLogService } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class OnelogApprovalService extends BaseApprovalService<IOnelog.ClientProps> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async getApprovals(): Promise<IApprovalResponse<IOnelog.ClientProps[]>> {
    try {
      const response = await APIOnelog.getDocuments(true);

      if (!response.success || !response.data) {
        return {
          success: false,
          message: "Failed to get OneLog approvals",
          data: {
            header: [],
            detail: [],
          },
        };
      }

      // Transformar a estrutura de clientes/aprovações em uma lista plana de aprovações
      const approvals = response.data.flatMap((client) =>
        client.aprovacoes.map((aprovacao) => ({
          ...aprovacao,
          clienteName: client.nome,
        }))
      );

      return {
        success: true,
        message: "",
        data: {
          header: approvals,
          detail: [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "ONELOG");
      return {
        success: false,
        message: "Error getting OneLog approvals",
        data: {
          header: [],
          detail: [],
        },
      };
    }
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IOnelog.ClientProps>
  > {
    const { rowData, status, reason, process } = this.document;

    try {
      const params = {
        status: status as "A" | "R",
        id: rowData.id,
        reason,
      };

      const response = await OneLogService.approvalReprovalDocument(params);

      return {
        success: response.success,
        message: response.message || "",
        data: {
          header: response?.data?.header || [],
          detail: response?.data?.detail || [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "ONELOG");
      return {
        success: false,
        message: "Error approving/reproving OneLog document",
        data: {
          header: [],
          detail: [],
        },
      };
    }
  }
}
