import { api } from "@/api/api";
import { IAriba } from "@/interfaces";
import { AribaService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AribaApprovalService extends BaseApprovalService<IAriba.AribaItemType> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IAriba.AribaItemType>
  > {
    const { rowData, status, reason, process } = this.document;
    const aribaParams = {
      itemCode: rowData.task,
      action: status as string,
      comment: reason || "",
      aribaUser: rowData.aribaUser || "",
      typeProcess: process || "",
    };

    const response = await AribaService.approvalReprovalDocument(aribaParams);
    return {
      // ...response,
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<IAriba.AribaItemType[]>> {
    const response = await api.get<IApprovalResponse<IAriba.AribaItemType[]>>(
      "/approvals"
    );
    return {
      success: true,
      message: "",
      data: response?.data?.data || [],
    };
  }
}
