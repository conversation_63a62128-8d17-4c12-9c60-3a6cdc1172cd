export interface IApprovalRequest {
  documentId: string;
  reason: string;
}

export interface IApprovalResponse<T> {
  success: boolean;
  message: string;
  data: {
    header: T[];
    detail: T[];
  };
}

export abstract class BaseApprovalService<T> {
  protected constructor() {}

  abstract approveReproveDocument(): Promise<IApprovalResponse<T>>;

  abstract getApprovals(): Promise<IApprovalResponse<T[]>>;
}
