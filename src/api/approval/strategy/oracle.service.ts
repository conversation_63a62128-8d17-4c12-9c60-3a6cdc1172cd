import { oracleTypeRoute } from "@/api/routes/oracle";
import { I<PERSON><PERSON>le } from "@/interfaces";
import { OracleService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class OracleApprovalService extends BaseApprovalService<IOracle.OracleItemType> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<IOracle.OracleItemType>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: IOracle.ApprovalReprovalParams = {
      id: rowData.id,
      statusCode: status as "APPROVED" | "REJECTED",
      obs: reason,
      email: rowData?.email || "",
      type: oracleTypeRoute[process || ""] as IOracle.OracleTypes,
    };

    const response = await OracleService.approvalReprovalDocument(params);

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response.success,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<IOracle.OracleItemType[]>> {
    try {
      if (!this.document.process) {
        return {
          data: [],
          message: "Process type is required",
          success: false,
        };
      }

      const response = await OracleService.getPendingApprovals(
        this.document.process
      );

      if (!response.success || !response.data) {
        return {
          data: [],
          message: "Error fetching pending approvals",
          success: false,
        };
      }

      const propertyName = oracleTypeRoute[this.document.process];
      const data = response.data[propertyName] as IOracle.OracleItemType[];

      return {
        data,
        message: "",
        success: true,
      };
    } catch (error) {
      console.error("Error getting approvals:", error);
      return {
        data: [],
        message: "Error getting approvals",
        success: false,
      };
    }
  }
}
