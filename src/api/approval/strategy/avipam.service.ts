import { APIAvipam } from "@/api";
import { IAvipam } from "@/interfaces";
import { AvipamItem } from "@/interfaces/avipam";
import { AvipamService, handleApiError } from "@/services";
import { t } from "i18next";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class AvipamApprovalService extends BaseApprovalService<AvipamItem> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async getApprovals(): Promise<IApprovalResponse<AvipamItem[]>> {
    try {
      const response = await APIAvipam.getPendingApprovals(true);
      return {
        success: response.success,
        message: "",
        data: {
          header: response.data.header || [],
          detail: [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "AVIPAM");
      return {
        success: false,
        message: "Error getting AVIPAM approvals",
        data: {
          header: [],
          detail: [],
        },
      };
    }
  }

  async approveReproveDocument(): Promise<IApprovalResponse<AvipamItem>> {
    try {
      const { rowData, status, reason, process } = this.document;

      const params: IAvipam.ApprovalReprovalParams = {
        orderNumber: rowData.orderNumber,
        status: status as "A" | "R",
        reason,
      };

      const response = await AvipamService.approvalReprovalDocument(params);

      console.log(response);

      return {
        success: response.success,
        message: response?.message || "",
        data: {
          header: response?.data?.header || [],
          detail: response?.data?.detail || [],
        },
      };
    } catch (error) {
      handleApiError(error, t, "AVIPAM");
      return {
        success: false,
        message: "Error approving/reproving AVIPAM document",
        data: {
          header: [],
          detail: [],
        },
      };
    }
  }
}
