import { api } from "@/api/api";
import { ICommercial } from "@/interfaces";
import { CommercialService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class CommercialApprovalService extends BaseApprovalService<ICommercial.ICommercialProps> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<ICommercial.ICommercialProps>
  > {
    const { rowData, status, reason, process } = this.document;

    const params: ICommercial.IGCCParams | ICommercial.IPreCapParams =
      process === "GCC"
        ? {
            params: {
              comment: reason || "",
              status: status as string,
            },
            documentCode: rowData.id,
          }
        : {
            preCapNumber: rowData.preCapNumber,
            comment: reason || "",
            operation: status as string,
          };

    const response = await CommercialService.approvalReprovalDocument(
      process || "",
      params
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<
    IApprovalResponse<ICommercial.ICommercialProps[]>
  > {
    const response = await api.get<
      IApprovalResponse<ICommercial.ICommercialProps[]>
    >("/approvals");
    return {
      success: true,
      message: "",
      data: response?.data?.data || [],
    };
  }
}
