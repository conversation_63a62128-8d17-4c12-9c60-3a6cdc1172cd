import { APIProcurement } from "@/api";
import { IResponse } from "@/interfaces/IResponse";
import {
  IProcurement,
  IProcurementApprovalReprovalParams,
} from "@/interfaces/procurement";
import { ProcurementService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class ProcurementApprovalService extends BaseApprovalService<IProcurement> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<IApprovalResponse<IProcurement>> {
    const { rowData, status, reason, process } = this.document;

    const params: IProcurementApprovalReprovalParams = {
      documentNumber: String(
        rowData?.numeroSolicitacao ?? rowData?.id ?? rowData?.Documento
      ),
      requestType: rowData?.tipoSolicitacao ?? rowData.requestType,
      operation: status as 1 | 0,
      comment: reason,
      userInitials: rowData?.initials,
    };

    const response = await ProcurementService.approvalReprovalDocument(
      params,
      process || ""
    );

    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response.message ?? "",
      success: response.success,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<IProcurement[]>> {
    try {
      if (!this.document.process) {
        return {
          data: [],
          message: "Process type is required",
          success: false,
        };
      }

      const response = await APIProcurement.getPendingApprovals(
        this.document.process
      );

      return {
        data: response.data,
        message: "",
        success: response.success,
      };
    } catch (error) {
      console.error("Error getting approvals:", error);
      return {
        data: [],
        message: "Error getting approvals",
        success: false,
      };
    }
  }

  async getDetails(documentNumber: string): Promise<IResponse<IProcurement>> {
    try {
      if (!documentNumber) {
        return {
          data: {} as IProcurement,
          success: false,
        };
      }

      const response = await APIProcurement.getCircularizationDetail(
        parseInt(documentNumber)
      );

      return {
        data: response.data[0] as unknown as IProcurement,
        success: response.success,
      };
    } catch (error) {
      console.error("Error getting document details:", error);
      return {
        data: {} as IProcurement,
        success: false,
      };
    }
  }
}
