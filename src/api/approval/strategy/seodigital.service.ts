import { api } from "@/api/api";
import { ISeoDigital } from "@/interfaces";
import { SeoDigitalService } from "@/services";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class SeoDigitalApprovalService extends BaseApprovalService<ISeoDigital.ISeoDigitalBase> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<
    IApprovalResponse<ISeoDigital.ISeoDigitalBase>
  > {
    const { rowData, status, reason, process } = this.document;

    const seoDigitalParams = {
      processCode: rowData.id,
      approvalManagerId: rowData.approvalManagerId,
      typeOfApproval: rowData.statusApproval === "S" ? 2 : 1,
      status: status as "A" | "R",
      reason,
    };

    const response = await SeoDigitalService.approvalReprovalDocument(
      seoDigitalParams
    );
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response?.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<
    IApprovalResponse<ISeoDigital.ISeoDigitalBase[]>
  > {
    const response = await api.get<
      IApprovalResponse<ISeoDigital.ISeoDigitalBase[]>[]
    >("/approvals");
    return {
      success: true,
      message: "",
      data: response.data[0]?.data || [],
    };
  }
}
