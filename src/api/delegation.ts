import { api } from "@/api/api";
import * as routes from "@/api/routes/delegation";
import { IDelegation, IResponse } from "../interfaces";

import { getLocalStorageItem } from "@/utils/storage";

export const DelegationAPI = {
  // POST create/record a delegation
  recordDelegation: (
    params: IDelegation.DelegationParams
  ): Promise<IResponse.Default<string>> => {
    return api.put(routes.recordDelegation, null, {
      params,
    });
  },
  // POST cancel an existing delegation
  cancelDelegation: (
    params: IDelegation.DelegationParams
  ): Promise<IResponse.Default<string>> => {
    return api.put(routes.cancelDelegation, null, { params });
  },

  // GET lists
  listDelegatedPowers: async (
    params?: IDelegation.DelegationParams
  ): Promise<IResponse.Default<IDelegation.DelegationProps[]>> => {
    try {
      const { data, status } = await api.get<IDelegation.DelegationResponse[]>(
        routes.listDelegatedPowers +
          "?" +
          new URLSearchParams(params as Record<string, string>).toString(),
        {
          headers: {
            Authorization: `Bearer ${
              getLocalStorageItem("employeeToken") || ""
            }`,
          },
        }
      );

      if (status === 200) {
        if (data[0].returnValues.length) {
          return {
            data: data[0].returnValues,
            success: true,
            message: data[0].message,
          };
        } else {
          return {
            data: [],
            success: false,
            message: data[0].message,
          };
        }
      }

      return {
        data: [],
        success: false,
        message: data[0].message,
      };
    } catch (error) {
      console.error("Error fetching delegated powers:", error);
      return {
        data: [],
        success: false,
        message: "Error fetching delegated powers",
      };
    }
  },

  listDelegatedPowersToUser: async (
    params: IDelegation.DelegationParams
  ): Promise<IResponse.Default<IDelegation.DelegationProps[]>> => {
    try {
      const { data, status } = await api.get<IDelegation.DelegationResponse[]>(
        routes.listDelegatedPowersToUser +
          "?" +
          new URLSearchParams(params as Record<string, string>).toString(),
        {
          headers: {
            Authorization: `Bearer ${
              getLocalStorageItem("employeeToken") || ""
            }`,
          },
        }
      );

      if (status === 200) {
        if (data[0].returnValues.length) {
          return {
            data: data[0].returnValues,
            success: true,
            message: data[0].message,
          };
        } else {
          return {
            data: [],
            success: false,
            message: data[0].message,
          };
        }
      }

      return {
        data: [],
        success: false,
        message: data[0].message,
      };
    } catch (error) {
      console.error("Error fetching delegated powers:", error);
      return {
        data: [],
        success: false,
        message: "Error fetching delegated powers",
      };
    }
  },
};
