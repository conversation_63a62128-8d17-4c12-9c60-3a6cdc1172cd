import { handleApiError } from "@/services";
import { t } from "i18next";
import { IResponse, IServiceNow } from "../interfaces";
import { getLocalStorageItem } from "../utils/storage";
import { api, objectCatch } from "./api";
import { RouteServiceNow } from "./routes";

export const getPendingApprovals = async (
  cache = true
): Promise<IResponse.Default<IServiceNow.ItemProps[]>> => {
  try {
    const { data, status } = await api.get(RouteServiceNow.getDocuments, {
      params: { cache },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return { data, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "Service Now");
    return { data: [], success: false };
  }
};

export const getDocumentDetails = async (
  documentNumber: string
): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.get(
      RouteServiceNow.getDocumentDetails(documentNumber),
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch {
    return { ...objectCatch, data: "" };
  }
};

export const approvalReprovalDocument = async (
  params: IServiceNow.ApprovalReprovalParams
): Promise<IResponse.Default<IServiceNow.ApprovalReprovalResponse>> => {
  try {
    const { data, status } = await api.put(
      RouteServiceNow.approvalReprovalDocument(
        params.registerId,
        params.status,
        params.comment
      )
    );

    return { data, success: status === 200 };
  } catch {
    return { ...objectCatch, data: {} as IServiceNow.ApprovalReprovalResponse };
  }
};

export const reprovalDocument = async (
  params: IServiceNow.ApprovalReprovalParams
): Promise<IResponse.Default<IServiceNow.ApprovalReprovalResponse>> => {
  try {
    const url = params.comment
      ? `${RouteServiceNow.approvalReprovalDocument(
          params.registerId,
          "R"
        )}?comment=${params.comment}`
      : RouteServiceNow.approvalReprovalDocument(params.registerId, "R");

    const { data, status } = await api.put(url, params, {
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return { data, success: status === 200 };
  } catch {
    return { ...objectCatch, data: {} as IServiceNow.ApprovalReprovalResponse };
  }
};
