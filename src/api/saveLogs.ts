import { api } from "./api";

interface LogErrorProps {
  statusCode?: number;
  method?: string;
  requestPath?: string;
  message?: string;
  stackTrace?: string;
  queryString?: string;
  innerMessage?: string;
  accountId?: string;
  process?: string | null;
  errorCode?: string;
}

const baseUrl = `${import.meta.env.VITE_ANALYTICS}`;

export const saveLogException = (params: LogErrorProps): void => {
  api.post(`${baseUrl}/LogException`, params, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("employeeToken")}`,
    },
  });
};
