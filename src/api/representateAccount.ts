import { IResponse } from "../interfaces";
import { api, arrayCatch } from "./api";
import { RouteRepresentateAccount } from "./routes";

export const representateAccount = async ({
  accountIdRepresentate,
  accountIdOriginal,
}: {
  accountIdRepresentate: number;
  accountIdOriginal: number;
}): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.post(
      RouteRepresentateAccount.representateAccount,
      { accountIdRepresentate, accountIdOriginal }
    );

    return {
      data,
      success: status === 200,
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    return { ...arrayCatch };
  }
};
