import { getLocalStorageItem } from "@/utils/storage";
import { IAriba, IResponse } from "../interfaces";
import { api } from "./api";
import { RouteAriba } from "./routes";

export const getPendingApprovals = async (
  cache = false
): Promise<IResponse.Default<IAriba.PendingApprovalsProps>> => {
  try {
    const { data, status } = await api.get(RouteAriba.getDocuments, {
      params: { cache },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return { data, success: status === 200 };
  } catch {
    return { data: { total: 0 }, success: false };
  }
};

export const approvalReprovalDocument = async (params: {
  itemCode: string;
  action: string;
  comment: string;
  aribaUser: string;
  typeProcess: string;
}): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.post(
      RouteAriba.approvalReprovalDocument,
      params,
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    return { data: null, success: false };
  }
};

export const downloadAttachment = async (params: {
  entity_id: string;
  entity_type: string;
  attachment_id: string;
}): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.get(RouteAriba.downloadAttachment, {
      params,
    });

    return { data, success: status === 200 };
  } catch (error) {
    return { data: null, success: false };
  }
};
