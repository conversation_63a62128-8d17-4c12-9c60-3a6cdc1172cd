import axios, { AxiosError } from "axios";

import { IResponse } from "../interfaces";
import { devEnv } from "./logException";
import { saveLogException } from "./saveLogs";

export const api = axios.create();

const getUrlParams = (url: string | undefined) => {
  try {
    return url ? new URL(url) : null;
  } catch {
    return null;
  }
};

const handleError = (error: AxiosError, type: "Request" | "Response") => {
  const url = error?.request?.responseURL;
  if (!url) return;

  const urlParams = getUrlParams(url);
  if (!devEnv && urlParams) {
    saveLogException({
      statusCode: error?.status,
      method: error?.config?.method?.toUpperCase(),
      requestPath: urlParams.pathname,
      message: `${type}: ${(error.response?.data as any)?.message}`,
      stackTrace: error?.request?.responseText,
      queryString: urlParams.search,
      // process: findProcessAcronymUrl(url),
    });
  }
};

// api.interceptors.request.use(
//   (req) => {
//     req.paramsSerializer = (params) =>
//       qs.stringify(params, { arrayFormat: "comma" });
//     return req;
//   },
//   (e) => {
//     handleError(e, "Request");
//     return Promise.reject(e);
//   }
// );

api?.interceptors.response.use(
  (res) => {
    if (res.status >= 300 && res.status < 400) {
      const url = res.request?.responseURL;
      const urlParams = getUrlParams(url);

      if (!devEnv && urlParams) {
        saveLogException({
          statusCode: res.status,
          method: res.config?.method?.toUpperCase(),
          requestPath: urlParams.pathname,
          message: `Warning: ${res.data?.message}`,
          stackTrace: res.request?.responseText,
          queryString: urlParams.search,
          // process: findProcessAcronymUrl(url),
        });
      }
    }
    return res;
  },
  (e) => {
    return Promise.reject(e);
  }
);

// Interceptor to add authentication token to all requests
// api.interceptors.request.use(
//   (config) => {
//     // Get token from localStorage or other storage
//     const token = localStorage.getItem("employeeToken");

//     console.log(token, "token");

//     // If token exists, add it to the Authorization header
//     if (token) {
//       config.headers = {
//         ...config.headers,
//         Authorization: `Bearer ${token}`,
//         apiKey: `${import.meta.env.VITE_API_KEY}`,
//       } as any;
//     }

//     return config;
//   },
//   (error) => {
//     handleError(error, "Request");
//     return Promise.reject(error);
//   }
// );

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("employeeToken");

    if (token) {
      config.headers = {
        ...config.headers,
        "Ocp-Apim-Subscription-Key": import.meta.env.VITE_APIM_SUBSCRIPTION_KEY,
      } as any;
    }

    return config;
  },
  (error) => {
    handleError(error, "Request");
    return Promise.reject(error);
  }
);

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("employeeToken");

    if ((config.method === "post" || config.method === "put") && token) {
      config.headers = {
        ...config.headers,
        authorization: `Bearer ${token}`,
      } as any;
    }

    return config;
  },
  (error) => {
    handleError(error, "Request");
    return Promise.reject(error);
  }
);

const objectCatch: IResponse.Default<undefined> = {
  data: undefined,
  success: false,
  message: "Falha na requisição, tente novamente.",
};

const arrayCatch: IResponse.Default<[]> = {
  data: [],
  success: false,
  message: "Falha na requisição, tente novamente.",
};

export { arrayCatch, objectCatch };
