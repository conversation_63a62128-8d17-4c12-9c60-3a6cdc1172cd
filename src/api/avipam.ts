import { IResponse } from "@/interfaces/IResponse";
import { handleApiError } from "@/services";
import { t } from "i18next";
import { ApprovalReprovalParams, AvipamItem } from "../interfaces/avipam";
import { getLocalStorageItem } from "../utils/storage";
import { api } from "./api";
import * as routes from "./routes/avipam";

export const getPendingApprovals = async (
  cache: boolean = true
): Promise<IResponse<AvipamItem[]>> => {
  try {
    const { data, status } = await api.get(routes.getPendingApprovals(), {
      params: { cache },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return {
      data,
      success: status === 200,
    };
  } catch (error) {
    handleApiError(error, t, `AVIPAM`.trim());
    return {
      data: [],
      success: false,
    };
  }
};

export const getDocumentDetails = async (orderNumber: string): Promise<any> => {
  try {
    const { data, status } = await api.get(
      routes.getDocumentDetails(orderNumber)
    );

    return { data: data as AvipamItem, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, `AVIPAM`.trim());
    return { data: {} as AvipamItem, success: false };
  }
};

export const approvalReprovalDocument = async (
  params: ApprovalReprovalParams
): Promise<any> => {
  try {
    const { data, status } = await api.put(
      routes.approvalReprovalDocument(
        params.status,
        params.orderNumber,
        params.reason
      )
    );

    return { data, success: status === 200 };
  } catch (error) {
    return {
      data: [],
      success: false,
    };
  }
};
