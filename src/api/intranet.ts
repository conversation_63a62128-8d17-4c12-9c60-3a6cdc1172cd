// import { handleApiError } from "@/services";
import { AxiosError } from "axios";
import { IIntranet, IResponse } from "../interfaces";
import {
  ApprovalReprovalParams,
  ItemProps,
  ProcessKey,
} from "../interfaces/intranet";
import { getLocalStorageItem } from "../utils/storage";
import { api } from "./api";
import * as routes from "./routes/intranet";

export const getPendingApprovals = async (
  process: IIntranet.ProcessKey,
  cache: boolean = true
): Promise<IResponse.Default<IIntranet.IntranetProps[]>> => {
  try {
    const { data } = await api.get(routes.getPendingApprovals(process), {
      params: { cache },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return {
      data,
      success: data.success,
    };
  } catch (error) {
    // handleApiError(error, t, `Intranet ${process || ""}`.trim());

    return {
      success: false,
      data: [],
      message: "",
    };
  }
};

export const getDocumentDetails = async (
  documentNumber: string,
  process: IIntranet.ProcessKey,
  language?: string
): Promise<IResponse.Default<ItemProps[]>> => {
  try {
    const { data } = await api.get(
      routes.getDocumentDetails(documentNumber, process, language)
    );

    return {
      success: data.success,
      data: data.retorno || [],
      message: "",
    };
  } catch (error) {
    const err = error as AxiosError;
    return {
      success: false,
      data: [],
      message: err.message,
    };
  }
};

export const approvalReprovalDocument = async (
  params: ApprovalReprovalParams,
  processType: ProcessKey
): Promise<IResponse.Default<string[]>> => {
  try {
    const { data } = await api.post(
      routes.approvalReprovalDocument(params, processType)
    );

    return {
      success: data.success,
      data: data.MSG || [],
    };
  } catch (error) {
    const err = error as AxiosError;
    return {
      success: false,
      data: [],
      message: err.message,
    };
  }
};
