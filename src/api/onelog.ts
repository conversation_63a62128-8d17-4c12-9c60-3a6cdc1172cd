import { IResponse } from "../interfaces/IResponse";
import {
  ApprovalReprovalResponse,
  ClientProps,
  ItemDetailProps,
} from "../interfaces/onelog";
import { getLocalStorageItem } from "../utils/storage";
import { api, objectCatch } from "./api";
import * as RouteOnelog from "./routes/onelog";

export const getPendingApprovals = async (
  cache: boolean = true
): Promise<IResponse<ClientProps[]>> => {
  try {
    const { data, status } = await api.get(RouteOnelog.getDocuments, {
      params: { cache },
      headers: {
        Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
      },
    });

    return { data: data.data.clientes, success: status === 200 };
  } catch (error) {
    return { ...objectCatch, data: {} as ClientProps[] };
  }
};

export const getDocumentDetails = async (
  documentNumber: string
): Promise<IResponse<ItemDetailProps>> => {
  try {
    const { data, status } = await api.get(
      RouteOnelog.getDocumentDetails(documentNumber),
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data: data.data, success: status === 200 };
  } catch (error) {
    return { ...objectCatch, data: {} as ItemDetailProps };
  }
};

export const approvalReprovalDocument = async (
  statusReq: "A" | "R",
  id: string,
  employeeid: string,
  reason?: string
): Promise<IResponse<ApprovalReprovalResponse>> => {
  try {
    const { data } = await api.put(
      RouteOnelog.approvalReprovalDocument(statusReq),
      {},
      {
        params: { id, employeeid, reason },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data: data.data, success: data.data.success };
  } catch (e) {
    return {
      ...objectCatch,
      data: {} as ApprovalReprovalResponse,
    };
  }
};

export const approvalReprovalDetailDocument = async (
  statusReq: "A" | "R",
  id: string,
  motivo: string,
  subMotivo: string,
  employeeid: string,
  reason?: string
): Promise<IResponse<ApprovalReprovalResponse>> => {
  try {
    const { data } = await api.put(
      RouteOnelog.approvalReprovalDetailDocument(statusReq),
      {},
      {
        params: { id, employeeid, motivo, subMotivo, reason },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data: data.data, success: data.data.success };
  } catch (e) {
    return {
      ...objectCatch,
      data: {} as ApprovalReprovalResponse,
    };
  }
};
