import { ICommercial, IResponse } from "../interfaces";
import { getLocalStorageItem } from "../utils/storage";
import { api, objectCatch } from "./api";
import { RouteCommercial } from "./routes";

export const pendingApprovals = async (
  process: string,
  cache = false
): Promise<IResponse.Default<ICommercial.ICommercialProps[]>> => {
  try {
    const { data, status } = await api.get(
      RouteCommercial.getDocuments(process),
      {
        params: { cache },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return {
      data: process === "GCC" ? data.approvalProcessInstances : data,
      success: status === 200,
    };
  } catch {
    return { ...objectCatch, data: [] as ICommercial.ICommercialProps[] };
  }
};

export const approvalReprovalDocumentGcc = async ({
  params,
  documentCode,
}: ICommercial.IGCCParams): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.put(
      RouteCommercial.approvalReprovalDocumentGcc(documentCode),
      {
        params,
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    return { ...objectCatch, data: null };
  }
};

export const approvalReprovalDocumentSales = async ({
  preCapNumber,
  comment,
  operation,
}: ICommercial.IPreCapParams): Promise<IResponse.Default<any>> => {
  try {
    const { data, status } = await api.put(
      RouteCommercial.approvalReprovalDocumentSales,
      {
        preCapNumber,
        comment,
        operation,
      }
    );

    return { data, success: status === 200 };
  } catch (error) {
    return { ...objectCatch, data: null };
  }
};
