import { saveLogException } from "./saveLogs";

interface LogExceptionParams {
  statusCode: number | undefined;
  method: string | undefined;
  requestPath: string;
  message: string;
  stackTrace: string | undefined;
  queryString: string | undefined;
  process: string | null;
}

export const devEnv = import.meta.env.VITE_ENVIROMENT === "DEV";

export const logException = ({
  statusCode,
  method,
  requestPath,
  message,
  stackTrace,
  queryString,
  process,
}: LogExceptionParams) => {
  if (!devEnv) {
    saveLogException({
      statusCode,
      method,
      requestPath,
      message,
      stackTrace,
      queryString,
      process,
    });
  }
};
