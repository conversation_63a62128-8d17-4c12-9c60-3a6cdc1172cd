// import Notiflix from 'notiflix';

// const handleApiError = (
//   error: any,
//   translation: (message: string) => string,
//   context: string,
// ) => {
//   let err = { title: '', subtitle: '', cod: '', type: '' };
//   if (error.request) {
//     if (error.request.status === 401) {
//       err = {
//         ...err,
//         title: translation('Unauthorized.refresh.the.screen.and.try.again.'),
//         type: 'failure',
//       };
//       Notiflix.Notify.Failure(
//         translation('Unauthorized.refresh.the.screen.and.try.again.'),
//       );
//     } else {
//       err = {
//         ...err,
//         title: translation('Oops.Something.went.wrong.Try.again.later.'),
//         type: 'failure',
//       };
//       Notiflix.Notify.Failure(
//         translation('Oops.Something.went.wrong.Try.again.later.'),
//       );
//     }
//   } else {
//     err = {
//       title: `${translation('It.was.not.possible.to.connect.with')} ${context}`,
//       subtitle: translation('Try.again.later'),
//       cod: `${error.response?.data?.InnerMessage?.slice(-12)}`,
//       type: 'failure',
//     };
//     Notiflix.Notify.Failure(
//       `<strong>${translation(
//         'It.was.not.possible.to.connect.with',
//       )} ${context}</strong></br>
//         <span>${translation('Try.again.later')}</span></br>
//         Cod: ...${error.response?.data?.InnerMessage?.slice(-12)}`,
//     );
//   }
//   return { status: false, err };
// };

// export default handleApiError;
