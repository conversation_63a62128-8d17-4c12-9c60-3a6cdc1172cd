export function getUniqueDocs(
  array: {
    documento?: string;
    Codigo?: string;
    CODIGO?: string;
    id?: string;
  }[]
): string[] {
  if (!array) return [];

  const documentsSet = new Set<string>();

  array.forEach((element) => {
    if (element.documento) documentsSet.add(element.documento);
    if (element.Codigo) documentsSet.add(element.Codigo);
    if (element.CODIGO) documentsSet.add(element.CODIGO);
    if (element.id) documentsSet.add(element.id);
  });

  return Array.from(documentsSet);
}

