/**
 * Converte a data: 20210108 para a escrita 08/01/2018
 * @param {string} date >>>> recebe a data: 20210108
 * @returns {string} dateRetorno >>> converte para: 08/01/2018
 */
export function dateBDtoRead(date: string): string {
  const ano = date?.substring(0, 4);
  const mes = date?.substring(4, 6);
  const dia = date?.substring(6, 8);
  return `${dia}/${mes}/${ano}`;
}

/**
 * Converte a data: July, 23 2018 00:00:00 para 23/07/2018
 * @param {string} date >>>> recebe a data: July, 23 2018 00:00:00
 * @param day >>>> indica se retorna o dia: true
 * @return {string} dateRetorno >>> converte para: 23/07/2018
 */
export function dateUStoBR(date: string, day = true): string {
  try {
    if (date.trim() === "") {
      return "";
    }
    const data = new Date(date);

    const dayValue = () => {
      if (day) {
        return `${data.getDate().toString().padStart(2, "0")}/`;
      } else return "";
    };

    return `${dayValue()}${(data.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${data.getFullYear().toString()}`;
  } catch (e) {
    return "";
  }
}

/**
 * Converte a data: 2021-01-08 para a escrita 08/01/2018
 * @param {string} date >>>> recebe a data: 20210108
 * @returns {string} dateRetorno >>> converte para: 08/01/2018
 */
export function dateBDtoReadWithSeparator(date: string): string {
  if (!date) return "-";
  const ano = date.substring(0, 4);
  const mes = date.substring(5, 7);
  const dia = date.substring(8, 10);
  return `${dia}/${mes}/${ano}`;
}

/**
 * Converte a data: 2021-02-26 17:05:33 para a escrita 08/01/2018
 * @param {string} date >>>> recebe a data: 2021-02-26 17:05:33
 * @returns {string} dateRetorno >>> converte para: 08/01/2018
 */
export function datetimeBDtoReadWithSeparator(
  date: any,
  showTime = false
): string {
  const td = date?.split(" ");

  const ano = td?.[0].substring(0, 4);
  const mes = td?.[0].substring(5, 7);
  const dia = td?.[0].substring(8, 10);
  let dataRetorno = `${dia}/${mes}/${ano}`;

  if (showTime) {
    const time = td?.[1].split(":");
    dataRetorno += ` ${time[0]}:${time[1]}`;
  }

  return dataRetorno;
}

/**
 * Converte a data: 01/02/2020 17:05:33 para a escrita 08/01/2018
 * @param {string} date >>>> recebe a data: 01/02/2020 17:05:33
 * @returns {string} dateRetorno >>> converte para: 2020-02-01
 */
export function parsePTBrToDB(date: string): string {
  const full = date.split(" ")[0];
  const dia = full.substring(0, 2);
  const mes = full.substring(3, 5);
  const ano = full.substring(6, 10);

  return `${ano}-${mes}-${dia}`;
}

/**
 * Converte a data: 20210201 para a escrita 2021-02-01
 * @param {string} date >>>> recebe a data: 20210201
 * @returns {string} dateRetorno >>> converte para: 2021-02-01
 */
export function parseDBtoDBSeparated(date: string): string {
  const ano = date.substring(0, 4);
  const mes = date.substring(4, 6);
  const dia = date.substring(6, 8);

  return `${ano}-${mes}-${dia}`;
}

/**
 * Retorna a data atual incluindo o tempo no formato de banco
 * @returns {string} Ex: 2021-12-30 14:52:36
 */
export function getDateWithTimeDBFormat(): string {
  const date = new Date().toLocaleString();
  const [day, month, year] = date.split(" ")[0].split("/");
  const time = date.split(" ")[1];

  return `${year}-${month}-${day} ${time}`;
}

export function mathDate(value: string): boolean {
  const spred = [...value];
  if (value?.length === 10 && spred[2] === "." && spred[5] === ".") {
    return true;
  }
  return false;
}
