
import axios from 'axios';

export async function getIp(): Promise<string> {
  try {
    const { data } = await axios.get<string>('https://l2.io/ip.js?var=userip');
    return data.replace('userip = "', '').replace('";', '');
  } catch (e) {
    try {
      const { data } = await axios.get(
        'https://api.ipify.org?format=jsonp&callback=?',
      );
      return data.replace('?({"ip":"', '').replace('"});', '');
    } catch (error) {
        const Err = error as unknown;
      throw new Error('it was not possible to get your ip for authentication');
    }
  }
}
