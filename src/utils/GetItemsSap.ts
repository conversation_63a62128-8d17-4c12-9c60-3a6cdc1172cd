/**
 * Busca e retorna o valor em string do campo solicitado para os adicionais do SAP
 * @param {string} value >>>> Recebe o nome do campo que precuramos o valor
 * @param {string} additionalArray >>>> Recebe o array onde vamos procurar o campo
 * @returns {string} field >>> O valor do campo encontrado
 */

import { ISap } from "@/interfaces";

/**
 * Busca e retorna o valor do campo solicitado nos adicionais do SAP
 * @param {string} value - Nome do campo que desejamos buscar
 * @param {AdicionalProps[]} additionalArray - Array onde vamos procurar o campo
 * @returns {string} - Valor do campo encontrado ou string vazia se não encontrado
 */
export function getAdditionalField(
  value: string,
  additionalArray?: ISap.AdicionalProps[]
): string {
  if (!additionalArray?.length || !value) {
    return "";
  }
  const field = additionalArray.find((item) => item.campo === value);
  return field?.valor !== undefined ? String(field.valor) : "";
}

export function getPartialWordFields(
  value: string,
  additionalArray?: ISap.AdicionalProps[]
): (string | number | undefined)[] {
  if (!additionalArray) {
    return [];
  }
  return additionalArray
    .filter((f) => f.campo?.toLowerCase()?.includes(value?.toLowerCase()))
    .map((f) => f.valor);
}
