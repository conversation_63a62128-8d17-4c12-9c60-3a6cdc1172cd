export const removeCard = (card: HTMLDivElement): void => {
  card.classList.add('removing');
  card.dataset.removed = 'true';
  window.setTimeout(() => {
    card.style.display = 'none';
  }, 500);
};

export const showCard = (card: HTMLDivElement): void => {
  if (card) {
    card.classList.remove('removing');
    card.dataset.removed = 'false';
    window.setTimeout(() => {
      card.style.display = 'block';
    }, 500);
  }
};

export const removeTr = (
  tableContainer: HTMLTableElement | null,
  ids: string[],
): void => {
  ids.forEach((id) => {
    try {
      const trs =
        tableContainer !== null
          ? tableContainer.querySelectorAll<HTMLTableRowElement>(id)
          : document.querySelectorAll<HTMLTableRowElement>(id);
      if (trs) {
        trs.forEach((tr) => {
          const subCardAriba = tr.parentNode?.parentNode?.parentNode;
          if ((subCardAriba as HTMLElement)?.classList.contains('ariba-AS')) {
            const trAS = tr.parentNode?.parentNode?.parentNode
              ?.parentNode as HTMLElement;

            let subCardCounter = trAS?.childNodes[0].childNodes[1]
              .childNodes[0] as any;

            subCardCounter = (subCardCounter as HTMLElement)?.getAttribute(
              'data-counter',
            );

            if (Number(subCardCounter) <= 1 && trAS) {
              trAS.dataset.removed = 'true';
              trAS.style.opacity = '0';
              window.setTimeout(() => {
                if (trAS) {
                  trAS.style.display = 'none';
                  trAS.style.height = '0';
                  trAS.style.visibility = 'hidden';
                  trAS.style.overflow = 'hidden';
                  trAS.innerHTML = '';
                }
              }, 300);
            }

            if (Number(subCardCounter) > 1 && trAS) {
              subCardCounter = Number(subCardCounter) - 1;
              subCardCounter.innerHTML = subCardCounter.toString();
            }
          }

          tr.dataset.removed = 'true';
          tr.style.opacity = '0';
          window.setTimeout(() => {
            tr.style.display = 'none';
            tr.style.height = '0';
            tr.style.visibility = 'hidden';
            tr.style.overflow = 'hidden';
            tr.innerHTML = '';
          }, 300);
        });
      }
    } catch (e) {
      // no have this tr
    }
  });
};

/**
 *
 * @param tableRef table element for search a children tr to remove
 * @param trChildrenId id of children tr (ex: 'tr#tr-123')
 * @returns boolean inform if has childrens visibles, if empry or hiddens childrens is return true for remove father TR
 */
export const removeTrChildren = (
  tableRef: HTMLTableElement,
  trChildrenId: string,
): boolean => {
  removeTr(tableRef, [trChildrenId]);
  const child = tableRef.querySelector<HTMLTableRowElement>(trChildrenId);
  if (child) {
    const childParents = child.parentElement?.querySelectorAll('tr');
    if (childParents) {
      return (
        [...childParents].filter((ch) => !ch.getAttribute('data-removed'))
          .length === 0
      );
    }
  }
  return false;
};
