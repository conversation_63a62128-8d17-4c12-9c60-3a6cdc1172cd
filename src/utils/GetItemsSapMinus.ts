/**
 * Busca e retorna o valor em string do campo solicitado para os adicionais do SAP
 * @param {string} value >>>> Recebe o nome do campo que precuramos o valor
 * @param {string} additionalArray >>>> Recebe o array onde vamos procurar o campo
 * @returns {string} field >>> O valor do campo encontrado
 */

interface AdicionalProps {
  chave: string;
  valor: string;
}

export function getAdditionalFieldMinus(
  value: string,
  additionalArray: AdicionalProps[]
): string {
  if (!additionalArray) {
    return "";
  }
  const field = additionalArray.filter((accField) => accField.chave === value);
  if (field[0]) return field[0].valor;
  return "";
}

