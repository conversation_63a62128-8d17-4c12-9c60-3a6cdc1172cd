import { IUser } from "../interfaces";

export default function handlePermissions(user: IUser, location?: string) {
  const usersSpecialPermissions = import.meta.env.VITE_PERMISSION_REPORT;

  if (location === "/" || (user.hasFilter && !user?.permissionsAllowed)) {
    return user.permissionsApproval;
  }

  if (location === "/admin" && !!user?.userReading) {
    if (usersSpecialPermissions?.includes(user.employeeID)) {
      return user.permissionsApproval;
    }
    return user?.permissionsAllowed;
  }
}

