const handleObjParams = (paramsArray: string[]): any => {
  let objParams = {};
  paramsArray.forEach((param: any) => {
    const [key, value] = param?.split('=');
    const entries = new Map([[key, value]]);

    objParams = { ...objParams, ...Object.fromEntries(entries) };
  });
  return objParams;
};

export const getParams = (): Record<string, string> => {
  const paramsArray = window.location.search.replace('?', '').split('&');
  return handleObjParams(paramsArray);
};

export const getParam = (key: string): string => {
  const params = getParams();
  return params[key];
};

export const getParamsOfEspecifcURL = (url: string): Record<string, string> => {
  const paramsArray = url.replace('?', '').split('&');
  return handleObjParams(paramsArray);
};
