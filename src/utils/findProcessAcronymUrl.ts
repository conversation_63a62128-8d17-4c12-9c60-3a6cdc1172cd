export default function findProcessAcronymUrl(url: string): string | undefined {
  try {
    const newUrl = new URL(url);

    console.log(url);
    let longestMatch = "";
    let acronym;

    Object.keys(processPathname).forEach((key) => {
      const value = processPathname[key];
      if (
        newUrl.pathname?.includes(value) &&
        value.length > longestMatch.length
      ) {
        longestMatch = value;
        acronym = key?.split("_")[0];
      }
    });
    return acronym;
  } catch {
    return undefined;
  }
}
