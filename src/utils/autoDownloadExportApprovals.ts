import dayjs from "dayjs";

import type { ExportApprovalsHistoryFormat } from "@/services/approvals-history";

interface CreateBlobForApprovalsExportsParams {
  data: any;
  exportFormat: ExportApprovalsHistoryFormat;
}

export function autoDownloadExportApprovals({ data, exportFormat }: CreateBlobForApprovalsExportsParams) {
  const url = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement('a');

  const currentDate = dayjs(new Date).format('YYYY-MM-DD')

  link.href = url;
  link.setAttribute('download', `approval-history-${currentDate}.${exportFormat}`);
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
}