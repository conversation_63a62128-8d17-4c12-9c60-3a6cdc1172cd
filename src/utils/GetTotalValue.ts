import React from 'react';

/**
 * recebe dos valores em String, converte e retorna o valor em String
 * @param {string} number1 >>>> recebe o valor completo: R$ 10.000,00
 * @param {string} number2 >>>> recebe o valor completo: 10.000,00
 * @param {string} operation >>>> implementador os básico + - * /
 * @returns {string} sum >>> Valor somado
 */
export function MathOperationWithStrings(
  number1: string,
  number2: string,
  operation: string,
) {
  const v01: string = number1.replace('R$', '');
  const v1: string = v01.replace('.', '');
  const v12 = parseFloat(v1.replace(',', '.'));

  const v02: string = number2.replace('R$', '');
  const v2: string = v02.replace('.', '');
  const v22 = parseFloat(v2.replace(',', '.'));

  const total: number = v12 * v22;

  return total;
}
