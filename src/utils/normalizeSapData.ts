import { ISap } from "@/interfaces";

interface SapDataWithDynamicProps extends ISap.ItemProps {
  [key: string]: unknown;
}

/**
 * Normaliza propriedades SAP que podem ter diferentes cases (maiúsculo/minúsculo)
 * <PERSON><PERSON> resolve problemas onde as propriedades esperadas na interface não coincidem
 * com as propriedades reais dos dados vindos da API
 */
export function normalizeSapItemProps(item: ISap.ItemProps): ISap.ItemProps {
  const itemWithExtras = item as SapDataWithDynamicProps;
  const normalized: SapDataWithDynamicProps = { ...item };

  // Lista de propriedades que podem ter inconsistências de case
  const propertiesToNormalize = [
    { lowercase: "centro", uppercase: "CENTRO" },
    { lowercase: "material", uppercase: "MATERIAL" },
    { lowercase: "qtd", uppercase: "QTD" },
    { lowercase: "fornecedor", uppercase: "FORNECEDOR" },
    { lowercase: "requisitante", uppercase: "REQUISITANTE" },
    { lowercase: "data_emissao", uppercase: "DATA_EMISSAO" },
    { lowercase: "documento", uppercase: "DOCUMENTO" },
    { lowercase: "item", uppercase: "ITEM" },
    { lowercase: "tipo", uppercase: "TIPO" },
    { lowercase: "vldoc", uppercase: "VLDOC" },
    { lowercase: "vlitem", uppercase: "VLITEM" },
    { lowercase: "vlunit", uppercase: "VLUNIT" },
  ];

  // Normalizar cada propriedade
  propertiesToNormalize.forEach(({ lowercase, uppercase }) => {
    // Se a propriedade minúscula não existe mas a maiúscula sim, copiar o valor
    if (!normalized[lowercase] && itemWithExtras[uppercase]) {
      normalized[lowercase] = String(itemWithExtras[uppercase]);
    }

    // Se a propriedade maiúscula não existe mas a minúscula sim, copiar o valor
    if (!itemWithExtras[uppercase] && normalized[lowercase]) {
      normalized[uppercase] = normalized[lowercase];
    }
  });

  return normalized as ISap.ItemProps;
}

/**
 * Normaliza um array de itens SAP
 */
export function normalizeSapItemArray(
  items: ISap.ItemProps[]
): ISap.ItemProps[] {
  return items.map(normalizeSapItemProps);
}
