import axios from "axios";
import MobileDetect from "./MobileDetect";
import { getParam } from "./URL";

export const handleTerminal = () => {
  if (getParam("from") === "ios") {
    return "IOS";
  }
  if (MobileDetect()) {
    return "Android";
  }
  return "Desktop";
};

export const refreshMiddleware = (employeeID: string) => {
  // Axios.interceptors.request.use(
  //   (config: { headers: any; url: string | string[] }) => {
  //     config.headers = {
  //       ...config.headers,
  //       apiKey,
  //     };
  //     if (!config.url?.includes(`employeeid`)) {
  //       if (config.url?.includes("?")) {
  //         config.url += `&employeeid=${employeeID}`;
  //       } else {
  //         config.url += `?employeeid=${employeeID}`;
  //       }
  //     }
  //     return config;
  //   },
  //   (error: any) => {
  //     return Promise.reject(error);
  //   }
  // );
};

export const setAxiosDefaults = (token: string, employeeID: string): void => {
  axios.defaults.headers.common.Authorization = `Bearer ${token}`;
  axios.defaults.headers.common.EmployeId = employeeID;
  axios.defaults.headers.common.apiKey = import.meta.env.VITE_API_KEY;
};

// const filterPermissions = (
//   permissionsApproval: IPermissionsApproval[],
//   permissionsReport: IPermissionsApproval[]
// ): IPermissionsApproval[] => {
//   const permissionsAllowed: IPermissionsApproval[] = [];
//   permissionsReport.forEach((report) => {
//     permissionsApproval.forEach((permission) => {
//       if (permission.process === report.process) {
//         permissionsAllowed.push(permission);
//       }
//     });
//   });
//   return permissionsAllowed;
// };
