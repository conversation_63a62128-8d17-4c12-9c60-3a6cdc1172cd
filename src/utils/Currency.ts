/**
 * Separa a moeda do valor em uma string 1 retorna a moeda, 2 retona o valor
 * @param {string} currencyAndValue >>>> recebe o valor completo: R$ 10.000,00
 * @param {number} position >>>> Qual posição do retorno 1 ou 2
 * @returns {string} valie >>> String com a moeda (R$) ou o valor (10.000,00)
 */
export function FullCurrencyAndValueSeparete(
  currencyAndValue: string,
  position: number
) {
  if (position === 1) {
    return currencyAndValue?.split(" ")[0];
  }

  return currencyAndValue?.split(" ")[1];
}
/**
 * Formata um valor no formato pt-BR
 * @param {string} textValue >>> valor a ser tratado
 * @return {string} value >>> valor formatado
 */
export function formatToBRLFloatAmount(textValue: string): string {
  if (textValue === "0") {
    return `0,${"0".padStart(2, "0")}`;
  }
  // eslint-disable-next-line radix

  const numberformated = parseFloat(textValue);
  return Number.isNaN(numberformated)
    ? ""
    : new Intl.NumberFormat("pt-BR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
        minimumIntegerDigits: 1,
      }).format(numberformated);
}

/**
 * Adicionar o separador de decimal em um número. ex. 12000 => 120,00
 * @param {string} textValue >>> valor a ser tratado
 * @param {string} separator >>> caractere usado como separador, por padrão é ,
 * @param {number} decimals >>> quantidade de casas decimais no número
 * @return {string} value >>> valor formatado
 */
export function putDecimalPoint(
  textValue: string,
  separator = ",",
  decimals = 2
) {
  try {
    if (textValue === "0") {
      return `0${separator}${"0".padStart(decimals, "0")}`;
    }

    const numero =
      decimals === 3 ? parseInt(textValue) / 1000 : parseInt(textValue) / 100;

    const formatNumber = () => {
      if (separator === ",") {
        return new Intl.NumberFormat("pt-BR", {
          minimumFractionDigits: decimals,
          minimumIntegerDigits: 1,
        }).format(numero);
      } else {
        return new Intl.NumberFormat("en", {
          minimumFractionDigits: decimals,
          minimumIntegerDigits: 1,
        }).format(numero);
      }
    };

    return isNaN(numero) ? "" : formatNumber();
  } catch (e) {
    console.log(e);
    return "";
  }
}

export function toPtBrCurrency(value: string) {
  let finalValue = "0";
  if (
    (value?.indexOf(".0") !== -1 && Number(value?.split(".")[1]) === 0) ||
    !value?.includes(".")
  ) {
    return Number(value)
      .toLocaleString("pt-BR", {
        maximumFractionDigits: 2,
      })
      .concat(",00");
  }
  if (value?.includes(".") && Number(value?.split(".")[1]) > 0) {
    finalValue = Number(value).toLocaleString("pt-BR", {
      maximumFractionDigits: 2,
    });

    if (
      Number(finalValue.split(",")[1]) > 0 &&
      finalValue.split(",")[1].length === 1
    ) {
      return Number(value)
        .toLocaleString("pt-BR", {
          maximumFractionDigits: 2,
        })
        .concat("0");
    }

    return Number(value).toLocaleString("pt-BR", {
      maximumFractionDigits: 2,
    });
  }
}

export function toPtBrCurrencyWithoutDecimal(value: string) {
  if (
    (value?.indexOf(".0") !== -1 && Number(value?.split(".")[1]) === 0) ||
    !value?.includes(".")
  ) {
    return Number(value).toLocaleString("pt-BR", {
      maximumFractionDigits: 2,
    });
  }
}
