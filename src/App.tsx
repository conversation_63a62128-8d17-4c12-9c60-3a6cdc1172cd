import { JSX, useEffect } from "react";

import { InteractionStatus } from "@azure/msal-browser";
import { useMsal } from "@azure/msal-react";
import { ToastContainer } from "react-toastify";
import "./App.css";
import { AuthError } from "./components";
import Loading from "./components/Loading";
import SessionExpiredModal from "./components/SessionExpiredModal";
import { useAuth } from "./hooks";
import AppRoutes from "./routes";

const App = (): JSX.Element => {
  const {
    isLoading,
    msalExpiration,
    isSessionExpired,
    setIsSessionExpired,
    showErrorModal,
  } = useAuth();

  const { inProgress } = useMsal();
  const isMsalLoading = inProgress !== InteractionStatus.None;
  const isAppLoading = isLoading || isMsalLoading;

  useEffect(() => {
    const expiresTime = () => {
      if (!msalExpiration) return 99999999;
      return new Date(msalExpiration).getTime() - new Date().getTime();
    };

    const timer = window.setTimeout(() => {
      localStorage.removeItem("msalToken");
      localStorage.removeItem("employeeToken");
      localStorage.removeItem("employeeid");
      setIsSessionExpired(true);
    }, expiresTime() - 120000); // 48 minutes (120000 ms) before expiry

    return () => {
      window.clearTimeout(timer);
      setIsSessionExpired(false);
    };
  }, [msalExpiration]);

  return (
    <>
      {showErrorModal && !isAppLoading ? (
        <AuthError />
      ) : isAppLoading ? (
        <Loading open={isAppLoading} />
      ) : (
        <AppRoutes />
      )}

      {!showErrorModal && isSessionExpired && !isAppLoading && (
        <SessionExpiredModal open={isSessionExpired} />
      )}

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        limit={5}
      />
    </>
  );
};

export default App;
