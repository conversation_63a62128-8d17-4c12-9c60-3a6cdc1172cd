import axios from "axios";
import api from "./api";

jest.mock("axios");

describe("API Interceptors", () => {
  it("should log exceptions on request error", async () => {
    const error = {
      request: { responseURL: "http://example.com" },
      response: { status: 500, data: { message: "Error" } },
    };
    axios.create = jest.fn(() => ({
      interceptors: {
        request: { use: jest.fn((_, reject) => reject(error)) },
        response: { use: jest.fn() },
      },
    }));
    await expect(api.get("/test")).rejects.toThrow();
  });
});
