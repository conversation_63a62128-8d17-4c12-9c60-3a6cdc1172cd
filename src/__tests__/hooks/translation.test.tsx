import i18n from "i18next";
import {
  initializeTranslations,
  loadAllLanguages,
  loadTranslations,
  translate,
  translationsCache,
} from "../../hooks/translation";
import { getLocalStorageItem } from "../../utils/storage";

// Mock das dependências
jest.mock("../../utils/storage", () => ({
  getLocalStorageItem: jest.fn(),
}));

jest.mock("../../api/index", () => ({
  APITranslation: {
    getTranslation: jest.fn(),
  },
  __esModule: true,
}));

// Mock do i18next
jest.mock("i18next", () => ({
  use: jest.fn().mockReturnThis(),
  init: jest.fn(),
  t: jest.fn((key) => `translated_${key}`),
  on: jest.fn(),
  addResourceBundle: jest.fn(),
}));

// Mock do react-i18next
jest.mock("react-i18next", () => ({
  initReactI18next: { type: "3rdParty" },
}));

describe("Translation Module", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Limpar o cache entre os testes
    Object.keys(translationsCache).forEach((key) => {
      delete translationsCache[key];
    });
  });

  describe("loadTranslations", () => {
    it("should load translations successfully", async () => {
      // Configurar o mock da API
      const mockAPIModule = await import("../../api/index");
      // Redefine o mock para a função
      (
        mockAPIModule.APITranslation.getTranslation as jest.Mock
      ).mockResolvedValue({
        success: true,
        data: [
          { key: "hello", value: "Hello" },
          { key: "world", value: "World" },
        ],
      });

      const result = await loadTranslations("en-US", "mock-token");

      expect(mockAPIModule.APITranslation.getTranslation).toHaveBeenCalledWith(
        "en-US",
        "mock-token"
      );
      expect(result).toEqual({
        hello: "Hello",
        world: "World",
      });
    });
  });

  it("should handle API error gracefully", async () => {
    // Configurar o mock da API para lançar erro
    const mockAPIModule = await import("../../api/index");
    // Redefine o mock para a função
    (
      mockAPIModule.APITranslation.getTranslation as jest.Mock
    ).mockRejectedValue(new Error("API Error"));

    // Espiar o console.error
    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    await expect(loadTranslations("en-US", "mock-token")).rejects.toThrow(
      "API Error"
    );
    expect(consoleErrorSpy).toHaveBeenCalled();

    // Restaurar console.error
    consoleErrorSpy.mockRestore();
  });
});

describe("loadAllLanguages", () => {
  it("should load translations for all languages", async () => {
    // Import the module directly to access the original function
    const mockLoadTranslations = jest
      .fn()
      .mockResolvedValueOnce({ key1: "value1-pt" })
      .mockResolvedValueOnce({ key1: "value1-es" })
      .mockResolvedValueOnce({ key1: "value1-en" });

    // Mock the module with jest.mock to mock the loadTranslations function
    jest.mock("../../hooks/translation", () => ({
      ...jest.requireActual("../../hooks/translation"),
      loadTranslations: mockLoadTranslations,
    }));

    // Re-import to use the mocked version
    const { loadAllLanguages } = require("../../hooks/translation");

    await loadAllLanguages(["pt-BR", "es-ES", "en-US"], "mock-token");

    expect(mockLoadTranslations).toHaveBeenCalledTimes(3);
    expect(mockLoadTranslations).toHaveBeenCalledWith("pt-BR", "mock-token");
    expect(mockLoadTranslations).toHaveBeenCalledWith("es-ES", "mock-token");
    expect(mockLoadTranslations).toHaveBeenCalledWith("en-US", "mock-token");
    expect(i18n.addResourceBundle).toHaveBeenCalledTimes(3);

    // Restore the original implementation by clearing the mock
    jest.resetModules();
  });

  it("should handle errors when loading languages", async () => {
    // Mock para loadTranslations que lança erro

    // Obter a implementação original antes de substituí-la
    const originalLoadTranslations =
      require("../../hooks/translation").loadTranslations;

    // Definir o mock para loadTranslations que lança erro
    const mockLoadTranslations = jest
      .fn()
      .mockRejectedValue(new Error("Mock Error"));
    require("../../hooks/translation").loadTranslations = mockLoadTranslations;

    // Espiar console.error
    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    await loadAllLanguages(["pt-BR"], "mock-token");

    expect(consoleErrorSpy).toHaveBeenCalled();
    expect(consoleErrorSpy.mock.calls[0][0]).toContain(
      "Erro ao buscar traduções para pt-BR:"
    );

    // Restaurar
    consoleErrorSpy.mockRestore();
    require("../../hooks/translation").loadTranslations =
      originalLoadTranslations;
  });
});

describe("initializeTranslations", () => {
  it("should initialize translations when token exists", async () => {
    // Mock getLocalStorageItem para retornar um token
    (getLocalStorageItem as jest.Mock).mockReturnValue("mock-token");

    // Mock loadAllLanguages
    const mockLoadAllLanguages = jest.fn().mockResolvedValue(undefined);
    const originalLoadAllLanguages =
      require("../../hooks/translation").loadAllLanguages;
    require("../../hooks/translation").loadAllLanguages = mockLoadAllLanguages;

    // Re-import to use the mocked function
    const {
      initializeTranslations: mockedInitializeTranslations,
    } = require("../../hooks/translation");
    await mockedInitializeTranslations();

    expect(getLocalStorageItem).toHaveBeenCalledWith("employeeToken");
    expect(mockLoadAllLanguages).toHaveBeenCalledWith(
      ["pt-BR", "es-ES", "en-US"],
      "mock-token"
    );

    // Restaurar
    require("../../hooks/translation").loadAllLanguages =
      originalLoadAllLanguages;
  });

  it("should not load translations when token is missing", async () => {
    // Mock getLocalStorageItem para retornar null
    (getLocalStorageItem as jest.Mock).mockReturnValue(null);

    // Espiar console.warn
    const consoleWarnSpy = jest.spyOn(console, "warn").mockImplementation();

    await initializeTranslations();

    expect(getLocalStorageItem).toHaveBeenCalledWith("employeeToken");
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      "Token não encontrado. Traduções não serão carregadas."
    );

    // Restaurar
    consoleWarnSpy.mockRestore();
  });
});

describe("translate", () => {
  it("should call i18n.t with the provided key", () => {
    const result = translate("testKey");

    expect(i18n.t).toHaveBeenCalledWith("testKey");
    expect(result).toBe("translated_testKey");
  });
});
