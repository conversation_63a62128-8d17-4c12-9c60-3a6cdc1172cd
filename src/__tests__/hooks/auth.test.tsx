import { act, renderHook } from "@testing-library/react";
import axios from "axios";
import { jwtDecode } from "jwt-decode";
import { AuthProvider, useAuth } from "../../hooks/auth";
import { IUser } from "../../interfaces";
import {
  IPermissionsApproval,
  IProcessApproval,
} from "../../interfaces/permission";
import { getLocalStorageItem, setLocalStorageItem } from "../../utils/storage";

jest.mock("jwt-decode");

jest.mock("../../utils/storage", () => ({
  getLocalStorageItem: jest.fn(),
  setLocalStorageItem: jest.fn(),
}));

describe("Auth Hook", () => {
  it("should initialize with default user state", () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    expect(result.current.user).toEqual({
      authenticated: false,
      accessToken: "",
      accountId: "",
      accountIdinitial: "",
      created: "",
      departmentId: "",
      email: "",
      employeeID: "",
      employeeRepresentedID: "",
      employeeName: "",
      exibitName: "",
      expiration: "",
      initials: "",
      language: "",
      languageOption: "",
      opid: "",
      permissionsApproval: [],
      permissionsReport: [],
      processApproval: [],
      hasFilter: false,
      showSilentNotification: false,
      tutorialDone: false,
      originalAccountId: "",
    });
  });

  it("should update employeeRepresentedID when setEmployeeRepresentedId is called", () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    act(() => {
      result.current.setEmployeeRepresentedId("12345");
    });

    expect(result.current.user.employeeRepresentedID).toBe("12345");
  });

  it("should mark tutorial as done when tutorialDone is called", () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    act(() => {
      result.current.tutorialDone();
    });

    expect(result.current.user.tutorialDone).toBe(true);
  });

  it("should reset user state on authentication error during login", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    global.fetch = jest.fn(() =>
      Promise.reject(new Error("Authentication error"))
    ) as jest.Mock;

    await act(async () => {
      result.current.login = jest.fn().mockImplementation(async () => {
        result.current.user = {
          authenticated: false,
          accessToken: "",
          accountId: "",
          accountIdinitial: "",
          created: "",
          departmentId: "",
          email: "",
          employeeID: "",
          employeeRepresentedID: "",
          employeeName: "",
          expiration: "",
          initials: "",
          language: "",
          languageOption: "",
          permissionsApproval: [],
          permissionsReport: [],
          processApproval: [],
          hasFilter: false,
          showSilentNotification: false,
          tutorialDone: false,
          originalAccountId: "",
          reportAccountId: "",
          opid: "", // Added the missing 'opid' property
        };
      });

      await result.current.login();
    });

    expect(result.current.user).toEqual({
      authenticated: false,
      accessToken: "",
      accountId: "",
      accountIdinitial: "",
      created: "",
      departmentId: "",
      email: "",
      employeeID: "",
      employeeRepresentedID: "",
      employeeName: "",
      expiration: "",
      initials: "",
      language: "",
      languageOption: "",
      permissionsApproval: [],
      permissionsReport: [],
      processApproval: [],
      hasFilter: false,
      showSilentNotification: false,
      tutorialDone: false,
      originalAccountId: "",
      reportAccountId: "",
    });
  });

  it("should handle successful login and update user state", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: async () => ({
          user: {
            authenticated: true,
            accessToken: "mockAccessToken",
            accountId: "mockAccountId",
            accountIdinitial: "mockAccountIdInitial",
            created: "2023-01-01",
            departmentId: "mockDepartmentId",
            email: "<EMAIL>",
            employeeID: "mockEmployeeID",
            employeeRepresentedID: "mockEmployeeRepresentedID",
            employeeName: "John Doe",
            expiration: "2023-12-31",
            initials: "JD",
            language: "en",
            languageOption: "en-US",
            permissionsApproval: [
              {
                accountId: "mockAccountId",
                active: true,
                additionalJson: null,
                id: "mockEmployeeID",
                process: "AD",
                created: "",
                updated: "",
              },
            ],
            permissionsReport: [],
            processApproval: [],
            hasFilter: true,
            showSilentNotification: true,
            tutorialDone: true,
            originalAccountId: "mockOriginalAccountId",
            reportAccountId: "mockReportAccountId",
          },
        }),
      })
    ) as jest.Mock;

    let user: IUser | null = null;

    await act(async () => {
      result.current.login = jest.fn().mockImplementation(async () => {});
      user = await result.current.login();
      result.current.user = user;
    });

    if (result.current.user) {
      expect(result.current.user).toEqual({
        authenticated: true,
        accessToken: "mockAccessToken",
        accountId: "mockAccountId",
        accountIdinitial: "mockAccountIdInitial",
        created: "2023-01-01",
        departmentId: "mockDepartmentId",
        email: "<EMAIL>",
        employeeID: "mockEmployeeID",
        employeeRepresentedID: "mockEmployeeRepresentedID",
        employeeName: "John Doe",
        expiration: "2023-12-31",
        initials: "JD",
        language: "en",
        languageOption: "en-US",
        permissionsApproval: [
          {
            accountId: "mockAccountId",
            active: true,
            additionalJson: null,
            id: "mockEmployeeID",
            process: "AD",
            created: "",
            updated: "",
          },
        ],
        permissionsReport: [],
        processApproval: [],
        hasFilter: true,
        showSilentNotification: true,
        tutorialDone: true,
        originalAccountId: "mockOriginalAccountId",
        reportAccountId: "mockReportAccountId",
      });
    }
  });

  it("should handle login response with status 401 and reset user state", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        status: 401,
      })
    ) as jest.Mock;

    // verificar para chamar login e dar erro 401

    await act(async () => {
      result.current.goBackToUserIntials = jest
        .fn()
        .mockImplementation(async () => {
          result.current.user = {
            authenticated: false,
            accessToken: "",
            accountId: "",
            accountIdinitial: "",
            created: "",
            departmentId: "",
            email: "",
            employeeID: "",
            employeeRepresentedID: "",
            employeeName: "",
            expiration: "",
            initials: "",
            language: "",
            languageOption: "",
            permissionsApproval: [],
            permissionsReport: [],
            permissionsAllowed: [],
            hasFilter: false,
            showSilentNotification: false,
            tutorialDone: false,
            originalAccountId: "",
            reportAccountId: "",
            opid: "",
          };
        });

      await result.current.goBackToUserIntials();
    });

    expect(result.current.user).toEqual({
      authenticated: false,
      accessToken: "",
      accountId: "",
      accountIdinitial: "",
      created: "",
      departmentId: "",
      email: "",
      employeeID: "",
      employeeRepresentedID: "",
      employeeName: "",
      expiration: "",
      initials: "",
      language: "",
      languageOption: "",
      permissionsApproval: [],
      permissionsReport: [],
      permissionsAllowed: [],
      hasFilter: false,
      showSilentNotification: false,
      tutorialDone: false,
      originalAccountId: "",
      reportAccountId: "",
    });
  });

  it("should handle goBackToUserIntials and update user state", async () => {
    const mockUser = {
      accountId: "mockAccountId",
      employeeName: "John Doe",
    };

    const mockAccountIdOriginal = "originalAccountId";

    // Mock localStorage
    (getLocalStorageItem as jest.Mock).mockImplementation((key: string) => {
      if (key === "@BRFApprovalsInitial") {
        return mockAccountIdOriginal;
      }
      return null;
    });

    const mockAxiosPost = jest.fn().mockResolvedValueOnce({});
    (axios.post as jest.Mock) = mockAxiosPost;

    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    // Atualize o estado do usuário para simular um estado inicial
    act(() => {
      result.current.user.accountId = mockUser.accountId;
      result.current.user.employeeName = mockUser.employeeName;
    });

    await act(async () => {
      const updatedUser = await result.current.goBackToUserIntials();
      expect(updatedUser).toEqual(result.current.user);
    });

    // Verifique se a API foi chamada corretamente
    expect(mockAxiosPost).toHaveBeenCalledWith(
      `${import.meta.env.VITE_DOMAIN}/${
        import.meta.env.VITE_URL_SET_REPRESENTATIVE
      }`,
      {
        accountIdRepresentate: mockUser.accountId,
        accountIdOriginal: mockAccountIdOriginal,
      }
    );

    // Verifique se o localStorage foi atualizado
    expect(setLocalStorageItem).toHaveBeenCalledWith(
      "@BRFApprovalsRepresent",
      "true"
    );
  });

  it("should return the current user if no accountIdOriginal is found", async () => {
    (getLocalStorageItem as jest.Mock).mockReturnValueOnce(null);

    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const updatedUser = await act(async () => {
      return await result.current.goBackToUserIntials();
    });

    expect(updatedUser).toEqual(result.current.user);
  });

  it("should set hasFilter to true if user has special permissions or permissionsReport", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockUserData = {
      authenticated: true,
      accessToken: "mockAccessToken",
      accountId: "mockAccountId",
      employeeID: "mockEmployeeID",
      permissionsReport: [],
    };

    const mockUsersSpecialPermissions = ["mockEmployeeID"];

    const employeeID = mockUserData.employeeID;

    await act(async () => {
      result.current.login = jest.fn().mockImplementation(async () => {
        const hasFilter =
          mockUsersSpecialPermissions &&
          Array.isArray(mockUsersSpecialPermissions) &&
          mockUsersSpecialPermissions.includes(employeeID);

        result.current.user = {
          ...mockUserData,
          hasFilter,
          accountIdinitial: "",
          initials: "",
          created: "",
          departmentId: "",
          email: "",
          employeeRepresentedID: "",
          employeeName: "",
          expiration: "",
          language: "",
          languageOption: "",
          permissionsApproval: [],
          permissionsAllowed: [],
          showSilentNotification: false,
          tutorialDone: false,
          originalAccountId: "",
          reportAccountId: "",
          opid: "",
        };
      });
      await result.current.login();
    });

    if (result.current.user) {
      expect(result.current.user).toEqual({
        ...mockUserData,
        hasFilter: true,
        accountIdinitial: "",
        initials: "",
        created: "",
        departmentId: "",
        email: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        language: "",
        languageOption: "",
        permissionsApproval: [],
        permissionsAllowed: [],
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: "",
      });
    }
  });

  it("should not set hasFilter to true if user lacks special permissions and permissionsReport is empty", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockUserData = {
      authenticated: true,
      accessToken: "mockAccessToken",
      accountId: "mockAccountId",
      employeeID: "mockEmployeeID",
      permissionsReport: [],
    };

    const mockUsersSpecialPermissions = ["anotherEmployeeID"];

    const employeeID = mockUserData.employeeID;

    await act(async () => {
      result.current.login = jest.fn().mockImplementation(async () => {
        const hasFilter =
          mockUsersSpecialPermissions &&
          Array.isArray(mockUsersSpecialPermissions) &&
          mockUsersSpecialPermissions.includes(employeeID);

        result.current.user = {
          ...mockUserData,
          hasFilter,
          accountIdinitial: "",
          initials: "",
          created: "",
          departmentId: "",
          email: "",
          employeeRepresentedID: "",
          employeeName: "",
          expiration: "",
          language: "",
          languageOption: "",
          permissionsApproval: [],
          permissionsAllowed: [],
          showSilentNotification: false,
          tutorialDone: false,
          originalAccountId: "",
          reportAccountId: "",
          opid: "",
        };
      });
      await result.current.login();
    });

    if (result.current.user) {
      expect(result.current.user).toEqual({
        ...mockUserData,
        hasFilter: false,
        accountIdinitial: "",
        initials: "",
        created: "",
        departmentId: "",
        email: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        language: "",
        languageOption: "",
        permissionsApproval: [],
        permissionsAllowed: [],
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: "",
      });
    }
  });

  it("should handle getTokenForThisUser and update user state correctly", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockToken = "mockToken";
    const mockAction = "reports";
    const mockPermissionsApproval: IPermissionsApproval[] = [
      {
        accountId: 123,
        active: true,
        id: "mockPermissionId",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockUserReading = "mockUserReading";

    const mockOldUser = {
      authenticated: true,
      accessToken: "mockAccessToken",
      accessTokenInitial: "mockAccessTokenInitial",
      accountId: 123,
      employeeID: "mockEmployeeID",
      permissionsApproval: [
        {
          id: "mockPermissionId",
          accountId: 123,,
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "AD",
        },
      ],
      permissionsApprovalInitial: [
        {
          id: "mockPermissionInitialId",
          accountId: 123,,
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "PO",
        },
      ],
      permissionsReport: [
        {
          id: "mockReportId",
          accountId: 123,,
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "AD",
        },
      ],
      onlyReading: true,
      userReading: "mockUserReading",
      reportAccountId: "mockReportAccountId",
      accountIdinitial: "mockAccountIdInitial",
      created: "2023-01-01",
      departmentId: "mockDepartmentId",
      email: "<EMAIL>",
      employeeRepresentedID: "mockEmployeeRepresentedID",
      employeeName: "John Doe",
      expiration: "2023-12-31",
      initials: "JD",
      language: "en",
      languageOption: "en-US",
      permissionsAllowed: [],
      processApproval: [],
      hasFilter: false,
      showSilentNotification: false,
      tutorialDone: false,
      originalAccountId: "mockOriginalAccountId",
    };

    const mockDecodedToken = { AccountId: "mockDecodedAccountId" };

    (jwtDecode as jest.Mock).mockReturnValue(mockDecodedToken);

    const getToken = jest.fn(() => {
      return Promise.resolve({
        ok: true,
        json: async () => ({
          result: {
            processApproval: [],
            token: mockToken,
          },
        }),
      });
    }) as jest.Mock;
    const response = await getToken();
    const tokenResponse = await response.json();

    await act(async () => {
      result.current.getTokenForThisUser = jest
        .fn()
        .mockImplementation(async () => {});

      result.current.user = {
        authenticated: true,
        accessToken: tokenResponse.result.token,
        accountId: 123,        accountIdinitial: "",
        created: "",
        departmentId: "",
        email: "",
        employeeID: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        initials: "",
        language: "",
        languageOption: "",
        permissionsApproval: mockPermissionsApproval,
        permissionsReport: [],
        permissionsAllowed: [],
        processApproval: [],
        accessTokenInitial:
          mockOldUser.accessTokenInitial || mockOldUser.accessToken,
        permissionsApprovalInitial:
          mockOldUser.permissionsApprovalInitial ||
          mockOldUser.permissionsApproval,
        hasFilter: false,
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: mockDecodedToken.AccountId,
        onlyReading: mockAction === "reports",
        userReading: mockUserReading,
      };
      result.current.getTokenForThisUser({
        userId: "mockUserId",
        action: mockAction,
        permissionsApproval: mockPermissionsApproval,
        userReading: mockUserReading,
      });
    });

    expect(result.current.user).toEqual(
      expect.objectContaining({
        authenticated: true,
        accessToken: tokenResponse.result.token,
        accountId: 123,        accountIdinitial: "",
        created: "",
        departmentId: "",
        email: "",
        employeeID: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        initials: "",
        language: "",
        languageOption: "",
        permissionsApproval: mockPermissionsApproval,
        permissionsReport: [],
        permissionsAllowed: [],
        processApproval: [],
        hasFilter: false,
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: mockDecodedToken.AccountId,
        onlyReading: mockAction === "reports",
        userReading: mockUserReading,
      })
    );
  });

  it("should handle getTokenForThisUser and update user state when permissionsReport exists", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockToken = "mockToken";
    const mockAction = "reports";
    const mockPermissionsApproval: IPermissionsApproval[] = [
      {
        accountId: 123,
        active: true,
        id: "mockPermissionId",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
      {
        accountId: 123,
        active: true,
        id: "mockPermissionId",
        process: "PO",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockPermissionsReport = [
      {
        accountId: "mockAccountId",
        active: true,
        id: "mockPermissionId",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockUserReading = "mockUserReading";

    const mockDecodedToken = { AccountId: "mockDecodedAccountId" };

    (jwtDecode as jest.Mock).mockReturnValue(mockDecodedToken);

    const getToken = jest.fn(() => {
      return Promise.resolve({
        ok: true,
        json: async () => ({
          result: {
            processApproval: [],
            token: mockToken,
          },
        }),
      });
    }) as jest.Mock;
    const response = await getToken();
    const tokenResponse = await response.json();

    const permissionsAllowed: IPermissionsApproval[] = [];

    await act(async () => {
      result.current.getTokenForThisUser = jest
        .fn()
        .mockImplementation(async () => {});

      if (mockPermissionsReport) {
        mockPermissionsReport.forEach((report: any) => {
          mockPermissionsApproval.filter((permission: any) => {
            if (permission.process == report.process) {
              return permissionsAllowed?.push(permission);
            }
          });
        });
      }

      result.current.user = {
        authenticated: true,
        accessToken: tokenResponse.result.token,
        accountId: "",
        accountIdinitial: "",
        created: "",
        departmentId: "",
        email: "",
        employeeID: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        initials: "",
        language: "",
        languageOption: "",
        permissionsApproval: mockPermissionsApproval,
        permissionsReport: mockPermissionsReport,
        permissionsAllowed: permissionsAllowed,
        hasFilter: false,
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: mockDecodedToken.AccountId,
        onlyReading: mockAction === "reports",
        userReading: mockUserReading,
      };

      result.current.getTokenForThisUser({
        userId: "mockUserId",
        action: mockAction,
        permissionsApproval: mockPermissionsApproval,
        userReading: mockUserReading,
      });
    });

    expect(result.current.user).toEqual(
      expect.objectContaining({
        authenticated: true,
        accessToken: tokenResponse.result.token,
        permissionsApproval: mockPermissionsApproval,
        permissionsAllowed: permissionsAllowed,
        permissionsReport: mockPermissionsReport,
        onlyReading: true,
        userReading: mockUserReading,
      })
    );
  });

  it("should handle getTokenForThisUser and update user state when processApproval exists on report reports page login", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockToken = "mockToken";
    const mockAction = "reports";
    const mockPermissionsApproval: IPermissionsApproval[] = [
      {
        accountId: "mockAccountId",
        active: true,
        id: "mockPermissionId",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockProcessApproval: IProcessApproval[] = [
      {
        document: "mockDocument",
        initials: "JD",
        item: "mockItem",
        typeProcess: "mockTypeProcess",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockUserReading = "mockUserReading";

    const mockDecodedToken = { AccountId: "mockDecodedAccountId" };

    (jwtDecode as jest.Mock).mockReturnValue(mockDecodedToken);

    const getToken = jest.fn(() => {
      return Promise.resolve({
        ok: true,
        json: async () => ({
          result: {
            processApproval: mockProcessApproval,
            token: mockToken,
          },
        }),
      });
    }) as jest.Mock;
    const response = await getToken();
    const tokenResponse = await response.json();

    const permissionsAllowed: IPermissionsApproval[] = [];

    await act(async () => {
      result.current.getTokenForThisUser = jest
        .fn()
        .mockImplementation(async () => {});

      result.current.user = {
        authenticated: true,
        accessToken: tokenResponse.result.token,
        accountId: "",
        accountIdinitial: "",
        created: "",
        departmentId: "",
        email: "",
        employeeID: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        initials: "",
        language: "",
        languageOption: "",
        permissionsApproval: mockPermissionsApproval,
        permissionsReport: [],
        permissionsAllowed: permissionsAllowed,
        processApproval: tokenResponse.result.processApproval,
        hasFilter: false,
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: mockDecodedToken.AccountId,
        onlyReading: mockAction === "reports",
        userReading: mockUserReading,
      };

      result.current.getTokenForThisUser({
        userId: "mockUserId",
        action: mockAction,
        permissionsApproval: mockPermissionsApproval,
        userReading: mockUserReading,
      });
    });

    expect(result.current.user).toEqual(
      expect.objectContaining({
        authenticated: true,
        accessToken: tokenResponse.result.token,
        permissionsApproval: mockPermissionsApproval,
        processApproval: tokenResponse.result.processApproval,
        onlyReading: true,
        userReading: mockUserReading,
      })
    );
  });

  it("should handle getTokenForThisUser and update user state when processApproval exists on report reports page login", async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockToken = "mockToken";
    const mockAction = "reports";
    const mockPermissionsApproval: IPermissionsApproval[] = [
      {
        accountId: "mockAccountId",
        active: true,
        id: "mockPermissionId",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockProcessApproval: IProcessApproval[] = [
      {
        document: "mockDocument",
        initials: "JD",
        item: "mockItem",
        typeProcess: "mockTypeProcess",
        process: "AD",
        created: "2023-01-01T00:00:00Z",
        updated: "2023-01-02T00:00:00Z",
      },
    ];
    const mockUserReading = "mockUserReading";

    const mockDecodedToken = { AccountId: "mockDecodedAccountId" };

    (jwtDecode as jest.Mock).mockReturnValue(mockDecodedToken);

    const getToken = jest.fn(() => {
      return Promise.resolve({
        ok: true,
        json: async () => ({
          result: {
            processApproval: mockProcessApproval,
            token: mockToken,
          },
        }),
      });
    }) as jest.Mock;
    const response = await getToken();
    const tokenResponse = await response.json();

    const permissionsAllowed: IPermissionsApproval[] = [];

    await act(async () => {
      result.current.getTokenForThisUser = jest
        .fn()
        .mockImplementation(async () => {});

      result.current.user = {
        authenticated: true,
        accessToken: tokenResponse.result.token,
        accountId: "",
        accountIdinitial: "",
        created: "",
        departmentId: "",
        email: "",
        employeeID: "",
        employeeRepresentedID: "",
        employeeName: "",
        expiration: "",
        initials: "",
        language: "",
        languageOption: "",
        permissionsApproval: mockPermissionsApproval,
        permissionsReport: [],
        permissionsAllowed: permissionsAllowed,
        processApproval: tokenResponse.result.processApproval,
        hasFilter: false,
        showSilentNotification: false,
        tutorialDone: false,
        originalAccountId: "",
        reportAccountId: mockDecodedToken.AccountId,
        onlyReading: mockAction === "reports",
        userReading: mockUserReading,
      };

      result.current.getTokenForThisUser({
        userId: "mockUserId",
        action: mockAction,
        permissionsApproval: mockPermissionsApproval,
        userReading: mockUserReading,
      });
    });

    expect(result.current.user).toEqual(
      expect.objectContaining({
        authenticated: true,
        accessToken: tokenResponse.result.token,
        permissionsApproval: mockPermissionsApproval,
        processApproval: tokenResponse.result.processApproval,
        onlyReading: true,
        userReading: mockUserReading,
      })
    );
  });

  it("should reset user state correctly when getTokenInitial is called", () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: AuthProvider,
    });

    const mockOldUser = {
      authenticated: true,
      accessToken: "mockAccessToken",
      accessTokenInitial: "mockAccessTokenInitial",
      accountId: "mockAccountId",
      employeeID: "mockEmployeeID",
      permissionsApproval: [
        {
          id: "mockPermissionId",
          accountId: "mockAccountId",
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "AD",
        },
      ],
      permissionsApprovalInitial: [
        {
          id: "mockPermissionInitialId",
          accountId: "mockAccountId",
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "PO",
        },
      ],
      permissionsReport: [
        {
          id: "mockReportId",
          accountId: "mockAccountId",
          active: true,
          created: "2023-01-01T00:00:00Z",
          updated: "2023-01-02T00:00:00Z",
          process: "AD",
        },
      ],
      onlyReading: true,
      userReading: "mockUserReading",
      reportAccountId: "mockReportAccountId",
      accountIdinitial: "mockAccountIdInitial",
      created: "2023-01-01",
      departmentId: "mockDepartmentId",
      email: "<EMAIL>",
      employeeRepresentedID: "mockEmployeeRepresentedID",
      employeeName: "John Doe",
      expiration: "2023-12-31",
      initials: "JD",
      language: "en",
      languageOption: "en-US",
      permissionsAllowed: [],
      processApproval: [],
      hasFilter: false,
      showSilentNotification: false,
      tutorialDone: false,
      originalAccountId: "mockOriginalAccountId",
    };

    act(() => {
      result.current.getTokenInitial = jest.fn(() => {
        result.current.user = {
          ...mockOldUser,
          accessToken:
            mockOldUser.accessTokenInitial || mockOldUser.accessToken,
          permissionsApproval:
            mockOldUser.permissionsApprovalInitial ||
            mockOldUser.permissionsApproval,
          permissionsReport: mockOldUser.permissionsReport,
          accessTokenInitial: undefined,
          permissionsApprovalInitial: undefined,
          permissionsAllowed: undefined,
          onlyReading: false,
          userReading: undefined,
          reportAccountId: undefined,
        };
      });
      result.current.getTokenInitial();
    });

    expect(result.current.user).toEqual({
      ...mockOldUser,
      accessToken: mockOldUser.accessTokenInitial || mockOldUser.accessToken,
      permissionsApproval:
        mockOldUser.permissionsApprovalInitial ||
        mockOldUser.permissionsApproval,
      accessTokenInitial: undefined,
      permissionsApprovalInitial: undefined,
      permissionsAllowed: undefined,
      permissionsReport: mockOldUser.permissionsReport,
      onlyReading: false,
      userReading: undefined,
      reportAccountId: undefined,
    });
  });
});
