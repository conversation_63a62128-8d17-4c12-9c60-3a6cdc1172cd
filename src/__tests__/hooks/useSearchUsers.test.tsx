import { act, renderHook } from "@testing-library/react";
import axios from "axios";
import { MemoryRouter } from "react-router-dom";
import { useSearchUsers } from "../../hooks/useSearchUsers";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock import.meta.env

describe("useSearchUsers", () => {
  const mockGetTokenForThisUser = jest.fn();
  const mockNavigate = jest.fn();

  jest.mock("react-router-dom", () => ({
    useNavigate: () => mockNavigate,
  }));

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(() => {
    import.meta.env.VITE_DOMAIN = "http://mock-domain.com";
    import.meta.env.VITE_API_KEY = "mockApiKey";
  });

  const mockOption = [
    {
      email: "mockedEmail",
      employeeId: "mockedEmployeeId",
      id: 1,
      initials: "mockedInitial",
      name: "mockedName",
      opid: "mockedOpid",
      permissionsApproval: [],
    },
  ];

  it("should initialize with default values", () => {
    const { result } = renderHook(
      () => useSearchUsers(mockGetTokenForThisUser, "reports"),
      {
        wrapper: ({ children }) => <MemoryRouter>{children}</MemoryRouter>,
      }
    );

    expect(result.current.selectedValue).toBe("");
    expect(result.current.inputValue).toBe("");
    expect(result.current.isPending).toBe(false);
    expect(result.current.options).toEqual([]);
  });

  it("should update inputValue and fetch options on handleInputChange", async () => {
    mockedAxios.get.mockResolvedValueOnce({
      data: mockOption,
    });

    const { result } = renderHook(
      () => useSearchUsers(mockGetTokenForThisUser, "reports"),
      {
        wrapper: ({ children }) => <MemoryRouter>{children}</MemoryRouter>,
      }
    );

    await act(async () => {
      await result.current.handleInputChange("mockedName");
    });

    expect(result.current.inputValue).toBe("mockedName");

    // Add a small delay to ensure state updates have completed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.options).toEqual(mockOption);
  });

  it("should update selectedValue and call searchCardsOfSpecifcUser on handleChange", async () => {
    const { result } = renderHook(
      () => useSearchUsers(mockGetTokenForThisUser, "subordinate"),
      {
        wrapper: ({ children }) => <MemoryRouter>{children}</MemoryRouter>,
      }
    );

    await act(() => {
      result.current.handleChange(mockOption);
    });

    expect(result.current.selectedValue).toBe("mockedName");
    expect(mockGetTokenForThisUser).toHaveBeenCalledWith({
      userId: "mockedEmployeeId",
      action: "subordinate",
      permissionsApproval: [],
      userReading: "mockedName",
    });
  });

  it("should handle errors in searchCardsOfSpecifcUser gracefully", async () => {
    mockGetTokenForThisUser.mockRejectedValueOnce(new Error("Error"));

    const { result } = renderHook(
      () => useSearchUsers(mockGetTokenForThisUser, "subordinate"),
      {
        wrapper: ({ children }) => <MemoryRouter>{children}</MemoryRouter>,
      }
    );

    await act(() => {
      result.current.handleChange(mockOption);
    });

    expect(mockGetTokenForThisUser).toHaveBeenCalledWith({
      userId: "mockedEmployeeId",
      action: "subordinate",
      permissionsApproval: [],
      userReading: "mockedName",
    });
  });
});
