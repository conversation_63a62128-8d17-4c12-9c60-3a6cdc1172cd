import { PublicClientApplication } from "@azure/msal-browser";
import { renderHook } from "@testing-library/react";
import useMsalToken from "../../hooks/useMsalToken";

jest.mock("@azure/msal-browser", () => ({
  PublicClientApplication: jest.fn().mockImplementation(() => ({
    acquireTokenSilent: jest.fn(),
    acquireTokenPopup: jest.fn(),
  })),
}));

describe("useMsalToken", () => {
  const mockMsalInstance = new PublicClientApplication({
    auth: { clientId: "test-client-id" },
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return a token when acquireTokenSilent succeeds", async () => {
    const mockToken = "mockToken";
    mockMsalInstance.acquireTokenSilent = jest
      .fn()
      .mockResolvedValue({ accessToken: mockToken });

    const { result, waitForNextUpdate } = renderHook(() =>
      useMsalToken(mockMsalInstance, "test-scope")
    );

    await waitForNextUpdate();

    expect(result.current.token).toBe(mockToken);
    expect(result.current.error).toBeUndefined();
  });

  it("should handle errors when acquireTokenSilent fails and fallback to acquireTokenPopup", async () => {
    const mockToken = "mockToken";
    mockMsalInstance.acquireTokenSilent = jest
      .fn()
      .mockRejectedValue(new Error("Silent token error"));
    mockMsalInstance.acquireTokenPopup = jest
      .fn()
      .mockResolvedValue({ accessToken: mockToken });

    const { result, waitForNextUpdate } = renderHook(() =>
      useMsalToken(mockMsalInstance, "test-scope")
    );

    await waitForNextUpdate();

    expect(result.current.token).toBe(mockToken);
    expect(result.current.error).toBeUndefined();
  });

  it("should return an error when both acquireTokenSilent and acquireTokenPopup fail", async () => {
    const errorMessage = "Token acquisition failed";
    mockMsalInstance.acquireTokenSilent = jest
      .fn()
      .mockRejectedValue(new Error("Silent token error"));
    mockMsalInstance.acquireTokenPopup = jest
      .fn()
      .mockRejectedValue(new Error(errorMessage));

    const { result, waitForNextUpdate } = renderHook(() =>
      useMsalToken(mockMsalInstance, "test-scope")
    );

    await waitForNextUpdate();

    expect(result.current.token).toBeUndefined();
    expect(result.current.error).toBe(errorMessage);
  });
});
