import { fireEvent, render, screen } from "@testing-library/react";
import { ModalItemDetails } from "../../components/ModalItemDetails";
import { ISap } from "../../interfaces";

// Mock de react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("ModalItemDetails Component", () => {
  // Dados de exemplo
  const mockData: ISap.ItemProps = {
    DOCUMENTO: "1000001",
    ITEM: "10",
    MATERIAL: "Item de Teste",
    QTD: "5",
    ADICIONAIS: [
      { CAMPO: "CUSTNAME", VALOR: "Cliente Teste" },
      { CAMPO: "SALESPERSON", VALOR: "Vendedor Teste" },
    ],
    TIPO: "Material",
    VLUNIT: "150.00",
    VLITEM: "750.00",
    CENTRO: "",
    DATA_EMISSAO: "",
    FORNECEDOR: "",
    REQUISITANTE: "",
    VLDOC: "",
  };

  // Props básicas do componente
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    modalTitle: "Detalhes.do.Documento",
    data: mockData,
    detailModalHeader: undefined,
    detailModalContent: undefined,
    isLoading: false,
  };

  // Helpers para renderização
  const renderComponent = (props = {}) => {
    return render(<ModalItemDetails {...defaultProps} {...props} />);
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the modal with the correct title", () => {
    renderComponent();
    expect(screen.getByText("Detalhes.do.Documento")).toBeInTheDocument();
  });

  it("should call onClose when close button is clicked", () => {
    renderComponent();
    const closeButton = screen.getByText("Fechar");
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("should render the header content when provided", () => {
    const detailModalHeader = () => (
      <tr>
        <td>Número do Documento:</td>
        <td data-testid="doc-number">{mockData.DOCUMENTO}</td>
      </tr>
    );

    renderComponent({ detailModalHeader });
    expect(screen.getByTestId("doc-number")).toHaveTextContent("1000001");
  });

  it("should render the body content when provided", () => {
    const detailModalContent = () => (
      <tr>
        <td>Material:</td>
        <td data-testid="material-name">{mockData.MATERIAL}</td>
      </tr>
    );

    renderComponent({ detailModalContent });
    expect(screen.getByTestId("material-name")).toHaveTextContent(
      "Item de Teste"
    );
  });

  it("should render both header and body content when provided", () => {
    const detailModalHeader = () => (
      <tr>
        <td>Número do Documento:</td>
        <td data-testid="doc-number">{mockData.DOCUMENTO}</td>
      </tr>
    );

    const detailModalContent = () => (
      <tr>
        <td>Material:</td>
        <td data-testid="material-name">{mockData.MATERIAL}</td>
      </tr>
    );

    renderComponent({ detailModalHeader, detailModalContent });

    expect(screen.getByTestId("doc-number")).toHaveTextContent("1000001");
    expect(screen.getByTestId("material-name")).toHaveTextContent(
      "Item de Teste"
    );
  });

  it("should render approval and rejection buttons", () => {
    renderComponent();

    const approveButton = screen.getByText("Aprovar");
    const rejectButton = screen.getByText("Reprovar");

    expect(approveButton).toBeInTheDocument();
    expect(rejectButton).toBeInTheDocument();
  });

  it("should handle promises from detailModalHeader", async () => {
    // Simular um header que retorna uma Promise
    const asyncHeaderContent = jest.fn().mockImplementation(() => {
      return Promise.resolve(
        <tr>
          <td>Async Header:</td>
          <td data-testid="async-header">Carregado Assincronamente</td>
        </tr>
      );
    });

    renderComponent({ detailModalHeader: asyncHeaderContent });

    // Verificar se a função foi chamada
    expect(asyncHeaderContent).toHaveBeenCalled();
  });

  it("should handle promises from detailModalContent", async () => {
    // Simular um content que retorna uma Promise
    const asyncBodyContent = jest.fn().mockImplementation(() => {
      return Promise.resolve(
        <tr>
          <td>Async Content:</td>
          <td data-testid="async-content">Carregado Assincronamente</td>
        </tr>
      );
    });

    renderComponent({ detailModalContent: asyncBodyContent });

    // Verificar se a função foi chamada
    expect(asyncBodyContent).toHaveBeenCalled();
  });

  it("should not display content when not provided", () => {
    renderComponent({
      detailModalHeader: undefined,
      detailModalContent: undefined,
    });

    // Procurar por elementos de tabela que não deveriam existir
    const tables = document.querySelectorAll("table");
    expect(tables.length).toBe(0);
  });

  it("should handle array data correctly", () => {
    const arrayData: ISap.ItemProps[] = [
      { ...mockData, DOCUMENTO: "1000001" },
      { ...mockData, DOCUMENTO: "1000002" },
    ];

    const detailModalHeader = (data: ISap.ItemProps | ISap.ItemProps[]) => {
      const items = Array.isArray(data) ? data : [data];
      return (
        <>
          {items.map((item, index) => (
            <tr key={index}>
              <td>Documento {index + 1}:</td>
              <td data-testid={`doc-number-${index}`}>{item.DOCUMENTO}</td>
            </tr>
          ))}
        </>
      );
    };

    renderComponent({
      data: arrayData,
      detailModalHeader,
    });

    expect(screen.getByTestId("doc-number-0")).toHaveTextContent("1000001");
    expect(screen.getByTestId("doc-number-1")).toHaveTextContent("1000002");
  });

  it("should show the dialog when open is true", () => {
    renderComponent({ open: true });
    expect(screen.getByRole("dialog")).toBeInTheDocument();
  });

  it("should not show the dialog when open is false", () => {
    renderComponent({ open: false });
    expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
  });
});
