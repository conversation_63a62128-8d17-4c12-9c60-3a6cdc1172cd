import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "react-toastify";
import { APISap } from "../../api";
import { DTable } from "../../components/DataTableSap";
import { useAuth } from "../../hooks";
import { useCards } from "../../hooks/cards";
import { ISap } from "../../interfaces";

// Mock de dependências
jest.mock("react-query", () => ({
  useMutation: () => ({
    isLoading: false,
    mutate: jest.fn((params, options) => {
      options.onSuccess({
        data: {
          MSG: [
            {
              TIPO: "S",
              CODIGO: "000",
              MENSAGEM: "Operação realizada com sucesso",
            },
          ],
        },
        success: true,
      });
    }),
  }),
}));

jest.mock("../../api", () => ({
  APISap: {
    approvalReprovalDocument: jest.fn(() =>
      Promise.resolve({
        data: {
          MSG: [
            {
              TIPO: "S",
              CODIGO: "000",
              MENSAGEM: "Operação realizada com sucesso",
            },
          ],
        },
        success: true,
      })
    ),
    getDocumentDetails: jest.fn(() =>
      Promise.resolve({
        data: {
          RETORNO: [
            { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
          ],
        },
      })
    ),
  },
}));

jest.mock("react-toastify", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("../../hooks", () => ({
  useAuth: jest.fn(() => ({
    user: { onlyReading: false },
  })),
}));

jest.mock("../../hooks/cards", () => ({
  useCards: jest.fn(() => ({
    execute: jest.fn(() =>
      Promise.resolve({
        RETORNO: [
          { DOCUMENTO: "DOC123", TIPO: "Material", VALOR: 100 },
          { DOCUMENTO: "DOC456", TIPO: "Serviço", VALOR: 200 },
        ],
      })
    ),
  })),
}));

jest.mock("../../constant", () => ({
  processesProps: jest.fn(() => [
    {
      headerColumns: [
        {
          name: "Document",
          selector: (row: ISap.ItemProps) => row.DOCUMENTO,
        },
        { name: "Type", selector: (row: ISap.ItemProps) => row.TIPO },
        {
          name: "Value",
          selector: (row: ISap.ItemProps) => row.VLDOC,
        },
      ],
      detailColumns: [
        { name: "Item", selector: (row: ISap.ItemProps) => row.ITEM },
        {
          name: "Material",
          selector: (row: ISap.ItemProps) => row.MATERIAL,
        },
      ],
      title: "Processo de teste",
      hasDetailModal: true,
      approveItems: false,
    },
  ]),
}));

// Mocks para tradução
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("i18next", () => ({
  language: "pt-BR",
}));

describe("DTable Component", () => {
  const headerData = [
    { DOCUMENTO: "DOC123", TIPO: "Material", VALOR: 100 },
    { DOCUMENTO: "DOC456", TIPO: "Serviço", VALOR: 200 },
  ];

  const detailData = [
    { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material A" },
    { DOCUMENTO: "DOC123", ITEM: "002", MATERIAL: "Material B" },
    { DOCUMENTO: "DOC456", ITEM: "001", MATERIAL: "Material C" },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the table with header data", () => {
    render(<DTable headerData={headerData} process="TEST" />);

    expect(screen.getByText("Document")).toBeInTheDocument();
    expect(screen.getByText("Type")).toBeInTheDocument();
    expect(screen.getByText("Value")).toBeInTheDocument();
    expect(screen.getByText("DOC123")).toBeInTheDocument();
    expect(screen.getByText("Material")).toBeInTheDocument();
  });

  it("expands a row when clicked", async () => {
    render(
      <DTable headerData={headerData} detailData={detailData} process="TEST" />
    );

    // Encontrar a linha pelo texto do documento
    const row = screen.getByText("DOC123").closest('div[role="row"]');

    // Encontrar o botão de expandir na linha
    const expandButton = row?.querySelector("button");
    if (expandButton) {
      fireEvent.click(expandButton);
    }

    // Verificar se os detalhes são exibidos após expandir
    await waitFor(() => {
      expect(screen.getByText("Item")).toBeInTheDocument();
      expect(screen.getByText("Material A")).toBeInTheDocument();
      expect(screen.getByText("001")).toBeInTheDocument();
    });
  });

  it("approves a document successfully", async () => {
    render(<DTable headerData={headerData} process="TEST" />);

    // Encontrar o botão de aprovar (CheckCircle)
    const approveButtons = document.querySelectorAll(
      '[data-testid="CheckCircleIcon"]'
    );
    fireEvent.click(approveButtons[0]);

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith("Success.in.approving");
      expect(useCards().execute).toHaveBeenCalled();
    });
  });

  it("handles error when approving", async () => {
    // Sobrescrever o mock para simular um erro
    jest
      .spyOn(require("react-query"), "useMutation")
      .mockImplementation(() => ({
        isLoading: false,
        mutate: jest.fn((params, options) => {
          options.onSuccess({
            data: {
              MSG: [
                {
                  TIPO: "E",
                  CODIGO: "064",
                  MENSAGEM: "Erro ao aprovar documento",
                },
              ],
            },
            success: false,
          });
        }),
      }));

    render(<DTable headerData={headerData} process="TEST" />);

    // Encontrar o botão de aprovar (CheckCircle)
    const approveButtons = document.querySelectorAll(
      '[data-testid="CheckCircleIcon"]'
    );
    fireEvent.click(approveButtons[0]);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Erro ao aprovar documento");
      expect(useCards().execute).not.toHaveBeenCalled();
    });
  });

  it("opens detail modal when search button is clicked", async () => {
    render(<DTable headerData={headerData} process="TEST" />);

    // Encontrar o botão de pesquisa (Search)
    const searchButtons = document.querySelectorAll(
      '[data-testid="SearchIcon"]'
    );
    fireEvent.click(searchButtons[0]);

    await waitFor(() => {
      expect(APISap.getDocumentDetails).toHaveBeenCalledWith("DOC123", "TEST");
      // Verificar se o modal está aberto (depende da implementação específica do modal)
      expect(screen.getByText("Processo de teste")).toBeInTheDocument();
    });
  });

  it("handles only reading mode", () => {
    // Sobrescrever o mock do useAuth
    (useAuth as jest.Mock).mockImplementation(() => ({
      user: { onlyReading: true },
    }));

    render(<DTable headerData={headerData} process="TEST" />);

    // Em modo somente leitura, não deve exibir os botões de ação
    const approveButtons = document.querySelectorAll(
      '[data-testid="CheckCircleIcon"]'
    );
    const rejectButtons = document.querySelectorAll(
      '[data-testid="CancelIcon"]'
    );

    expect(approveButtons.length).toBe(0);
    expect(rejectButtons.length).toBe(0);
  });

  it("handles no detail data", () => {
    render(<DTable headerData={headerData} detailData={[]} process="TEST" />);

    // Verificar se os cabeçalhos são renderizados
    expect(screen.getByText("Document")).toBeInTheDocument();

    // Clicar para expandir não deve mostrar detalhes
    const row = screen.getByText("DOC123").closest('div[role="row"]');
    const expandButton = row?.querySelector("button");

    expect(expandButton).toBeFalsy(); // Não deve haver botão de expandir
  });

  it("translates column headers correctly", () => {
    render(<DTable headerData={headerData} process="TEST" />);

    // Verificar se as chaves de tradução são passadas corretamente
    expect(screen.getByText("Document")).toBeInTheDocument();
    expect(screen.getByText("Type")).toBeInTheDocument();
    expect(screen.getByText("Value")).toBeInTheDocument();
  });
});
