import { fireEvent, render, screen } from "@testing-library/react";
import { Select } from "../../components";

describe("Select Component", () => {
  const options = [
    {
      email: "<EMAIL>",
      employeeId: "12345",
      id: 1,
      initials: "<PERSON><PERSON>",
      name: "<PERSON>",
      opid: "OP123",
      permissionsApproval: [],
      // label: "Option 1",
      value: "option1",
    },
    {
      email: "<EMAIL>",
      employeeId: "67890",
      id: 2,
      initials: "<PERSON><PERSON>",
      name: "<PERSON>",
      opid: "OP456",
      permissionsApproval: [],
      // label: "Option 2",
      value: "option2",
    },
  ];

  it("should trigger onChange when a new option is selected", () => {
    const handleChange = jest.fn();
    const value = "option2";
    render(
      <Select
        options={options}
        value={value}
        onChange={handleChange}
        onInputChange={() => {}}
      />
    );
    const selectElement = screen.getByRole("combobox") as HTMLSelectElement;

    fireEvent.mouseDown(selectElement);
    const optionElement = screen.getByText("John Doe");
    fireEvent.click(optionElement);
    expect(handleChange).toHaveBeenCalledWith([
      {
        email: "<EMAIL>",
        employeeId: "12345",
        id: 1,
        initials: "JD",
        name: "John Doe",
        opid: "OP123",
        permissionsApproval: [],
        // label: "Option 1",
        value: "option1",
      },
    ]);
  });

  it("should display the correct default value", () => {
    render(
      <Select
        options={options}
        value="option1"
        onChange={() => {}}
        onInputChange={() => {}}
      />
    );

    // Verifica se o elemento select está presente
    const selectElement = screen.getByRole("combobox") as HTMLSelectElement;
    expect(selectElement).toBeInTheDocument();

    // Verifica se o valor padrão está correto
    if (selectElement.value) {
      expect(selectElement.value).toBe("option1");
    }
  });

  it("should handle empty options gracefully", () => {
    render(
      <Select
        options={[]}
        value=""
        onChange={() => {}}
        onInputChange={() => {}}
      />
    );
    const selectElement = screen.getByRole("combobox");
    expect(selectElement.children.length).toBe(0);
  });

  it("should disable the select when disabled prop is true", () => {
    render(
      <Select
        options={options}
        disabled
        value=""
        onChange={() => {}}
        onInputChange={() => {}}
      />
    );
    const selectElement = screen.getByRole("combobox");
    expect(selectElement).toBeDisabled();
  });
});
