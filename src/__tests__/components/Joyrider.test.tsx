import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { EVENTS, STATUS } from "react-joyride";
import { JoyrideTutorial } from "../../components";

describe("JoyrideTutorial Component", () => {
  const mockSetRunJoyrider = jest.fn();
  const mockTutorialDone = jest.fn();

  jest.mock("../../hooks", () => ({
    useAuth: () => ({
      tutorialDone: mockTutorialDone,
    }),
  }));

  const stepsDesktop = [
    { target: ".step1", content: "Step 1" },
    { target: ".step2", content: "Step 2" },
  ];

  const stepsMobile = [
    { target: ".step1-mobile", content: "Step 1 Mobile" },
    { target: ".step2-mobile", content: "Step 2 Mobile" },
  ];

  it("renders JoyrideTutorial component", () => {
    render(
      <JoyrideTutorial
        stepsDesktop={stepsDesktop}
        stepsMobile={stepsMobile}
        runJoyrider={true}
        setRunJoyrider={mockSetRunJoyrider}
      />
    );
    const joyrideContainer = screen.getByTestId("joyride-dialog");
    expect(joyrideContainer).toBeInTheDocument();

    const joyride = joyrideContainer.querySelector(".react-joyride");
    expect(joyride).toBeInTheDocument();
  });

  it("calls setRunJoyrider and tutorialDone when tutorial is finished", async () => {
    render(
      <JoyrideTutorial
        stepsDesktop={stepsDesktop}
        stepsMobile={stepsMobile}
        runJoyrider={true}
        setRunJoyrider={mockSetRunJoyrider}
      />
    );

    fireEvent(
      screen.getByTestId("joyride-dialog"),
      new CustomEvent("callback", {
        detail: {
          status: STATUS.FINISHED,
          action: null,
          index: 0,
          type: null,
        },
      })
    );

    // ver o que realmente chama quando acaba o turorial
    await waitFor(() => {
      expect(mockSetRunJoyrider).toHaveBeenCalledWith(false);
    });

    // Verifica se tutorialDone foi chamado
    expect(mockTutorialDone).toHaveBeenCalled();
  });

  it("handles EVENTS.TARGET_NOT_FOUND", async () => {
    render(
      <JoyrideTutorial
        stepsDesktop={stepsDesktop}
        stepsMobile={stepsMobile}
        runJoyrider={true}
        setRunJoyrider={mockSetRunJoyrider}
      />
    );
    fireEvent(
      screen.getByTestId("joyride-dialog"),
      new CustomEvent("callback", {
        detail: {
          status: null,
          action: null,
          index: 0,
          type: EVENTS.TARGET_NOT_FOUND,
        },
      })
    );
    await waitFor(() => {
      expect(mockSetRunJoyrider).toHaveBeenCalledWith(false);
      // aqui ele ta verificando se foi chamado o metodo passando o parametro false
    });
  });

  it("handles step navigation", () => {
    render(
      <JoyrideTutorial
        stepsDesktop={stepsDesktop}
        stepsMobile={stepsMobile}
        runJoyrider={true}
        setRunJoyrider={mockSetRunJoyrider}
      />
    );

    // Simula o callback do Joyride com EVENTS.STEP_AFTER
    fireEvent(
      screen.getByTestId("joyride-dialog"),
      new CustomEvent("callback", {
        detail: {
          status: null,
          action: "next",
          index: 0,
          type: EVENTS.STEP_AFTER,
        },
      })
    );

    // Verifica se o índice do passo foi atualizado
    expect(mockSetRunJoyrider).not.toHaveBeenCalledWith(false);
  });

  it("does not call tutorialDone when tutorial is not finished", async () => {
    render(
      <JoyrideTutorial
        stepsDesktop={stepsDesktop}
        stepsMobile={stepsMobile}
        runJoyrider={true}
        setRunJoyrider={mockSetRunJoyrider}
      />
    );

    fireEvent(
      screen.getByTestId("joyride-dialog"),
      new CustomEvent("callback", {
        detail: {
          status: STATUS.RUNNING,
          action: null,
          index: 0,
          type: null,
        },
      })
    );

    // Verifica se setRunJoyrider não foi chamado
    await waitFor(() => {
      expect(mockSetRunJoyrider).not.toHaveBeenCalledWith(false);
    });

    // Verifica se tutorialDone não foi chamado
    expect(mockTutorialDone).not.toHaveBeenCalled();
  });
});
