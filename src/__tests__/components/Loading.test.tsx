import { render, screen } from "@testing-library/react";
import React from "react";
import Loading from "../../components/Loading";

// Mock para o createPortal
jest.mock("react-dom", () => {
  return {
    ...jest.requireActual("react-dom"),
    createPortal: (node: React.ReactNode) => node,
  };
});

describe("Loading Component", () => {
  beforeEach(() => {
    // Limpar qualquer mock/configuração entre testes
    jest.clearAllMocks();
  });

  it("should not render anything when open is false", () => {
    const { container } = render(
      <Loading open={false} message="Carregando..." />
    );
    expect(container.firstChild).toBeNull();
  });

  it("should render backdrop with spinner when open is true", () => {
    render(<Loading open={true} message="Carregando..." />);

    // Verificar se o Backdrop foi renderizado
    const backdrop = document.querySelector(".MuiBackdrop-root");
    expect(backdrop).not.toBeNull();

    expect(backdrop).toBeInTheDocument();

    // Verificar se o CircularProgress foi renderizado
    const spinner = document.querySelector(".MuiCircularProgress-root");
    expect(spinner).toBeInTheDocument();
  });

  it("should display the provided message", () => {
    const testMessage = "Teste de carregamento";
    render(<Loading open={true} message={testMessage} />);

    expect(screen.getByText(testMessage)).toBeInTheDocument();
  });

  it("should use default message if none provided", () => {
    // O componente não tem uma mensagem padrão explícita,
    // então este teste verifica que ele não quebra sem uma mensagem
    render(<Loading open={true} />);

    // Verificar se o componente renderizou sem erros
    const backdrop = document.querySelector(".MuiBackdrop-root");
    expect(backdrop).toBeInTheDocument();

    // Não deveria ter um elemento Typography com texto
    const typographyElements = document.querySelectorAll(".MuiTypography-root");
    typographyElements.forEach((elem) => {
      expect(elem.textContent).toBe("");
    });
  });

  it("should have correct styling", () => {
    render(<Loading open={true} message="Carregando..." />);

    const backdrop = document.querySelector(".MuiBackdrop-root");
    expect(backdrop).not.toBeNull();

    expect(backdrop).toHaveStyle("background-color: rgba(0, 0, 0, 0.7)");

    // Verificar se o CircularProgress tem o tamanho correto
    const spinner = document.querySelector(".MuiCircularProgress-root");
    expect(spinner).not.toBeNull();

    // CircularProgress indeterminate doesn't have aria-valuenow
    expect(spinner).toHaveClass("MuiCircularProgress-indeterminate");
  });

  it("should have a high z-index for overlay", () => {
    render(<Loading open={true} message="Carregando..." />);

    // Verificar se o Backdrop tem um z-index alto
    const backdrop = document.querySelector(".MuiBackdrop-root");
    expect(backdrop).not.toBeNull();

    if (backdrop) {
      const styles = window.getComputedStyle(backdrop);
      // Podemos verificar o inline style diretamente
      expect(backdrop).toHaveStyle("z-index: 9999");
    }
  });

  it("should have white text color for the message", () => {
    render(<Loading open={true} message="Carregando..." />);

    const messageElement = screen.getByText("Carregando...");
    expect(messageElement).toHaveStyle("color: white");
  });

  it("should center content vertically and horizontally", () => {
    render(<Loading open={true} message="Carregando..." />);

    const backdrop = document.querySelector(".MuiBackdrop-root");
    expect(backdrop).not.toBeNull();

    expect(backdrop).toHaveStyle({
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
    });
  });
});
