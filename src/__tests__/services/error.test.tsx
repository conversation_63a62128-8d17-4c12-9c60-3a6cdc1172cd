import { toast } from "react-toastify";
import { handleApiError } from "../../../src/services/error";

// Mock do react-toastify
jest.mock("react-toastify", () => ({
  toast: {
    error: jest.fn(),
  },
}));

describe("Error Service", () => {
  // Função de tradução mock
  const mockT = jest.fn((key) => key);

  beforeEach(() => {
    // Limpar os mocks entre os testes
    jest.clearAllMocks();
  });

  describe("handleApiError", () => {
    it("should show unauthorized error message for 401 status", () => {
      // Criar um erro com status 401
      const error = {
        request: { status: 401 },
      };

      handleApiError(error, mockT, "TestAPI");

      // Verificar se o toast foi chamado com a mensagem correta
      expect(toast.error).toHaveBeenCalledWith(
        "Unauthorized.refresh.the.screen.and.try.again."
      );
      expect(mockT).toHaveBeenCalledWith(
        "Unauthorized.refresh.the.screen.and.try.again."
      );
    });

    it("should show server error message for 500 status", () => {
      // Criar um erro com status 500
      const error = {
        request: { status: 500 },
      };

      handleApiError(error, mockT, "TestAPI");

      expect(toast.error).toHaveBeenCalledWith(
        "Oops.Something.went.wrong.Try.again.later."
      );
      expect(mockT).toHaveBeenCalledWith(
        "Oops.Something.went.wrong.Try.again.later."
      );
    });

    it("should show generic error message for other errors", () => {
      // Erro genérico sem request ou status
      const error = new Error("Generic error");

      handleApiError(error, mockT, "TestAPI");

      // Verificar se o toast foi chamado com a mensagem JSX
      expect(toast.error).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith("It.was.not.possible.to.connect.with");
      expect(mockT).toHaveBeenCalledWith("Try.again.later");
    });

    it("should handle empty origin in generic error message", () => {
      const error = new Error("Generic error");

      // Chamar sem origem
      handleApiError(error, mockT, undefined);

      expect(toast.error).toHaveBeenCalled();
      // Verificar se a função toast.error foi chamada com um componente JSX
      const toastCall = (toast.error as jest.Mock).mock.calls[0][0];

      // Verificando que é um elemento React
      expect(toastCall.type).toBe("div");

      // Verificar se o segundo div contém a mensagem de tentar novamente
      const secondDiv = toastCall.props.children[1];
      expect(secondDiv.props.children).toBe("Try.again.later");
    });

    it("should handle network errors with request but no status", () => {
      const error = {
        request: {}, // request existe, mas status não
      };

      handleApiError(error, mockT, "TestAPI");

      // Deve mostrar mensagem genérica
      expect(toast.error).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith("It.was.not.possible.to.connect.with");
    });
  });
});
