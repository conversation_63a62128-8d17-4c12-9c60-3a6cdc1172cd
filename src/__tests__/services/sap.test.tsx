import { APISap } from "../../api";
import { ISap } from "../../interfaces";
import {
  createApprovalParams,
  filterDocumentDetails,
  getDocumentDetails,
  prepareTableData,
  updateDataAfterApproval,
} from "../../services/sap";
import { mock } from "../../utils/mock";
import { getUniqueDocs } from "../../utils/UniqueDocs";

// Mock das dependências
jest.mock("../../api", () => ({
  APISap: {
    getDocumentDetails: jest.fn(),
  },
}));

jest.mock("../../utils/mock", () => ({
  mock: {
    TEST: {
      RETORNO: [
        { DOCUMENTO: "DOC123", ITEM: "001", TIPO: "Material", VALOR: 100 },
        { DOCUMENTO: "DOC123", ITEM: "002", TIPO: "Material", VALOR: 200 },
        { DOCUMENTO: "DOC456", ITEM: "001", TIPO: "Serviço", VALOR: 300 },
      ],
    },
  },
}));

jest.mock("../../utils/UniqueDocs", () => ({
  getUniqueDocs: jest.fn(),
}));

jest.mock("../../hooks/translation", () => ({
  language: "pt-BR",
}));

jest.mock("../../constant", () => ({
  processesProps: jest.fn().mockReturnValue([
    {
      headerColumns: [],
      detailColumns: [],
    },
  ]),
}));

describe("SAP Service Functions", () => {
  // Dados de exemplo para testes
  const mockRow: ISap.ItemProps = {
    DOCUMENTO: "DOC123",
    ITEM: "001",
    TIPO: "Material",
    ADICIONAIS: [{ CAMPO: "TESTE", VALOR: "Valor Teste" }],
    CENTRO: "",
    DATA_EMISSAO: "",
    FORNECEDOR: "",
    MATERIAL: "",
    QTD: "",
    REQUISITANTE: "",
    VLDOC: "",
    VLITEM: "",
    VLUNIT: "",
  };

  const mockDetailData: ISap.ItemProps[] = [
    {
      DOCUMENTO: "DOC123",
      ITEM: "001",
      TIPO: "Material",
      ADICIONAIS: [],
      CENTRO: "",
      DATA_EMISSAO: "",
      FORNECEDOR: "",
      MATERIAL: "",
      QTD: "",
      REQUISITANTE: "",
      VLDOC: "",
      VLITEM: "",
      VLUNIT: "",
    },
    {
      DOCUMENTO: "DOC123",
      ITEM: "002",
      TIPO: "Material",
      ADICIONAIS: [],
      CENTRO: "",
      DATA_EMISSAO: "",
      FORNECEDOR: "",
      MATERIAL: "",
      QTD: "",
      REQUISITANTE: "",
      VLDOC: "",
      VLITEM: "",
      VLUNIT: "",
    },
    {
      DOCUMENTO: "DOC456",
      ITEM: "001",
      TIPO: "Serviço",
      ADICIONAIS: [],
      CENTRO: "",
      DATA_EMISSAO: "",
      FORNECEDOR: "",
      MATERIAL: "",
      QTD: "",
      REQUISITANTE: "",
      VLDOC: "",
      VLITEM: "",
      VLUNIT: "",
    },
  ];

  const mockHeaderData: ISap.ItemProps[] = [
    {
      DOCUMENTO: "DOC123",
      TIPO: "Material",
      VLDOC: "300",
      ADICIONAIS: [],
      CENTRO: "",
      DATA_EMISSAO: "",
      FORNECEDOR: "",
      ITEM: "",
      MATERIAL: "",
      QTD: "",
      REQUISITANTE: "",
      VLITEM: "",
      VLUNIT: "",
    },
    {
      DOCUMENTO: "DOC456",
      TIPO: "Serviço",
      VLDOC: "300",
      ADICIONAIS: [],
      CENTRO: "",
      DATA_EMISSAO: "",
      FORNECEDOR: "",
      ITEM: "",
      MATERIAL: "",
      QTD: "",
      REQUISITANTE: "",
      VLITEM: "",
      VLUNIT: "",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getDocumentDetails", () => {
    it("should fetch document details successfully", async () => {
      // Configurar o mock da API
      (APISap.getDocumentDetails as jest.Mock).mockResolvedValue({
        data: {
          RETORNO: [
            { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
            { DOCUMENTO: "DOC123", ITEM: "002", MATERIAL: "Material Test 2" },
          ],
        },
      });

      // Chamar a função
      const result = await getDocumentDetails("DOC123", "TEST", mockRow);

      // Verificar se a API foi chamada corretamente
      expect(APISap.getDocumentDetails).toHaveBeenCalledWith("DOC123", "TEST");

      // Verificar o retorno
      expect(result).toEqual([
        { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
        { DOCUMENTO: "DOC123", ITEM: "002", MATERIAL: "Material Test 2" },
      ]);
    });

    it("should include row data for specific processes", async () => {
      // Configurar o mock da API
      (APISap.getDocumentDetails as jest.Mock).mockResolvedValue({
        data: {
          RETORNO: [
            { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
          ],
        },
      });

      // Chamar a função com processo VX
      const resultVX = await getDocumentDetails("DOC123", "VX", mockRow);

      // Verificar se o resultado inclui a linha original + dados da API
      expect(resultVX).toEqual([
        mockRow,
        { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
      ]);

      // Teste para processo NC
      const resultNC = await getDocumentDetails("DOC123", "NC", mockRow);
      expect(resultNC).toEqual([
        mockRow,
        { DOCUMENTO: "DOC123", ITEM: "001", MATERIAL: "Material Test" },
      ]);
    });

    it("should handle API errors", async () => {
      // Configurar o mock para lançar erro
      (APISap.getDocumentDetails as jest.Mock).mockRejectedValue(
        new Error("API Error")
      );

      // Espiar console.error
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      // Verificar se a função lança o erro
      await expect(
        getDocumentDetails("DOC123", "TEST", mockRow)
      ).rejects.toThrow("API Error");
      expect(consoleErrorSpy).toHaveBeenCalled();

      // Restaurar console.error
      consoleErrorSpy.mockRestore();
    });
  });

  describe("filterDocumentDetails", () => {
    it("should filter details by document number", () => {
      const result = filterDocumentDetails(mockDetailData, "DOC123");
      expect(result).toHaveLength(2);
      expect(result[0].DOCUMENTO).toBe("DOC123");
      expect(result[1].DOCUMENTO).toBe("DOC123");
    });

    it("should handle empty detail data", () => {
      const result = filterDocumentDetails([], "DOC123");
      expect(result).toEqual([]);
    });

    it("should handle document number with different formats", () => {
      // Teste com espaços e como string vs número
      const mockDataWithSpaces: ISap.ItemProps[] = [
        {
          DOCUMENTO: " DOC123 ",
          ITEM: "001",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        {
          DOCUMENTO: "DOC456",
          ITEM: "001",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
      ];

      const result = filterDocumentDetails(mockDataWithSpaces, "DOC123");
      expect(result).toHaveLength(1);
      expect(result[0].ITEM).toBe("001");
    });
  });

  describe("prepareTableData", () => {
    it("should prepare table data correctly", () => {
      // Configurar mock do getUniqueDocs
      (getUniqueDocs as jest.Mock).mockReturnValue(["DOC123", "DOC456"]);

      const result = prepareTableData(mockHeaderData, [{}]);

      expect(getUniqueDocs).toHaveBeenCalledWith(mockHeaderData);
      expect(result).toHaveLength(2);
      expect(result[0].DOCUMENTO).toBe("DOC123");
      expect(result[1].DOCUMENTO).toBe("DOC456");
    });

    it("should add empty ADICIONAIS if not present", () => {
      // Dados sem o campo ADICIONAIS
      const dataWithoutAdicionais = [
        {
          DOCUMENTO: "DOC123",
          TIPO: "Material",
          VLDOC: "300",
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          ITEM: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          VLITEM: "",
          VLUNIT: "",
          ADICIONAIS: [],
        },
      ];

      (getUniqueDocs as jest.Mock).mockReturnValue(["DOC123"]);

      const result = prepareTableData(dataWithoutAdicionais, [{}]);

      expect(result[0].ADICIONAIS).toEqual([]);
    });

    it("should handle empty header data", () => {
      (getUniqueDocs as jest.Mock).mockReturnValue([]);
      const result = prepareTableData([], [{}]);
      expect(result).toEqual([]);
    });

    it("should handle missing headerColumns", () => {
      (getUniqueDocs as jest.Mock).mockReturnValue(["DOC123", "DOC456"]);
      const result = prepareTableData(mockHeaderData, []);
      expect(result).toEqual(mockHeaderData);
    });
  });

  describe("createApprovalParams", () => {
    it("should create approval params with item for item approval", () => {
      const result = createApprovalParams(
        mockRow,
        "A",
        undefined,
        "TEST",
        true
      );

      expect(result).toEqual({
        document: "DOC123",
        item: "001",
        status: "A",
        additional1: "",
        additional2: "",
        reason: "",
        language: "pt-BR",
        process: "TEST",
        ecp: false,
      });
    });

    it("should create approval params without item for document approval", () => {
      const result = createApprovalParams(
        mockRow,
        "R",
        "Reason Test",
        "TEST",
        false
      );

      expect(result).toEqual({
        document: "DOC123",
        item: "",
        status: "R",
        additional1: "",
        additional2: "",
        reason: "Reason Test",
        language: "pt-BR",
        process: "TEST",
        ecp: false,
      });
    });

    it("should handle ECP field correctly", () => {
      const rowWithECP = { ...mockRow, ECP: true };
      const result = createApprovalParams(rowWithECP, "A", "", "TEST", false);

      expect(result.ecp).toBe(true);
    });
  });

  describe("updateDataAfterApproval", () => {
    const mockExecute = jest.fn();

    it("should update header and detail data after item approval", async () => {
      const result = await updateDataAfterApproval(
        {
          DOCUMENTO: "DOC123",
          ITEM: "001",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        "TEST",
        true,
        mockDetailData,
        mockHeaderData,
        mockExecute
      );

      expect(result.updatedDetailData).toHaveLength(2);
      // Verifica se o item aprovado foi removido
      expect(
        result.updatedDetailData.find(
          (item) => item.DOCUMENTO === "DOC123" && item.ITEM === "001"
        )
      ).toBeUndefined();
    });

    it("should update header when approving last item of a document", async () => {
      // Apenas um item restante para DOC123
      const lastItemDetailData = [
        {
          DOCUMENTO: "DOC123",
          ITEM: "001",
          TIPO: "Material",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        {
          DOCUMENTO: "DOC456",
          ITEM: "001",
          TIPO: "Serviço",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
      ];

      const result = await updateDataAfterApproval(
        {
          DOCUMENTO: "DOC123",
          ITEM: "001",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        "TEST",
        true,
        lastItemDetailData,
        mockHeaderData,
        mockExecute
      );

      // Verifica se o cabeçalho foi removido
      expect(
        result.updatedHeaderData.find((item) => item.DOCUMENTO === "DOC123")
      ).toBeUndefined();
    });

    it("should update header and detail data after document approval", async () => {
      const result = await updateDataAfterApproval(
        {
          DOCUMENTO: "DOC123",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          ITEM: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        "TEST",
        false,
        mockDetailData,
        mockHeaderData,
        mockExecute
      );

      // Verifica se o documento foi removido do cabeçalho
      expect(
        result.updatedHeaderData.find((item) => item.DOCUMENTO === "DOC123")
      ).toBeUndefined();

      // Verifica se todos os itens do documento foram removidos dos detalhes
      expect(
        result.updatedDetailData.find((item) => item.DOCUMENTO === "DOC123")
      ).toBeUndefined();
    });

    it("should handle empty response from API", async () => {
      // Modificar o mock para retornar uma lista vazia
      const originalMock = mock;
      Object.defineProperty(mock, "TEST", {
        value: { RETORNO: [] },
        writable: true,
      });

      const result = await updateDataAfterApproval(
        {
          DOCUMENTO: "DOC123",
          ADICIONAIS: [],
          CENTRO: "",
          DATA_EMISSAO: "",
          FORNECEDOR: "",
          ITEM: "",
          MATERIAL: "",
          QTD: "",
          REQUISITANTE: "",
          TIPO: "",
          VLDOC: "",
          VLITEM: "",
          VLUNIT: "",
        },
        "TEST",
        false,
        mockDetailData,
        mockHeaderData,
        mockExecute
      );

      expect(result.updatedHeaderData).toEqual([]);
      expect(result.updatedDetailData).toEqual([]);

      // Restaurar o mock
      Object.defineProperty(mock, "TEST", {
        value: originalMock,
        writable: true,
      });
    });

    it("should handle errors during update", async () => {
      // Configurar o mockExecute para lançar erro
      mockExecute.mockRejectedValueOnce(
        new Error("Error updating data after approval")
      );

      // Mock da função mock para garantir que o erro seja propagado
      const originalMock = mock;
      Object.defineProperty(mock, "TEST", {
        get: () => {
          throw new Error("Error updating data after approval");
        },
      });

      // Espiar console.error
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      // Use expect-async para testar rejeição
      await expect(
        updateDataAfterApproval(
          {
            DOCUMENTO: "DOC123",
            ADICIONAIS: [],
            CENTRO: "",
            DATA_EMISSAO: "",
            FORNECEDOR: "",
            ITEM: "",
            MATERIAL: "",
            QTD: "",
            REQUISITANTE: "",
            TIPO: "",
            VLDOC: "",
            VLITEM: "",
            VLUNIT: "",
          },
          "TEST",
          false,
          mockDetailData,
          mockHeaderData,
          mockExecute
        )
      ).rejects.toThrow("Error updating data after approval");

      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();

      // Restaurar o mock
      Object.defineProperty(mock, "TEST", {
        value: originalMock,
        writable: true,
      });
    });
  });
});
