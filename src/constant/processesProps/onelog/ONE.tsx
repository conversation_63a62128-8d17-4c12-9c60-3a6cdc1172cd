// import { t } from "i18next"; // Não utilizado
import ReasonSubReasonModalTr from "@/components/DataTableOneLog/components/ReasonSubReasonModalTr";
import { t } from "i18next";
import {
  AprovacoesProps,
  ClientProps,
  ItemDetailProps,
} from "../../../interfaces/onelog";

export const ONE = {
  title: "Onelog",
  origin: "ONELOG",
  type: "ONE",
  permission: "ONE",
  hasDetailModal: true,
  approveItems: true,
  headerColumns: [
    {
      name: "Client",
      selector: (row: ClientProps) => row.nome || "-",
    },
    {
      name: "Number.Daily",
      selector: (row: ClientProps) => row.aprovacoes.length || "-",
    },
  ],
  detailColumns: [
    {
      name: "Trip.Number",
      selector: (row: AprovacoesProps) => row.numeroViagem || "-",
    },
    {
      name: "Request.Date",
      selector: (row: AprovacoesProps) => row.dataSolicitacao || "-",
    },
    {
      name: "Reason.Daily",
      selector: (row: AprovacoesProps) => row.motivo || "-",
    },
    {
      name: "Delivery.Weight",
      selector: (row: AprovacoesProps) => row.pesoEntrega || "-",
    },
    {
      name: "Time.Daily",
      selector: (row: AprovacoesProps) => row.tempoEmDiaria || "-",
    },
    {
      name: "Vehicle.Plate",
      selector: (row: AprovacoesProps) => row.placa || "-",
    },
  ],
  detailModalHeader: (row: ItemDetailProps | ItemDetailProps[]) => {
    const itemDetail = Array.isArray(row) ? (row[0] as ItemDetailProps) : row;

    return (
      <>
        <tbody>
          <tr>
            <td>
              <div className="tableTitle">{`${t("Item.OneLog")}:`}</div>
              {itemDetail?.idDiaria || "-"}
            </td>
            <td>
              <div className="tableTitle">{`${t("Request.Of.Date.AF")}:`}</div>
              {itemDetail?.dataSolicitacao || "-"}
            </td>
            <td>
              <div className="tableTitle">{`${t("Travel")}:`}</div>
              {itemDetail?.numeroViagem || "-"}
            </td>
          </tr>
          <tr>
            <td>
              <div className="tableTitle">
                {`${t("Customer.Responsibility")}:`}
              </div>
              {itemDetail?.isClienteResponsavel || "-"}
            </td>
            <td>
              <div className="tableTitle">{`${t("Status")}:`}</div>
              {itemDetail?.status || "-"}
            </td>
            <td>
              <div className="tableTitle">{`${t("Travel.Delivery")}:`}</div>
              {itemDetail?.entregaViagem || "-"}
            </td>
          </tr>
          <tr>
            <td>
              <span className="tableTitle">{`${t("Delivery.Weight")}:`}</span>
              {itemDetail?.pesoEntrega || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("Time.Daily")}:`}</span>
              {itemDetail?.tempoEmDiaria || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("Vehicle.Plate")}:`}</span>
              {itemDetail?.placaVeiculo || "-"}
            </td>
          </tr>
          <tr>
            <td>
              <span className="tableTitle">{`${t("Client.Name")}:`}</span>
              {itemDetail?.nomeCliente || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("Category")}:`}</span>
              {itemDetail?.categoria || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("Estimated.Cost")}:`}</span>
              {itemDetail?.custoEstimado || "-"}
            </td>
          </tr>
          <tr>
            <td>
              <span className="tableTitle">
                {`${t("Name.Responsible.Client")}:`}
              </span>
              {itemDetail?.nomeClienteResponsavel || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("Product.OneLog")}:`}</span>
              {itemDetail?.produto || "-"}
            </td>
            <td>
              <span className="tableTitle">
                {`${t("Delivery.Forecast.OneLog")}:`}
              </span>
              {itemDetail?.dataPrevisaoEntrega || "-"}
            </td>
          </tr>
          <tr>
            <td>
              <span className="tableTitle">{`${t("District")}:`}</span>
              {itemDetail?.clienteBairro || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("City")}:`}</span>
              {itemDetail?.clienteCidade || "-"}
            </td>
            <td>
              <span className="tableTitle">{`${t("State")}:`}</span>
              {itemDetail?.clienteUF || "-"}
            </td>
          </tr>
        </tbody>

        <ReasonSubReasonModalTr row={itemDetail} />

        <tr>
          <td>
            <h3>{t("Monitoring.Justification")}</h3>
            <br />
            {itemDetail?.justificativa || "-"}
          </td>
        </tr>
      </>
    );
  },
};
