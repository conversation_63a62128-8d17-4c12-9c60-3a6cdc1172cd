import { css, keyframes } from "@emotion/react";
import styled from "@emotion/styled";

const loadRings = keyframes`
 from{
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;
const cardEnter = keyframes`
 from{
    transform: translateX(-35px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;
interface ICardContentProps {
  position?: number;
}

const animationDelay = (props: any) =>
  props.position &&
  css`
    animation-delay: ${props.position * 80}ms;
  `;

export const CardContent = styled.div<ICardContentProps>`
  background: #fff;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.21);
  border-radius: 8px;
  position: relative;
  opacity: 0;
  padding: 16px 16px 16px 8px;
  transition: margin 400ms, height 400ms, opacity 100ms, padding 400ms;
  animation: ${cardEnter} 300ms ease-in forwards;

  ${animationDelay}

  &.removing {
    margin: 0px;
    height: 0px;
    opacity: 0;
    overflow: hidden;
    padding-top: 0;
    padding-bottom: 0;
    & > div {
      opacity: 0;
    }
  }
`;

export const CardHeader = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;

  margin: -16px -8px;
  padding: 16px;
  width: calc(100% + 25px);
  transition: 300ms;
  min-height: 64px;

  h3 {
    margin-left: 18px;
    font-family: "CoTextCorp";
    color: #812990;
    font-weight: bold;
    font-size: 20px;
    @media (max-width: 748px) {
      font-size: 14px;
    }
    @media (max-width: 1199px) {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 18px;
    }
  }
  > div:last-child {
    margin-left: auto;
  }
`;

export const Loading = styled.div`
  display: inline-block;
  position: absolute;
  width: 40px;
  height: 40px;
  top: 10px;
  left: 5px;
  background: white;
  z-index: 1;
  div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 30px;
    height: 30px;
    margin: 6px;
    border: 3px solid #812990;
    border-radius: 50%;
    animation: ${loadRings} 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #812990 transparent transparent transparent;
    &:nth-of-type(1) {
      animation-delay: -0.45s;
    }
    &:nth-of-type(2) {
      animation-delay: -0.3s;
    }
    &:nth-of-type(3) {
      animation-delay: -0.15s;
    }
  }
`;

export const Counter = styled.div`
  width: 32px;
  height: 32px;
  background: #812990;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
  font-weight: 700;
  font-size: clamp(75%, 16px, 75%);
  padding: 2px;
  &.hidden {
    display: none;
  }
`;

export const CardBody = styled.div`
  display: none;
  overflow-x: auto;
  /* @media(max-width: 1199px){ */
  padding-left: 8px;
  /* } */
  &.show {
    display: block;
  }
`;

export const CardHeaderToggle = styled.div`
  display: flex;
  align-items: center;
  svg {
    cursor: pointer;
    margin-left: 18px;
    transition: 300ms;
    &.show {
      transform: rotate(180deg);
    }
    @media (max-width: 1199px) {
      margin-left: 8px;
    }
  }
`;

export const Table = styled.table`
  width: 100%;
  border-collapse: collapse !important;

  & .divAdditional {
    display: flex;
    justify-content: center;
    align-items: center;

    @media (max-width: 748px) {
      justify-content: start;
      display: block;
    }
  }

  & > thead {
    > tr {
      > td {
        font-weight: 700;
        padding: 8px;
        font-size: 14px;

        &:first-of-type {
          > div {
            display: none;
          }
        }
      }
    }
    @media (max-width: 748px) {
      display: none;
    }
  }
  & > tbody {
    > tr {
      transition: 300ms;
      &:hover {
        transition: 300ms;
        background: #f5f5f5;
      }
      cursor: pointer;
      &.selected {
        background: #f5f5f5;
        > td:first-of-type {
          border-top-left-radius: 10px;
        }
        > td:last-child {
          border-top-right-radius: 10px;
        }
      }
      &[data-hidden] {
        display: none;
      }

      @media (max-width: 748px) {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        flex: 1;
        /* box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2); */
        padding: 14px 8px 0;
        border-top: 1px solid #c2c2c28f;
        margin: 12px 0 0 0;
      }
      > td {
        padding: 8px;
        font-size: 14px;
        @media (max-width: 748px) {
          position: relative;
          flex: 1 0 50%;
          padding: 12px 4px;
          align-self: flex-start;
        }
        &:before {
          @media (max-width: 748px) {
            content: attr(data-head);
            /* position: absolute; */
            font-weight: 600;
            top: -4px;
            left: 0;
            display: block;
          }
        }
        &:first-of-type {
          &.flex50 {
            flex: 1 0 50%;
          }
          flex: 0 0 38px;
        }
        &.noCheckbox :first-of-type {
          @media (max-width: 748px) {
            flex: 1 0 50%;
          }
        }
        &[data-highlight] {
          background: #ffe183;
        }
      }
    }
  }
`;

export const ButtonsTd = styled.td`
  @media (max-width: 748px) {
    flex: 1 0 100% !important;
  }

  &.justification-icons {
    @media (min-width: 1024px) {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  > div {
    //container
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  @media (max-width: 748px) {
    &.justification-icons {
      > div {
        justify-content: flex-start;
      }
    }
  }

  > div > div {
    //icons FI inside a div
    position: relative;
    &:before {
      content: attr(data-translation);
      position: absolute;
      bottom: calc(100% + 5px);
      left: 50%;
      transform: translateX(-80%);
      background: #000;
      color: #fff;
      padding: 4px 8px;
      border-radius: 10px;
      opacity: 0;
      visibility: hidden;
      transition: 300ms;
      white-space: nowrap;
      pointer-events: none;
    }
    &::after {
      content: "";
      border-style: solid;
      border-color: #000 transparent;
      border-width: 6px 6px 0 6px;
      bottom: 100%;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      opacity: 0;
      visibility: hidden;
      transition: 300ms;
      pointer-events: none;
      white-space: nowrap;
    }
    &:hover {
      &::after,
      &::before {
        transition: 300ms;
        opacity: 1;
        visibility: visible;
      }
    }
  }

  button,
  svg,
  img,
  a {
    margin-left: 16px;
    &:before {
      content: attr(data-translation);
      position: absolute;
      bottom: calc(100% + 5px);
      left: 50%;
      transform: translateX(-80%);
      background: #000;
      color: #fff;
      padding: 4px 8px;
      border-radius: 10px;
      opacity: 0;
      visibility: hidden;
      white-space: nowrap;
      transition: 300ms;
      pointer-events: none;
    }
    &::after {
      content: "";
      border-style: solid;
      border-color: #000 transparent;
      border-width: 6px 6px 0 6px;
      bottom: 100%;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      opacity: 0;
      white-space: nowrap;
      visibility: hidden;
      transition: 300ms;
      pointer-events: none;
    }
    &:hover {
      &::after,
      &::before {
        transition: 300ms;
        opacity: 1;
        visibility: visible;
      }
    }
  }

  button {
    transition: 300ms;
    cursor: pointer;
    &:hover {
      transform: scale(1.1);
    }
  }
  svg {
    transition: 300ms;
    cursor: pointer;

    &:hover {
      transform: scale(1.1);
    }
    &.show {
      transform: rotate(180deg);
      &:hover {
        transform: rotate(180deg) scale(1.1);
      }
    }
  }
`;

export const TrCollapse = styled.tr`
  border-top: none !important;
  margin: 0 !important;

  > td {
    .additionalFreight {
      color: #812990;
      font-size: 15px !important;
      padding: 0 !important;
      font-weight: 700;

      > span {
        color: #000;
        font-weight: normal;
        font-size: 14px;
      }
    }

    padding: 0 !important;
    border-top: none;

    @media (max-width: 748px) {
      display: flex;
      flex: 1 !important;
    }
  }
  @media (max-width: 748px) {
    padding: 0 !important;
  }
  &.selected {
    background: #f5f5f5;
    td {
      padding: 8px;
      font-size: 14px;
      /* &:first-of-type {
        @media (max-width: 748px) {
          flex: 0 0 48px;
        }
      } */
      @media (max-width: 748px) {
        position: relative;
        flex: 1 0 50%;
        padding: 12px 0;
        align-self: flex-start;
      }
      &:before {
        @media (max-width: 748px) {
          content: attr(data-head);
          /* position: absolute; */
          font-weight: 600;
          top: -4px;
          left: 0;
          display: block;
        }
      }
    }
    td:first-of-type {
      border-top-left-radius: 0 !important; //fix to remove radius on top
      border-bottom-left-radius: 10px;
    }
    td:last-child {
      border-top-right-radius: 0 !important; //fix to remove radius on top
      border-bottom-right-radius: 10px;
    }
    @media (max-width: 748px) {
      margin-top: -44px;
    }
  }
`;

export const DivCollapse = styled.div`
  display: none;
  padding: 0 16px 16px;
  width: 100%;

  &.paddingNone {
    padding: 0;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  &.show {
    display: block;
    background-color: #f5f5f5;
  }
  h3 {
    color: #812990;
    padding: 8px 0;
  }
  h4 {
    padding: 8px 0;
    border-bottom: 1px solid #c2c2c273;
  }
`;

export const TableInternal = styled.table`
  font-family: Roboto, sans-serif;
  width: 100%;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
  line-height: 160%;

  border-collapse: collapse;

  &.noMargin {
    margin-top: 0;
  }

  &.processNF {
    margin: 25px 0;
  }

  .paddingLeftTable {
    padding-left: 20px;

    @media (max-width: 748px) {
      padding-left: 0;
    }
  }

  @media (max-width: 748px) {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
  }
  > thead {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.54);
    line-height: 160%;

    @media (max-width: 748px) {
      display: none;
    }
  }
  > tbody {
    font-size: 16px;
    line-height: 160%;
    color: #000000;

    @media (max-width: 748px) {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }
    > tr {
      @media (max-width: 748px) {
        display: flex;
        flex: 1 0 100%;
        flex-wrap: wrap;
        border-radius: 10px;
        padding: 10px;
        background: #fff;

        &.alignTable {
          width: 100%;
          display: inline-table;
        }

        &.withBackground {
          background: #f5f5f5;
        }
      }

      &:last-child > td > div {
        @media (min-width: 768px) {
          padding-bottom: 10px;
        }
      }

      td {
        > div {
          padding-top: 10px;

          &[data-tooltip] {
            position: relative;
            cursor: pointer;
            &:before {
              content: attr(data-tooltip);
              position: absolute;

              @media (min-width: 748px) {
                bottom: calc(100% + 5px);
              }
              @media (max-width: 748px) {
                height: auto;
                width: 51px;
                white-space: pre-line;
                word-break: break-all;
                left: 60%;
                top: 0;
              }

              left: 50%;
              transform: translateX(-80%);
              background: #000;
              color: #fff;
              padding: 4px 8px;
              text-align: center;
              border-radius: 10px;
              opacity: 0;
              visibility: hidden;
              transition: 300ms;
              pointer-events: none;
              white-space: nowrap;
              font-family: "Roboto", sans-serif;
              font-weight: 400;
              font-size: 14px;
            }
            @media (min-width: 748px) {
              &::after {
                content: "";
                border-style: solid;
                border-color: #000 transparent;
                border-width: 6px 6px 0 6px;
                bottom: 100%;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                opacity: 0;
                visibility: hidden;
                transition: 300ms;
                pointer-events: none;
              }
            }

            &:hover {
              &::after,
              &::before {
                transition: 300ms;
                opacity: 1;
                visibility: visible;
              }
            }
          }
        }

        &.costCenter {
          max-width: 10ch;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.textSGTXT {
          max-width: 50ch;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        @media (max-width: 748px) {
          flex: 1 0 50%;
          padding-right: 8px !important;
        }
        &.noCheckbox {
          @media (max-width: 748px) {
            flex: 1 0 50%;
          }
        }
        &:before {
          @media (max-width: 748px) {
            content: attr(data-head);
            /* position: absolute; */
            font-weight: 600;
            top: -4px;
            left: 0;
            display: block;
          }
        }
      }
      & + tr:not(.tableAptus) {
        border-top: 4px solid #f5f5f5;
      }
    }
  }

  @media (min-width: 1024px) {
    tr {
      border-top: 0 !important;
    }

    .borderLeft-row {
      border-left: 1px solid rgba(0, 0, 0, 0.12);
      margin-top: 0px;
      padding-left: 32px !important;
    }

    .thead-mg {
      border-top: 50px solid transparent;
    }
  }

  @media (max-width: 768px) {
    .adjustMobile {
      flex: 1 0 100% !important;
    }

    .m-top-mobile tr {
      border-top: 8px solid #ffff;
      border-radius: 0;
    }

    .m-top-mobile tr:first-of-type {
      border-top: 2px solid #f5f5f5;
    }

    tr.no-radius {
      border-top: 0px solid #fff !important;
      border-radius: 0;
    }

    tr:not(.tableAptus) {
      border-top: 4px solid #f5f5f5;
    }
    .thead-mg {
      border-top: 50px solid transparent;
    }
  }
`;

export const ApprovalIconButton = styled.button`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg fill='none' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 12C0 5.3719 5.3719 0 12 0s12 5.3719 12 12-5.3719 12-12 12S0 18.6281 0 12zm9.675 4.807l8.4258-8.4539c.3281-.328.3281-.8625-.0024-1.193-.3304-.328-.8648-.328-1.1929.0024l-7.6758 7.6992-2.0016-3.5039c-.2297-.4054-.7453-.5461-1.1508-.314-.4054.2297-.546.7453-.314 1.1507l2.5523 4.4625c.1547.2743.4406.4266.7336.4266a.8413.8413 0 00.4172-.1102.8157.8157 0 00.1992-.157l.0094-.0094z' fill='%23389243'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  position: relative;
`;
export const ReprovalIconButton = styled.button`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg fill='none' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 12C0 5.3719 5.3719 0 12 0s12 5.3719 12 12-5.3719 12-12 12S0 18.6281 0 12zm16.8094 4.8234c.3305-.3281.3305-.8625.0023-1.1929L13.1906 12l3.6211-3.6328c.3282-.3305.3282-.8649-.0023-1.193-.3305-.3281-.8648-.3281-1.193.0023L12 10.8048 8.3836 7.1789c-.3281-.3305-.8625-.3305-1.193-.0023-.3304.328-.3304.8624-.0023 1.193L10.8094 12l-3.621 3.6305c-.3282.3304-.3282.8648.0023 1.1929a.8391.8391 0 00.5953.2461.8373.8373 0 00.5976-.2484L12 13.1953l3.6164 3.6258a.8375.8375 0 00.5977.2484.8392.8392 0 00.5953-.2461z' fill='%23E31F26'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
`;
export const SearchIconButton = styled.button`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' %3F%3E%3Csvg enable-background='new 0 0 32 32' id='Editable-line' version='1.1' viewBox='0 0 32 32' xml:space='preserve' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ccircle cx='14' cy='14' fill='none' id='XMLID_42_' r='9' stroke='%23000000' stroke-linecap='round' stroke-linejoin='round' stroke-miterlimit='10' stroke-width='2'/%3E%3Cline fill='none' id='XMLID_44_' stroke='%23000000' stroke-linecap='round' stroke-linejoin='round' stroke-miterlimit='10' stroke-width='2' x1='27' x2='20.366' y1='27' y2='20.366'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 16px;
`;
export const PositiveHandIconButton = styled.button`
  width: 24px;
  height: 24px;
  color: red;
  background-image: url("data:image/svg+xml,%3Csvg width='22' height='20' viewBox='0 0 22 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 20H17C17.83 20 18.54 19.5 18.84 18.78L21.86 11.73C21.95 11.5 22 11.26 22 11V9C22 7.9 21.1 7 20 7H13.69L14.64 2.43L14.67 2.11C14.67 1.7 14.5 1.32 14.23 1.05L13.17 0L6.58 6.59C6.22 6.95 6 7.45 6 8V18C6 19.1 6.9 20 8 20ZM8 8L12.34 3.66L11 9H20V11L17 18H8V8ZM0 8H4V20H0V8Z' fill='black' fill-opacity='0.38'/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 0 !important;
`;
export const HandsIconButton = styled.button`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='20' viewBox='0 0 22 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 20.3574H17C17.83 20.3574 18.54 19.8574 18.84 19.1374L21.86 12.0874C21.95 11.8574 22 11.6174 22 11.3574V9.35742C22 8.25742 21.1 7.35742 20 7.35742H13.69L14.64 2.78742L14.67 2.46742C14.67 2.05742 14.5 1.67742 14.23 1.40742L13.17 0.357422L6.58 6.94742C6.22 7.30742 6 7.80742 6 8.35742V18.3574C6 19.4574 6.9 20.3574 8 20.3574ZM8 8.35742L12.34 4.01742L11 9.35742H20V11.3574L17 18.3574H8V8.35742ZM0 8.35742H4V20.3574H0V8.35742Z' fill='%235AC980'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 0 !important;
  margin-top: -6px;
`;
export const InactiveHandIconButton = styled.button`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 0H5c-.83 0-1.54.5-1.84 1.22L.14 8.27C.05 8.5 0 8.74 0 9v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L8.83 20l6.58-6.59c.37-.36.59-.86.59-1.41V2c0-1.1-.9-2-2-2Zm0 12-4.34 4.34L10.77 11H2V9l3-7h9v10Zm4-12h4v12h-4V0Z' fill='%23F04E23'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 0;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 0 !important;
`;
export const PriceInsideTableColumn = styled.td`
  display: flex;
  flex-direction: column;

  &#expandedColumn {
    @media (max-width: 748px) {
      display: flex;
      // flex-direction: column;
      width: 100% !important;
    }
  }
`;
export const PriceInsideTableRow = styled.td`
  display: flex;
  flex-direction: row;
  gap: 8px;
  margin-top: 5px;
  align-items: flex-start;
`;

export const OpenNewTabIconButton = styled.a`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg stroke='currentColor' fill='none' stroke-width='2' viewBox='0 0 24 24' stroke-linecap='round' stroke-linejoin='round' height='24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6'/%3E%3Cpolyline points='15 3 21 3 21 9'/%3E%3Cline x1='10' y1='14' x2='21' y2='3'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 16px;
  position: relative;
  &::before {
    white-space: nowrap;
  }
`;
export const AttachmentIconButton = styled.a`
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg stroke='currentColor' fill='none' stroke-width='2' viewBox='0 0 24 24' stroke-linecap='round' stroke-linejoin='round' height='24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z'/%3E%3Cpolyline points='13 2 13 9 20 9'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  margin-left: 16px;
`;

interface AtentionDivergencyIconButtonProps {
  isRed?: boolean;
}

export const AtentionDivergencyIconButton = styled.button<AtentionDivergencyIconButtonProps>`
  width: 24px;
  height: 24px;
  background-image: ${(props) =>
    props.isRed
      ? "url(\"data:image/svg+xml,%3Csvg width='24' height='21' viewBox='0 0 24 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 20.7273H24L12 0L0 20.7273ZM13.0909 17.4545H10.9091V15.2727H13.0909V17.4545ZM13.0909 13.0909H10.9091V8.72727H13.0909V13.0909Z' fill='%23FF0000'/%3E%3C/svg%3E%0A\")"
      : "url(\"data:image/svg+xml,%3Csvg width='24' height='21' viewBox='0 0 24 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 20.7273H24L12 0L0 20.7273ZM13.0909 17.4545H10.9091V15.2727H13.0909V17.4545ZM13.0909 13.0909H10.9091V8.72727H13.0909V13.0909Z' fill='%23F58220'/%3E%3C/svg%3E%0A\")"};
  background-repeat: no-repeat;
  background-position: center;
  outline: none;
  background-color: transparent;
  border: none;
  transition: 300ms;
  cursor: pointer;
  &:hover {
    transform: scale(1.1);
  }
`;

export const ModalTable = styled.table``;

export const ModalTableCell = styled.td`
  height: 50px;
  padding: 8px;

  .justification-line {
    align-items: center;
    width: 100%;
    display: flex;
    margin-bottom: 14px;

    align-items: flex-start;
    margin-left: 0;
    margin-right: 9px;
    margin-top: 4px;

    button {
      margin-right: 5px;
    }
  }

  &.jrProcess {
    width: 392px;
    @media (max-width: 768px) {
      width: auto;
    }
  }

  @media (max-width: 768px) {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;

    button {
      width: 32px;
    }

    &.empty {
      display: none;
    }
  }

  &.afProcess {
    display: table-cell;
    width: 382px;

    @media (max-width: 748px) {
      width: auto;
    }
  }

  &.afProcessDetailsTable {
    @media (min-width: 749px) {
      width: 25%;
    }

    @media (max-width: 748px) {
      width: auto;
    }
  }
`;

export const ModalTableCellTitle = styled.td`
  background: #fff;
  table {
    margin-top: 0px;
  }
`;

export const ControlButtonsLeft = styled.div`
  display: flex;
  flex: 1;
  column-gap: 16px;
  justify-content: flex-start;
  padding: 0px 0px 0px;
`;

export const ControlButtonsRight = styled.div`
  display: flex;
  flex: 1;
  /* column-gap: 16px; */
  justify-content: flex-end;
  padding: 0px 20px 0px 0px;
  > button {
    margin-left: 16px;
  }
  @media (max-width: 1199px) {
    padding: 0;
    justify-content: space-between;
    flex-direction: row-reverse;
    > button {
      margin: 0;
      min-width: 120px;
      justify-content: center;
    }
  }
`;

export const InTable = styled.table`
  width: 100%;
  flex: 1;
  margin-top: 25px;
  tr:nth-of-type(even) {
    background-color: #f2f2f2;
  }

  &.processNF {
    margin: 25px 0;
  }

  .paddingLeftTable {
    @media (max-width: 748px) {
      padding-left: 0;
    }
  }

  @media (max-width: 748px) {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
  }
  > thead {
    @media (max-width: 748px) {
      display: none;
    }
  }
  > tbody {
    @media (max-width: 748px) {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
    }
    > tr {
      @media (max-width: 748px) {
        &.alignTable {
          width: 100%;
          display: inline-table;
        }

        &.withBackground {
          background: #f5f5f5;
        }
      }

      &:last-child > td > div {
        @media (min-width: 768px) {
          padding-bottom: 10px;
        }
      }

      td {
        > div {
          padding-top: 10px;

          &[data-tooltip] {
            position: relative;
            cursor: pointer;
            &:before {
              content: attr(data-tooltip);
              position: absolute;

              @media (min-width: 748px) {
                bottom: calc(100% + 5px);
              }
              @media (max-width: 748px) {
                height: auto;
                width: 51px;
                white-space: pre-line;
                word-break: break-all;
                left: 60%;
                top: 0;
              }

              left: 50%;
              transform: translateX(-80%);
              background: #000;
              color: #fff;
              padding: 4px 8px;
              text-align: center;
              border-radius: 10px;
              opacity: 0;
              visibility: hidden;
              transition: 300ms;
              pointer-events: none;
              white-space: nowrap;
              font-family: "Roboto", sans-serif;
              font-weight: 400;
              font-size: 14px;
            }
            @media (min-width: 748px) {
              &::after {
                content: "";
                border-style: solid;
                border-color: #000 transparent;
                border-width: 6px 6px 0 6px;
                bottom: 100%;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                opacity: 0;
                visibility: hidden;
                transition: 300ms;
                pointer-events: none;
              }
            }

            &:hover {
              &::after,
              &::before {
                transition: 300ms;
                opacity: 1;
                visibility: visible;
              }
            }
          }
        }

        &.costCenter {
          max-width: 10ch;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.textSGTXT {
          max-width: 50ch;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        @media (max-width: 748px) {
          flex: 1 0 50%;
          padding-right: 8px !important;
        }
        &.noCheckbox {
          @media (max-width: 748px) {
            flex: 1 0 50%;
          }
        }
        &:before {
          @media (max-width: 748px) {
            content: attr(data-head);
            /* position: absolute; */
            font-weight: 600;
            top: -4px;
            left: 0;
            display: block;
          }
        }
      }
      & + tr:not(.tableAptus) {
        border-top: 4px solid #f5f5f5;
      }
    }
  }

  @media (min-width: 1024px) {
    tr {
      border-top: 0 !important;
    }

    .borderLeft-row {
      border-left: 1px solid rgba(0, 0, 0, 0.12);
      margin-top: 0px;
      padding-left: 32px !important;
    }

    .thead-mg {
      border-top: 50px solid transparent;
    }
  }

  @media (max-width: 768px) {
    .adjustMobile {
      flex: 1 0 100% !important;
    }

    .m-top-mobile tr {
      border-top: 8px solid #ffff;
      border-radius: 0;
    }

    .m-top-mobile tr:first-of-type {
      border-top: 2px solid #f5f5f5;
    }

    tr.no-radius {
      border-top: 0px solid #fff !important;
      border-radius: 0;
    }

    tr:not(.tableAptus) {
      border-top: 4px solid #f5f5f5;
    }
    .thead-mg {
      border-top: 50px solid transparent;
    }
  }
`;

export const Inthead = styled.thead`
  color: #f49a44;
  tr th {
    /* border-bottom: 1px solid #000000; */
    text-align: left;
    height: 25px;

    &.thCenter {
      text-align: center;
    }
  }

  &.pd-left th {
    padding-left: 8px;
  }
`;

export const TrBorder = styled.tr`
  line-height: 160%;
  border-bottom: 1px solid #d8d8d8;
  display: table;
  width: 100%;
`;

export const TrAvipam = styled.tr`
  display: flex;
  padding: 0 8px;
`;

export const TrBackground = styled.tr`
  @media (min-width: 768px) {
    background-color: #f5f5f5;

    > td:first-of-type {
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }
    > td:last-child {
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
`;

export const TableBackground = styled.tr`
  @media (min-width: 768px) {
    background-color: #f5f5f5;

    &.top {
      > td:first-of-type {
        /* border-bottom-left-radius: 8px; */
        border-top-left-radius: 8px;
      }
      > td:last-child {
        /* border-top-right-radius: 8px; */
        border-top-right-radius: 8px;
      }
    }

    &.bottom {
      > td:first-of-type {
        /* border-bottom-left-radius: 8px; */
        border-bottom-left-radius: 8px;
      }
      > td:last-child {
        /* border-top-right-radius: 8px; */
        border-bottom-right-radius: 8px;
      }
    }
  }
`;

export const ModalTableCellNF = styled.td`
  padding: 10px;
`;

export const TdAvipam = styled.td`
  text-align: left;
  width: 15%;
  padding-bottom: 23px;
  margin-right: 5px;

  &#minorColumn {
    width: 9%;
  }
`;

export const DivColumnNone = styled(TdAvipam)`
  @media (max-width: 748px) {
    display: none;
  }
`;

export const DivAvipam = styled.div<{ indexHeader: number }>`
  display: ${(p) => (p.indexHeader !== 0 ? "none" : "")};
  text-align: left;
  font-size: 14px;
  line-height: 160%;
  padding: 14px 0 8px;
  color: rgba(0, 0, 0, 0.54);
  font-weight: 400;

  @media (max-width: 748px) {
    width: 100%;
    display: block;
  }
`;

export const Inttitle = styled.h4`
  font-size: 14px;
  line-height: 160%;
  color: rgba(0, 0, 0, 0.54);
  // padding: 26px 0 16px 8px;
  text-align: left;
  font-weight: 400;

  @media (min-width: 768px) {
    &.paddingLeftTable {
      padding-left: 20px;
      padding-top: 16px;
      font-weight: bold;
      color: #000000;
      display: flex;
      gap: 40px;
    }
  }

  @media (max-width: 748px) {
    padding: 26px 0 0;

    &.paddingLeftTable {
      font-weight: bold;
      color: #000000;
      display: flex;
      gap: 40px;
    }
  }
`;

export const Intbody = styled.tbody`
  font-size: 14px;

  > tr {
    @media (max-width: 768px) {
      border-top: 0;
    }
  }

  .trDetailsNone {
    @media (max-width: 748px) {
      display: none;
    }
  }

  &.borderBottom {
    border-bottom: 1px solid rgba(197, 184, 184, 0.54);
  }

  &.withBackground {
    > tr:first-of-type {
      background-color: #f5f5f5;

      > td:first-of-type {
        border-top-left-radius: 8px;
      }

      > td:last-child {
        border-top-right-radius: 8px;
        background-color: #f5f5f5;
      }
    }

    @media (max-width: 748px) {
      > tr {
        background-color: #f5f5f5;
      }
      > tr tr {
        background-color: #f5f5f5;
        border-top: 1px solid rgba(197, 184, 184, 0.54);
        border-radius: 0;
      }
      padding: 0px;
      background-color: #f5f5f5;
      border-radius: 10px;
    }
  }

  .borderBottom {
    @media (min-width: 768px) {
      border-bottom: 1px solid rgba(197, 184, 184, 0.54);
    }
  }
`;

export const Intdleft = styled.td`
  text-align: left;
  padding-left: 2px;

  :not(:last-child) {
    padding-bottom: 10px;
  }
`;

export const Intdright = styled.td`
  text-align: right;
  padding-right: 2px;
`;

export const Intdcenter = styled.td`
  text-align: center;
  @media (max-width: 748px) {
    text-align: left;
  }

  .rotateIcon {
    transform: rotate(180deg);
  }
`;

export const Inthcenter = styled.th`
  text-align: center;
`;

export const Intrbody = styled.tr`
  /* height: 20px;
  td {
    padding: 4px;
  } */
`;

export const Intfoot = styled.tfoot`
  span {
    padding: 8px;
    font-weight: 600;
    @media (max-width: 748px) {
      font-size: 16px;
      font-weight: 400;
      text-align: right;
      display: block;
    }
  }
  @media (max-width: 748px) {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  tr {
    @media (max-width: 748px) {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
    }
    td {
      padding-top: 32px;
      @media (max-width: 748px) {
        font-size: 16px;
        font-weight: 400;
        flex: 1 0 100%;
        padding-top: 16px;
      }
    }
  }
`;

export const DivTitle = styled.span`
  font-weight: bold;
  display: block;
`;
export const DivValue = styled.span`
  display: block;
  flex: 1;
`;
export const DivLine = styled.div`
  display: flex;
  width: 100%;
  padding: 5px;
  @media (max-width: 748px) {
    flex-wrap: wrap;
  }
  div {
    flex: 1;
    display: flex;
    div {
      flex: 1;
      display: block;
    }
    @media (max-width: 748px) {
      flex: 1 0 100%;
      padding-bottom: 16px;
    }
  }
`;
export const DivLineInTable = styled.div`
  display: flex;
  width: 100%;
  div {
    flex: 1;
    display: flex;
    div {
      flex: 1;
      display: block;
      @media (max-width: 748px) {
        padding-right: 8px;
      }
    }
  }
`;
export const DivTitleInTable = styled.span`
  width: 100%;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
  margin-bottom: 4px;
  display: block;
`;
export const DivValueInTable = styled.span`
  display: block;
  flex: 1;
`;
export const DivTotalValue = styled.div`
  display: flex;
  justify-content: end;
`;

export const FakeCheck = styled.div`
  width: 18px;
`;

export const InternalItemsWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
`;

export const InternalItem = styled.div`
  width: 50%;
  margin-top: 16px;
  &:nth-of-type(odd) {
    margin-right: 8px;
    width: calc(50% - 8px);
    @media (max-width: 767px) {
      width: 100%;
      margin-right: 0;
    }
  }
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  @media (max-width: 767px) {
    width: 100%;
  }
  > h3 {
    text-align: center;
    width: 100%;
  }

  > div {
    padding-right: 8px;
    flex: 1 0 calc(33.33%);
    max-width: 33.33%;
    @media (max-width: 767px) {
      flex: 1 0 50%;
    }
    > h4 {
      border-bottom: none;
    }
    > p {
      display: inline;
      font-size: 14px;
      word-break: break-word;
    }
  }
`;

export const TableLine = styled.tr`
  width: 100%;
  border-bottom: 1px solid rgb(236 236 236);
  /* margin-top: 15px; */
  /* margin: 15px 0; */

  table {
    tr:last-child {
      margin-bottom: 15px;
    }
  }
`;

export const TRgrainOrder = styled.tr`
  display: flex;
  flex-direction: row;
  line-height: 160%;
  width: 100%;

  &#expandedColumn {
    @media (max-width: 748px) {
      display: flex;
      width: 100% !important;
    }
  }

  &.tr-column-25 {
    width: 25% !important;
  }
  &.tr-column-50 {
    width: 50% !important;
  }
`;

export const TDgrainOrder = styled.td`
  display: flex;
  flex-direction: column;
  text-align: left;
  padding-bottom: 23px;
  // padding-left: 3px;

  &.word-break {
    word-break: break-word;
  }
  &.column-25 {
    width: 25%;
  }
  &.column-50 {
    width: 50%;
  }
  &.column-75 {
    width: 75%;
  }
  &.column-100 {
    width: 100%;
  }

  &#expandedColumn {
    @media (max-width: 748px) {
      display: flex;
      width: 100% !important;
    }
  }
`;

export const TableRowFlex = styled.tr`
  display: flex;
  width: 100%;
  margin-bottom: 16px;
`;

export const TableCellFlex = styled.td`
  margin-left: 8px;

  &.word-break {
    word-break: break-word;
  }
  &.column-25 {
    width: 25%;
  }
  &.column-50 {
    width: 50%;
  }
  &.column-75 {
    width: 75%;
  }
  &.column-100 {
    width: 100%;
  }
`;

export const ModalTableBodyAF = styled.tbody`
  & > tr > td {
    background: rgba(0, 0, 0, 0.02);
  }

  @media (max-width: 390px) {
    & > tr > td:first-of-type {
      border-top-left-radius: 0px !important;
    }

    & > tr > td:nth-of-type(2) {
      border-top-right-radius: 0px !important;
    }

    & > tr > td:last-child {
      border-bottom-right-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }

  @media (min-width: 333px) and (max-width: 748px) {
    & > tr:first-of-type > td:first-of-type {
      border-top-left-radius: 8px;
    }

    & > tr:first-of-type > td:nth-of-type(2) {
      border-top-right-radius: 8px;
    }

    & > tr:last-child > td:last-child {
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }

  @media (min-width: 749px) {
    & > tr:first-of-type > td:first-of-type {
      border-top-left-radius: 8px;
    }

    & > tr:first-of-type > td:last-child {
      border-top-right-radius: 8px;
    }

    & > tr:last-child > td:first-of-type {
      border-bottom-left-radius: 8px;
    }

    & > tr:last-child > td:last-child {
      border-bottom-right-radius: 8px;
    }
  }
`;

export const ModalTableCellJR = styled.td`
  padding-top: 10px;
  padding-bottom: 10px;
`;

export const ModalTableHeaderJR = styled.thead`
  color: var(--palette-text-secondary, rgba(0, 0, 0, 0.54));
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%; /* 22.4px */

  border-bottom: #0000001f solid 1px;
`;

export const ModalTableBodyJR = styled.tbody`
  &:not(.bodyDetails) > tr > td {
    background: rgba(0, 0, 0, 0.02);
  }

  @media (max-width: 333px) {
    &:not(.bodyDetails) > tr > td:first-of-type {
      border-top-left-radius: 0px;
    }

    &:not(.bodyDetails) > tr > td:nth-of-type(2) {
      border-top-right-radius: 0px;
    }

    &:not(.bodyDetails) > tr > td:last-child {
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  }

  @media (min-width: 333px) and (max-width: 748px) {
    &:not(.bodyDetails) > tr > td:first-of-type {
      border-top-left-radius: 8px;
    }

    &:not(.bodyDetails) > tr > td:nth-of-type(2) {
      border-top-right-radius: 8px;
    }

    &:not(.bodyDetails) > tr > td:last-child {
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }

  @media (min-width: 748px) {
    &:not(.bodyDetails) > tr > td:first-of-type {
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }

    &:not(.bodyDetails) > tr > td:last-child {
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }

  &.bodyDetails {
    > tr {
      border-bottom: #0000001f solid 1px;
    }
  }
`;

export const BoldHeader = styled.td`
  font-weight: bold;
  color: #000000;
`;
