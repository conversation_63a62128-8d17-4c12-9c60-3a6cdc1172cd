import type { TableColumn } from 'react-data-table-component';
import React from 'react';

export interface ReturnProps<T> {
  title: string;
  origin: string;
  type?: string;
  permission: string;
  data?: any;
  headerColumns: TableColumn<T>[];
  detailColumns?: TableColumn<T>[];
  documentDetailHtml?: (rows: T | T[]) => React.ReactNode;
  approveItems?: boolean;
  hasDetailRoute?: boolean;
  hasDetailModal?: boolean;
  additional1?: (row: T) => string;
  additional2?: (row: T) => string;
  detailModalHeader?: (row: T) => React.ReactNode | any;
  detailModalContent?: (rows: T) => React.ReactNode;
  selector?: (row: T) => string;
}
