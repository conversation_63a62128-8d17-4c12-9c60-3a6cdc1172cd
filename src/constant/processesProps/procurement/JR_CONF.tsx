import { getAdditionalFieldMinus } from "@/utils/GetItemsSapMinus";
import { t } from "i18next";
import { ReturnProps } from "..";
import {
  DadosProps,
  IProcurementItemJR,
} from "../../../interfaces/procurement";
import { Inthead, TableInternal } from "../styles";

type ProcurementItem = IProcurementItemJR;

const getFromToText = (text: string) => {
  const removeBegin = text.replace("De:", "");
  const fromToArray = removeBegin.split("para:");

  if (fromToArray.length < 2) {
    return [fromToArray[0], ""];
  }

  return fromToArray;
};

export const JR_CONF: ReturnProps<ProcurementItem> = {
  title: "Confrontational",
  origin: "PROCUREMENT",
  type: "JR_CONF",
  permission: "JR",
  headerColumns: [
    {
      name: t("Solicitation.Number"),
      selector: (row) => row.id || "",
    },
    {
      name: t("Data"),
      selector: (row) => row.requestDate || "",
    },
    {
      name: t("FA.Requester"),
      selector: (row) => row.solicitante || "",
    },
    {
      name: t("CTG.FOLDER"),
      selector: (row) => row.folderNumber || "",
    },
    {
      name: t("DEJUR.Area"),
      selector: (row) => row.legalDepartamentArea || "",
    },
    {
      name: t("Locality"),
      selector: (row) => row.localidade || "",
    },
    {
      name: t("Description"),
      selector: (row) => row.descriptionOrderProvision || "",
    },
  ],
  documentDetailHtml: (data): React.ReactNode => {
    const row = Array.isArray(data) ? data[0] : data;

    const expectationTable: DadosProps[] = row.dadosProcesso.filter(
      (processo) =>
        processo.chave === "Ativo/Inativo" || processo.chave === "Expectativa"
    );

    const levelStatusTable: DadosProps[] = row.dadosProcesso.filter(
      (processo) =>
        processo.chave === "Nome Aprovador Nível Anterior" ||
        processo.chave === "Parecer do nível anterior." ||
        processo.chave === "Status Aprovado Nível Anterior"
    );

    return (
      <TableInternal style={{ borderCollapse: "collapse" }}>
        <Inthead>
          <tr>
            <th
              data-head={t("Previous.level.status")}
              colSpan={3}
              style={{
                paddingLeft: "8px",
              }}
            >
              {`${t("Previous.level.status")}:`}
            </th>
            <th
              data-head={t("Previous.level.approver")}
              colSpan={3}
              style={{
                paddingLeft: "8px",
              }}
            >
              {`${t("Previous.level.approver")}:`}
            </th>
            <th
              data-head={t("Previous.level.option")}
              colSpan={3}
              style={{
                paddingLeft: "8px",
              }}
            >
              {`${t("Previous.level.option")}:`}
            </th>
          </tr>
        </Inthead>
        <tbody className="no-radius">
          <tr>
            <td data-head={t("Previous.level.status")} colSpan={3}>
              {getAdditionalFieldMinus(
                "Status Aprovado Nível Anterior",
                levelStatusTable
              ) || "-"}
            </td>
            <td
              // className="borderLeft-row no-radius"
              colSpan={3}
              data-head={t("Previous.level.approver")}
            >
              {getAdditionalFieldMinus(
                "Nome Aprovador Nível Anterior",
                levelStatusTable
              ) || "-"}
            </td>
            <td colSpan={3} data-head={t("Previous.level.option")}>
              {getAdditionalFieldMinus(
                "Parecer do nível anterior.",
                levelStatusTable
              ) || "-"}
            </td>
          </tr>
        </tbody>

        <Inthead className="pd-left thead-mg">
          <tr>
            <th className="8pd" data-head={t("Item")} colSpan={1}>
              {`${t("Item")}:`}
            </th>
            <th className="borderLeft-row" data-head={t("From")} colSpan={3}>
              {`${t("From")}:`}
            </th>
            <th className="borderLeft-row" data-head={t("To")} colSpan={3}>
              {`${t("To")}:`}
            </th>
          </tr>
        </Inthead>

        <tbody className="no-radius">
          {expectationTable.map((dadoProcesso) => {
            return (
              <tr key={`${dadoProcesso.chave}-${dadoProcesso.valor}`}>
                <td
                  data-head={t("Item")}
                  colSpan={1}
                  className="adjustMobile no-radius"
                >
                  {dadoProcesso.chave || "-"}
                </td>
                <td
                  className="borderLeft-row no-radius"
                  colSpan={3}
                  data-head={t("From")}
                >
                  {getFromToText(dadoProcesso.valor)[0].trim() || "-"}
                </td>
                <td className="borderLeft-row" colSpan={3} data-head={t("To")}>
                  {getFromToText(dadoProcesso.valor)[1].trim() || "-"}
                </td>
              </tr>
            );
          })}
        </tbody>
        <thead className="thead-mg">
          <tr>
            <td>{t("opening")}</td>
            <td
              className="borderLeft-row"
              style={{
                borderTop: "20px",
              }}
            >
              {t("risk.prognosis.value")}
            </td>
            <td>{t("data.base")}</td>
            <td>{t("update.rule")}</td>
            <td className="borderLeft-row" style={{}}>
              {t("risk.prognosis.value")}
            </td>
            <td>{t("data.base")}</td>
            <td>{t("update.rule")}</td>
          </tr>
        </thead>
        <tbody className="m-top-mobile">
          {row.dadosValores &&
            row.dadosValores.map((dadoValor) => {
              return (
                <tr key={`${dadoValor.descricao}-${dadoValor.valorDe}`}>
                  <td data-head={t("opening")} className="noCheckbox">
                    {dadoValor.descricao || "-"}
                  </td>
                  <td
                    data-head={t("risk.prognosis.value")}
                    className="borderLeft-row"
                  >
                    {dadoValor.valorDe || "-"}
                  </td>
                  <td data-head={t("data.base")}>
                    {dadoValor.databaseDe || "-"}
                  </td>
                  <td data-head={t("update.rule")}>
                    {dadoValor.indiceCorrecaoDe || "-"}
                  </td>
                  <td
                    className="borderLeft-row"
                    style={{
                      borderTop: "0px",
                    }}
                    data-head={t("risk.prognosis.value")}
                  >
                    {dadoValor.valorPara || "-"}
                  </td>
                  <td data-head={t("data.base")}>
                    {dadoValor.databasePara || "-"}
                  </td>
                  <td data-head={t("update.rule")}>
                    {dadoValor.indiceCorrecaoPara || "-"}
                  </td>
                </tr>
              );
            })}
        </tbody>
      </TableInternal>
    );
  },
  hasDetailModal: false,
};
