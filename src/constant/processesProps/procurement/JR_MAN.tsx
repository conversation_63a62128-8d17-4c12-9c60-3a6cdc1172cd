import { Box, Card, CardContent, Typography } from "@mui/material";
import { t } from "i18next";
import { ReturnProps } from "..";
import { IProcurementItemJR } from "../../../interfaces/procurement";

type ProcurementItem = IProcurementItemJR;

export const JR_MAN: ReturnProps<ProcurementItem> = {
  title: "Juridic.Resolve",
  origin: "PROCUREMENT",
  type: "JR_MAN",
  permission: "JR",
  headerColumns: [
    {
      name: t("N.Solicitation"),
      selector: (row) => row.numeroSolicitacao || "",
    },
    {
      name: t("Request.Date"),
      selector: (row) => row.dataSolicitacao || "",
    },
    {
      name: t("Payment.Type"),
      selector: (row) => row.tipoPagamento || "",
    },
    {
      name: t("Payment.method"),
      selector: (row) => row.formaPagamento || "",
    },
    {
      name: t("CTG.Folder"),
      selector: (row) => row.pastaCTG || "",
    },
    {
      name: t("Payment.Date"),
      selector: (row) => row.dataPagamento || "",
    },
    {
      name: t("DEJUR.Area"),
      selector: (row) => row.areaDejur || "",
    },
    {
      name: t("Internal.Lawyer"),
      selector: (row) => row.advogadoInterno || "",
    },
    {
      name: t("Juridic.Value"),
      selector: (row) => row.valorTotal || "",
    },
  ],
  documentDetailHtml: (rows: ProcurementItem | ProcurementItem[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const returnAd = (value: any) => {
      return (
        <Box
          key={value?.chave}
          sx={{ mb: 1, width: "33.33%" }}
          display="inline-block"
        >
          <Typography
            variant="subtitle2"
            component="h4"
            sx={{ fontWeight: "bold", mb: 0 }}
            title={`${value?.chave}:`}
          >
            {`${value?.chave}:`}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              wordBreak: "break-word",
            }}
          >
            {value?.valor || "-"}
          </Typography>
        </Box>
      );
    };

    return (
      <>
        <Box sx={{ ml: "5px", my: 2 }}>
          <Typography
            variant="h6"
            component="h3"
            sx={{ mb: 1 }}
            color="secondary"
          >
            Observação
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            flexDirection: { xs: "column", md: "row" },
            mb: 2,
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Card
              sx={{
                height: "100%",
                backgroundColor: "white !important",
                "& > div": { backgroundColor: "white !important" },
              }}
            >
              <CardContent>
                <Typography
                  variant="h6"
                  component="h3"
                  sx={{
                    mb: 1,
                  }}
                  color="secondary"
                >
                  Dados Processos
                </Typography>
                <Box
                  display="flex"
                  flexWrap="wrap"
                  sx={{
                    backgroundColor: "white !important",
                    "& > div ": { backgroundColor: "white !important" },
                  }}
                >
                  {row?.dadosProcesso?.map((ad) => {
                    return returnAd(ad);
                  })}
                </Box>
              </CardContent>
            </Card>
          </Box>

          <Box sx={{ flex: 1 }}>
            <Card
              sx={{
                height: "100%",
                backgroundColor: "white !important",
                "& > div": { backgroundColor: "white !important" },
              }}
            >
              <CardContent>
                <Typography
                  variant="h6"
                  component="h3"
                  sx={{
                    mb: 1,
                  }}
                  color="secondary"
                >
                  Dados Valores
                </Typography>
                <Box
                  display="flex"
                  flexWrap="wrap"
                  sx={{
                    backgroundColor: "white !important",
                    "& > div ": { backgroundColor: "white !important" },
                  }}
                >
                  {row?.dadosValores?.map((ad) => {
                    return returnAd(ad);
                  })}
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>
      </>
    );
  },
  hasDetailModal: false,
};
