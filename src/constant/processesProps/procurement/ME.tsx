import { t } from "i18next";
import { ReturnProps } from "..";
import { IProcurementItemEletMarket } from "../../../interfaces/procurement";

type ProcurementItem = IProcurementItemEletMarket;

export const ME: ReturnProps<ProcurementItem> = {
  title: "Critical.documentation.approval.Homologation",
  origin: "PROCUREMENT",
  type: "ME",
  permission: "ME",
  headerColumns: [
    {
      name: t("Requester"),
      selector: (row) => row.Solicitante || "",
    },
    {
      name: t("Provider"),
      selector: (row) => row.Fornecedor || "",
    },
    {
      name: t("Clifor"),
      selector: (row) => row.Clifor || "",
    },
    {
      name: t("Description"),
      selector: (row) => row.Descricao || "",
    },
  ],
  hasDetailModal: false,
};
