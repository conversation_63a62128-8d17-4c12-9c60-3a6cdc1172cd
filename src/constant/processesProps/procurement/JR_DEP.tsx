import { t } from "i18next";
import { ReturnProps } from "..";
import { IProcurementItemJR } from "../../../interfaces/procurement";

type ProcurementItem = IProcurementItemJR;

export const JR_DEP: ReturnProps<ProcurementItem> = {
  title: "Update.Of.Monthly.Deposit",
  origin: "PROCUREMENT",
  type: "JR_DEP",
  permission: "JR",
  headerColumns: [
    {
      name: t("Execution.date"),
      selector: (row) => row.dataExecucao || "",
    },
    {
      name: t("Execution.User"),
      selector: (row) => row.usuarioExecucao || "",
    },
    {
      name: t("Competence"),
      selector: (row) => row.competencia || "",
    },
    {
      name: t("DEJUR.Area"),
      selector: (row) => row.areaDejur || "",
    },
    {
      name: t("Movement"),
      selector: (row) => row.movimento || "",
    },
    {
      name: t("Value"),
      selector: (row) => row.saldo || "",
    },
  ],
};
