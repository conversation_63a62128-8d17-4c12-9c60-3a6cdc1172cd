import { APIProcurement } from "@/api";
import { t } from "i18next";
import { ReturnProps } from "..";
import {
  IProcurementItemJR,
  ItemPropsDetails,
} from "../../../interfaces/procurement";

type ProcurementItem = IProcurementItemJR;

export const JR_CIRC: ReturnProps<ProcurementItem> = {
  title: "Circularization",
  origin: "PROCUREMENT",
  type: "JR_CIRC",
  permission: "JR",
  headerColumns: [
    {
      name: t("Reference.Month"),
      selector: (row) => row.dataPagamento || "",
      style: { width: "200px" },
    },
    {
      name: t("DEJUR.Area"),
      selector: (row) => row.areaDejur || "",
      style: { width: "200px" },
    },
    {
      name: t("Office"),
      selector: (row) => row.advogadoInterno || "",
      style: { width: "200px" },
    },
    {
      name: t("From.To.Information"),
      selector: (row) => row.observacao || "",
    },
  ],
  hasDetailModal: true,
  hasDetailRoute: true,
  detailModalHeader: (rows: ProcurementItem | ProcurementItem[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    return (
      <tbody>
        <tr>
          <td className="jrProcess">
            <div className="tableTitle">{`${t("Reference.Month")}:`}</div>
            {row.dataPagamento || "-"}
          </td>
          <td className="jrProcess">
            <div className="tableTitle">{`${t("DEJUR.Area")}:`}</div>
            {row.areaDejur || "-"}
          </td>
          <td className="jrProcess">
            <div className="tableTitle">{`${t("Office")}:`}</div>
            {row.advogadoInterno || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: async (rows: ProcurementItem | ProcurementItem[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const { data } = await APIProcurement.getCircularizationDetail(
      row.numeroSolicitacao
    );

    return (
      <tbody className="bodyDetails">
        {data.map((item: ItemPropsDetails) => {
          return (
            <tr key={item.id} className="detailModalContent">
              <td className="flex50">
                <span>{t("Folder.Number")}</span>
                {item.folderNumber || "-"}
              </td>
              <td>
                <span>{t("Process")}</span>
                {item.processNumber || "-"}
              </td>
              <td>
                <span>{t("Provision.Class")}</span>
                {item.provisionClass || "-"}
              </td>
              <td>
                <span>{t("Sphere")}</span>
                {item.sphere || "-"}
              </td>
              <td>
                <span>{t("Internal.Lawyer")}</span>
                {item.internalLawyer || "-"}
              </td>
              <td>
                <span>{t("From.To.Information")}</span>
                {item.observation || "-"}
              </td>
            </tr>
          );
        })}
      </tbody>
    );
  },
};
