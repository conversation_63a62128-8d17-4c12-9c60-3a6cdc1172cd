import { t } from "i18next";
import { ReturnProps } from "..";
import { IProcurementItemEletMarket } from "../../../interfaces/procurement";

type ProcurementItem = IProcurementItemEletMarket;

export const MX: ReturnProps<ProcurementItem> = {
  title: "Approval.of.debts.above.capital.Homologation",
  origin: "PROCUREMENT",
  type: "MX",
  permission: "MX",
  headerColumns: [
    {
      name: t("Negotiator"),
      selector: (row) => row.Solicitante || "",
    },
    {
      name: t("Provider"),
      selector: (row) => row.Fornecedor || "",
    },
    {
      name: t("Clifor"),
      selector: (row) => row.Clifor || "",
    },
    {
      name: t("Description"),
      selector: (row) => row.Descricao || "",
    },
    {
      name: t("Share.Capital"),
      selector: (row) => row.CapitalSocial || "",
    },
    {
      name: t("Debt"),
      selector: (row) => row.Divida || "",
    },
  ],
  hasDetailModal: false,
};
