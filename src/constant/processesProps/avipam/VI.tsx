import {
  AirsProps,
  CarsProps,
  HotelProps,
  JustificationExcerptProps,
  JustificationTravelProps,
  TransportProps,
} from "@/interfaces/avipam";
import { datetimeBDtoReadWithSeparator } from "@/utils/Date";
import {
  DirectionsBus,
  DirectionsCar,
  Flight,
  Hotel,
  ThumbDownOffAltOutlined,
  ThumbUpOutlined,
} from "@mui/icons-material";
import { Tooltip } from "@mui/material";
import { t } from "i18next";
import { ReturnProps } from "..";
import { IAvipam } from "../../../interfaces";
import { toPtBrCurrency } from "../../../utils/Currency";
import { Inttitle } from "../styles";

type AvipamItem = IAvipam.AvipamItem;

const getCurrencyDisplay = (row: {
  hotels?: HotelProps[];
  transport?: TransportProps[];
  cars?: CarsProps[];
  airs?: AirsProps[];
}): string => {
  const allCurrencies = [
    ...(row?.hotels?.map((item) => item.currency) || []),
    ...(row?.transport?.map((item) => item.currency) || []),
    ...(row?.cars?.map((item) => item.currency) || []),
    ...(row?.airs?.map((item) => item.currency) || []),
  ];

  const isSameCurrency =
    allCurrencies.length > 0 &&
    allCurrencies.every((currency) => currency === allCurrencies[0]);

  return isSameCurrency ? allCurrencies[0] : "-";
};

const handleAdhereToPolitic = (
  travelJustification: JustificationTravelProps[] | null,
  justificationExcerpt: JustificationExcerptProps[] | null
) => {
  if (travelJustification && travelJustification.length > 0) {
    return (
      <Tooltip title={t("out.of.politic")} placement="top">
        <ThumbDownOffAltOutlined
          titleAccess={t("out.of.politic")}
          color="error"
        />
      </Tooltip>
    );
  }

  if (justificationExcerpt && justificationExcerpt.length > 0) {
    return (
      <Tooltip title={t("out.of.politic")} placement="top">
        <ThumbDownOffAltOutlined
          titleAccess={t("out.of.politic")}
          color="error"
        />
      </Tooltip>
    );
  }

  return (
    <Tooltip title={t("within.politic")} placement="top">
      <ThumbUpOutlined titleAccess={t("within.politic")} color="success" />
    </Tooltip>
  );
};

export const VI: ReturnProps<AvipamItem> = {
  title: "Travels.Avipam",
  origin: "AVIPAM",
  type: "VI",
  permission: "VI",
  hasDetailModal: true,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: t("Solicitation.Number"),
      selector: (row) => row.orderNumber || "-",
    },
    {
      name: t("Solicitation.date"),
      selector: (row) => datetimeBDtoReadWithSeparator(row?.dateOrder) || "-",
    },
    {
      name: t("Traveler"),
      selector: (row) => row.traveler || "-",
    },
    {
      name: t("Cost.Center"),
      selector: (row) => row.centerCostCode || "-",
    },
    {
      name: t("Status"),
      selector: (row) => {
        return row?.statusTravel === "AUT"
          ? t("Waiting.for.Cost.Authorization")
          : "-";
      },
    },
    {
      name: t("Type"),
      cell: (row) => {
        return (
          <div style={{ display: "flex" }}>
            {!!(row?.airs && row?.airs[0]) && (
              <Flight
                fontSize="medium"
                titleAccess={t("Aerial")}
                color="secondary"
              />
            )}
            {!!(row?.hotels && row?.hotels[0]) && (
              <Hotel
                fontSize="medium"
                titleAccess={t("Hotel")}
                color="secondary"
              />
            )}
            {!!(row?.transport && row?.transport[0]) && (
              <DirectionsBus
                fontSize="medium"
                titleAccess={t("Bus.Transport")}
                color="secondary"
              />
            )}
            {!!(row?.cars && row?.cars[0]) && (
              <DirectionsCar
                fontSize="medium"
                titleAccess={t("Rent.Car")}
                color="secondary"
              />
            )}
          </div>
        );
      },
    },
    {
      name: t("Currency"),
      selector: (row) => getCurrencyDisplay(row) || "-",
    },
    {
      name: t("Ammount"),
      selector: (row) => toPtBrCurrency(row?.total?.toString()) || "-",
    },
    {
      name: t("adhere.to.the.politic"),
      cell: (row) => {
        return (
          handleAdhereToPolitic(
            row?.travelJustification,
            row?.justificationExcerpt
          ) || "-"
        );
      },
      center: true,
    },
  ],
  detailModalHeader: (rows: AvipamItem | AvipamItem[]) => {
    const row = Array.isArray(rows) ? (rows[0] as AvipamItem) : rows;

    return (
      <tbody>
        <tr>
          <td
            style={{
              borderTopLeftRadius: "8px",
            }}
          >
            <div className="tableTitle">{`${t("Solicitation.Number")}:`}</div>
            {row.orderNumber}
          </td>

          <td>
            <div className="tableTitle">{`${t("Solicitation.date")}:`}</div>
            {datetimeBDtoReadWithSeparator(
              row.dateOrder.replace("T", " "),
              true
            )}
          </td>

          <td>
            <div className="tableTitle">{`${t("Status")}:`}</div>
            {row.statusTravel === "AUT"
              ? t("Waiting.for.Cost.Authorization")
              : ""}
          </td>

          <td>
            <div className="tableTitle">{`${t("Traveler")}:`}</div>
            {row.traveler}
          </td>

          <td
            style={{
              borderTopRightRadius: "8px",
            }}
          >
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {row.centerCostCode}
          </td>
        </tr>

        <tr>
          <td className="empty">
            <div className="tableTitle">
              {row.approvers.length > 1
                ? `${t("Approvers")}:`
                : `${t("Approver")}:`}
            </div>
            {row?.approvers &&
              row.approvers?.map(({ name }) => {
                return <div>{name}</div>;
              })}
          </td>

          <td className="empty">
            <div className="tableTitle">{t("Origin")}</div>
            {row?.infGerencial?.length
              ? row.infGerencial?.find(
                  (inf) =>
                    inf.contentManagement === "avipam_brfhomolog_origem_base"
                )?.fieldManagement
              : "-"}
          </td>

          <td className="empty">
            <div className="tableTitle">{t("Destination")}</div>
            {row?.infGerencial?.length
              ? row.infGerencial?.find(
                  (inf) =>
                    inf.contentManagement === "avipam_brfhomolog_destino_final"
                )?.fieldManagement
              : "-"}
          </td>

          <td className="empty">
            <div className="tableTitle">{`${t("Currency")}:`}</div>
            {getCurrencyDisplay(row)}
          </td>

          <td className="empty">
            <div className="tableTitle">{`${t("Total.Value")}:`}</div>
            {toPtBrCurrency(row.total.toString())}
          </td>

          <td className="empty">
            <div className="tableTitle"></div>
          </td>
        </tr>

        <tr>
          <td colSpan={5}>
            <div className="tableTitle">{`${t("Reason")}:`}</div>
            {row.reason || "-"}
          </td>
        </tr>

        <tr>
          <td colSpan={5}>
            <div className="tableTitle">{`${t("Observation")}:`}</div>
            {row.observation || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: AvipamItem | AvipamItem[]) => {
    const row = Array.isArray(rows) ? (rows[0] as AvipamItem) : rows;

    const showIndividualValue = row.summary.length > 0;

    const hasDetails =
      (Array.isArray(row.airs) && row.airs.length > 0) ||
      (Array.isArray(row.hotels) && row.hotels.length > 0) ||
      (Array.isArray(row.transport) && row.transport.length > 0) ||
      (Array.isArray(row.cars) && row.cars.length > 0);

    return (
      <>
        {hasDetails && (
          <>
            <tr className="detailModalContent">
              <Inttitle>{`${t("Solicitation.Detail")}:`}</Inttitle>
            </tr>

            {Array.isArray(row.airs) && row.airs.length > 0 && (
              <tr className="detailModalContent">
                <tbody>
                  {row.airs.map((airs, index) => (
                    <tr key={`${airs.arrivalDate}-${airs.departureDate}`}>
                      <td
                        id="minorColumn"
                        data-head={t("Item")}
                        className="noCheckbox"
                      >
                        <span>{t("Item")}</span>
                        <div
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            gap: "4px",
                          }}
                        >
                          <Flight fontSize="small" color="secondary" />
                          {t("Aerial")}
                        </div>
                      </td>
                      <td data-head={t("Origin.Check.IN")}>
                        <span>{t("Origin.Check.IN")}</span>
                        <div>{`${airs.cityDeparture} /`}</div>
                        <div>
                          {datetimeBDtoReadWithSeparator(
                            airs.departureDate.replace("T", " "),
                            true
                          )}
                        </div>
                      </td>
                      <td data-head={t("Destiny.Check.OUT")}>
                        <span>{t("Destiny.Check.OUT")}</span>
                        <div>{`${airs.cityArrival} /`}</div>
                        <div>
                          {datetimeBDtoReadWithSeparator(
                            airs.arrivalDate.replace("T", " "),
                            true
                          )}
                        </div>
                      </td>

                      <td>
                        <span>{t("Currency")}</span>
                        {showIndividualValue ? airs.currency : "-"}
                      </td>
                      <td>
                        <span>{t("Ammount")}</span>
                        {showIndividualValue
                          ? toPtBrCurrency(airs.totalFare.toString())
                          : "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </tr>
            )}

            {Array.isArray(row.hotels) && row.hotels.length > 0 && (
              <tr>
                <tbody>
                  {row.hotels.map((hotel, index) => (
                    <tr key={`${hotel.checkinDate}-${hotel.checkoutDate}`}>
                      <td
                        id="minorColumn"
                        data-head={t("Item")}
                        className="noCheckbox"
                      >
                        <span>{t("Item")}</span>
                        <div
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            gap: "4px",
                          }}
                        >
                          <Hotel fontSize="small" color="secondary" />
                          {t("Hotel")}
                        </div>
                      </td>
                      <td data-head={t("Check.IN")}>
                        <span>{t("Check.IN")}</span>
                        {datetimeBDtoReadWithSeparator(
                          hotel.checkinDate.replace("T", " "),
                          true
                        )}
                      </td>
                      <td data-head={t("Check.OUT")}>
                        <span>{t("Check.OUT")}</span>
                        {datetimeBDtoReadWithSeparator(
                          hotel.checkoutDate.replace("T", " "),
                          true
                        )}
                      </td>
                      <td data-head={t("City.FU")}>
                        <span>{t("City.FU")}</span>
                        {`${hotel.city}/${hotel.state}`}
                      </td>
                      <td data-head={t("Hotel")}>
                        <span>{t("Hotel")}</span>
                        {hotel.name}
                      </td>
                      <td data-head={t("Room.Nights")}>
                        <span>{t("Room.Nights")}</span>
                        {hotel.amountDialy}
                      </td>
                      <td>
                        <span>{t("Currency")}</span>
                        {hotel.currency}
                      </td>
                      <td>
                        <span>{t("Ammount")}</span>
                        {toPtBrCurrency(hotel.totalFare.toString())}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </tr>
            )}

            {Array.isArray(row.transport) && row.transport.length > 0 && (
              <tr>
                <tbody>
                  {row.transport.map((transport, index) => (
                    <tr
                      key={`${transport.dateArrival}-${transport.dateDeparture}`}
                    >
                      <td
                        id="minorColumn"
                        data-head={t("Item")}
                        className="noCheckbox"
                      >
                        <span>{t("Item")}</span>
                        <div
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            gap: "4px",
                          }}
                        >
                          <Tooltip title={t("Bus.Transport")} placement="top">
                            <DirectionsBus fontSize="small" color="secondary" />
                          </Tooltip>
                          {t("Bus.Transport")}
                        </div>
                      </td>
                      <td data-head={t("Origin.Check.IN")}>
                        <span>{t("Origin.Check.IN")}</span>
                        <div>{`${transport.departure} /`}</div>
                        <div>
                          {datetimeBDtoReadWithSeparator(
                            transport.dateDeparture.replace("T", " "),
                            true
                          )}
                        </div>
                      </td>
                      <td data-head={t("Destiny.Check.OUT")}>
                        <span>{t("Destiny.Check.OUT")}</span>
                        <div>{`${transport.arrival} /`}</div>
                        <div>
                          {datetimeBDtoReadWithSeparator(
                            transport.dateArrival.replace("T", " "),
                            true
                          )}
                        </div>
                      </td>

                      <td>
                        <span>{t("Currency")}</span>
                        {transport.currency}
                      </td>
                      <td>
                        <span>{t("Ammount")}</span>
                        {toPtBrCurrency(transport.totalFare.toString())}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </tr>
            )}

            {Array.isArray(row.cars) && row.cars.length > 0 && (
              <tr>
                <tbody>
                  {row.cars.map((cars, index) => (
                    <tr key={`${cars.checkinDate}-${cars.checkoutDate}`}>
                      <td
                        id="minorColumn"
                        data-head={t("Item")}
                        className="noCheckbox"
                      >
                        <span>{t("Item")}</span>
                        <div
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            gap: "4px",
                          }}
                        >
                          <DirectionsCar fontSize="small" color="secondary" />
                          {t("Rent.Car")}
                        </div>
                      </td>
                      <td data-head={t("Check.IN")}>
                        <span>{t("Check.IN")}</span>
                        {datetimeBDtoReadWithSeparator(
                          cars.checkinDate.replace("T", " "),
                          true
                        )}
                      </td>
                      <td data-head={t("Check.OUT")}>
                        <span>{t("Check.OUT")}</span>
                        {datetimeBDtoReadWithSeparator(
                          cars.checkoutDate.replace("T", " "),
                          true
                        )}
                      </td>
                      <td data-head={t("Pickup.Location")}>
                        <span>{t("Pickup.Location")}</span>
                        {cars.localCheckout}
                      </td>

                      <td data-head={t("Dailies")}>
                        <span>{t("Dailies")}</span>
                        {cars.dailyQuantity}
                      </td>
                      <td>
                        <span>{t("Currency")}</span>
                        {cars.currency}
                      </td>
                      <td>
                        <span>{t("Ammount")}</span>
                        {toPtBrCurrency(cars.totalFare.toString())}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </tr>
            )}
          </>
        )}

        {((row.travelJustification && row.travelJustification.length > 0) ||
          (row.justificationExcerpt &&
            row.justificationExcerpt.length > 0)) && (
          <>
            <Inttitle
              style={{
                padding: "26px 0 16px 8px",
              }}
            >
              {t("Justification")}
            </Inttitle>
            <tr>
              <td
                style={{
                  padding: "0 8px",
                }}
                colSpan={4}
              >
                {row.travelJustification &&
                  row.travelJustification.map((justificationItem, index) => (
                    <div key={`travel-${index}`} className="justification-line">
                      <ThumbDownOffAltOutlined
                        titleAccess={t("out.of.politic")}
                        color="error"
                      />
                      {justificationItem.descriptionTravel}
                    </div>
                  ))}
                {row.justificationExcerpt &&
                  row.justificationExcerpt.map((justificationItem, index) => (
                    <div
                      key={`excerpt-${index}`}
                      className="justification-line"
                    >
                      <ThumbDownOffAltOutlined
                        titleAccess={t("out.of.politic")}
                        color="error"
                      />
                      {justificationItem.description}
                    </div>
                  ))}
              </td>
            </tr>
          </>
        )}
      </>
    );
  },
};
