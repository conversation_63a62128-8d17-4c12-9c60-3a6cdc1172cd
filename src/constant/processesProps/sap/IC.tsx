import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import formatNumberPtBr from "../../../utils/FormatNumberPtBr";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const IC: ReturnProps<ISap.ItemProps> = {
  title: "Inventory.Approval",
  origin: "SAP",
  type: "IC",
  permission: "IC",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return getAdditionalField("Nº depósito", row.adicionais);
  },
  additional2: (row: ISap.ItemProps) => {
    return getAdditionalField("Tipo depósito", row.adicionais);
  },
  headerColumns: [
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Deposit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nº depósito", row.adicionais) || "-";
      },
    },
    {
      name: "Storage.Type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Tipo depósito", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Date.Inventory",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Inventory.Rec",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Inventory",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Inventário", row.adicionais) || "-";
      },
    },
    {
      name: "Weight",
      selector: (row: ISap.ItemProps) => {
        return (
          formatNumberPtBr(getAdditionalField("PESO", row.adicionais)) || "-"
        );
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => {
        return formatNumberPtBr(row.vldoc) || "-";
      },
    },
  ],
};
