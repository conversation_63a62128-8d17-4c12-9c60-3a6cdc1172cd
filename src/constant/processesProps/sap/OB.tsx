import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const OB: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Sales.Orders",
  origin: "SAP",
  type: "OB",
  permission: "OB",
  approveItems: true,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },
  headerColumns: [
    {
      name: "Sales.Orders",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Rule",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Lock.description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_REGRA", row.adicionais) || "-";
      },
    },
    {
      name: "Lock.date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_BLOQUEIO", row.adicionais) || "-";
      },
    },
    {
      name: "Branch.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKORG", row.adicionais) || "-";
      },
    },
    {
      name: "Sales.District",
      selector: (row: ISap.ItemProps) => {
        return (
          `${getAdditionalField(
            "BZIRK",
            row.adicionais
          )} - ${getAdditionalField("BZIRK_DESC", row.adicionais)}` || "-"
        );
      },
    },
    {
      name: "Sell.Office",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKBUR", row.adicionais) || "-";
      },
    },
    {
      name: "Client.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "Total.FIFO.Amount.Approved",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL_VENDA_FIFO", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NETWR", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material.Description.OB",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Quantity",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Seller.Unity",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("VRKME", row.adicionais) || "-",
    },
    {
      name: "Batch.Expires.Date.OB",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("BATCH_EXPIRY_DATES", row.adicionais) || "-",
    },
    {
      name: "Sugest.Price",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("KBETR", row.adicionais) || "-",
    },
    {
      name: "Inserted.Price",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: "Total.Item.Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: ".Price.Difference",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("PRICECHG", row.adicionais) || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;
    return (
      <TableInternal>
        <thead>
          <tr>
            <td>{`${t("HDD.request")}: ${
              Array.isArray(rows) ? rows.length : 1
            }`}</td>
            <td>{t("Order.motivation")}</td>
            {row.tipo === "001" && <td>{t("Credit Limit")}</td>}
            {row.tipo === "001" && <td>{t("Total.Compromisse")}</td>}
            <td>URL</td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td data-head={t("HDD.request")}>
              {getAdditionalField("HDDREQ", row.adicionais) || "-"}
            </td>
            <td data-head={t("Order.motivation")}>
              {getAdditionalField("AUGRU", row.adicionais) || "-"}
            </td>
            {row.tipo === "001" && (
              <td>{getAdditionalField("KLIMK", row.adicionais) || "-"}</td>
            )}
            {row.tipo === "001" && (
              <td>{getAdditionalField("SAUFT", row.adicionais) || "-"}</td>
            )}
            {
              <td>
                {getAdditionalField("URL", row.adicionais) ? (
                  <a
                    href={getAdditionalField("URL", row.adicionais)}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {getAdditionalField("URL", row.adicionais)}
                  </a>
                ) : (
                  "-"
                )}
              </td>
            }
          </tr>
        </tbody>
      </TableInternal>
    );
  },
};
