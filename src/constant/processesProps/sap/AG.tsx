import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const AG: ReturnProps<ISap.ItemProps> = {
  title: "Complementary.Freight",
  origin: "SAP",
  type: "AG",
  permission: "AG",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Create.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Car.plate",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Placa", row.adicionais) || "-";
      },
    },
    {
      name: "Supplier",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Transport",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Transporte", row.adicionais) || "-";
      },
    },
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Occurrence",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Ocorrência", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro de Custo", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
  ],
};
