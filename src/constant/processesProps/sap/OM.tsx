import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const OM: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Orders.Release.Monitor",
  origin: "SAP",
  type: "OM",
  permission: "OM",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NRSOL", row.adicionais) || "-";
      },
    },
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Type.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_REGRA", row.adicionais) || "-";
      },
    },
    {
      name: "Emission.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Sell.Office",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKBUR", row.adicionais) || "-";
      },
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Liquid.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NETWR", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Credit.Limit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KLIMK", row.adicionais) || "-";
      },
    },
    {
      name: "Sales.value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SAUFT", row.adicionais) || "-";
      },
    },
    {
      name: "Debt.receivable",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SKFOR", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Compromisse",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBLIG", row.adicionais) || "-";
      },
    },
    {
      name: "Overduo",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OVERDUO_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Days.overdue.all.invoices",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("D_TOTAL_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Overduo.days.delay",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("D_OVERDUO_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Manager.s.Note",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBS_GESTOR", row.adicionais) || "-";
      },
    },
  ],
  hasDetailRoute: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document")}:`}</div>
            {rowHeader?.documento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Emission.Date")}:`}</div>
            {rowHeader?.data_emissao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Sell.Office")}:`}</div>
            {getAdditionalField("VKBUR", rowHeader?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Center")}:`}</div>
            {rowHeader?.CENTRO}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Client")}:`}</div>
            {getAdditionalField("CUSTNAME", rowHeader?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Seller")}:`}</div>
            {getAdditionalField("SALESPERSON", rowHeader?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Total.Liquid.Value")}`}</div>
            {getAdditionalField("NETWR", rowHeader?.adicionais)}
          </td>
          <td></td>
        </tr>
        <tr>
          <td colSpan={4}>
            <TableInternal>
              <thead>
                <tr>
                  <td>{`${t("Item")}:`}</td>
                  <td>{`${t("Material")}:`}</td>
                  <td>{`${t("Amount")}:`}</td>
                  <td>{`${t("Sell.unit")}:`}</td>
                  <td>{`${t("Unit.Value")}:`}</td>
                  <td>{`${t("Item.Value")}:`}</td>
                  <td>{`${t("Condition.value")}:`}</td>
                  <td>{`${t("Reason.for.refusal")}:`}</td>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-head={t("Item")}>{rowHeader?.item || "-"}</td>
                  <td data-head={t("Material")}>
                    {rowHeader?.material || "-"}
                  </td>
                  <td data-head={t("Amount")}>{rowHeader?.qtd || "-"}</td>
                  <td data-head={t("Sell.unit")}>
                    {getAdditionalField("VRKME", rowHeader?.adicionais) || "-"}
                  </td>
                  <td data-head={t("Unit.Value")}>
                    {rowHeader?.vlunit || "-"}
                  </td>
                  <td data-head={t("Item.Value")}>
                    {rowHeader?.vlitem || "-"}
                  </td>
                  <td data-head={t("Condition.value")}>
                    {getAdditionalField("KBETR", rowHeader?.adicionais) || "-"}
                  </td>
                  <td data-head={t("Reason.for.refusal")}>
                    {getAdditionalField("AUGRU", rowHeader?.adicionais) || "-"}
                  </td>
                </tr>
              </tbody>
            </TableInternal>
          </td>
        </tr>
      </tbody>
    );
  },
};
