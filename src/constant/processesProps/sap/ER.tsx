import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const ER: ReturnProps<ISap.ItemProps> = {
  title: "HR.hierarchy",
  origin: "SAP",
  type: "ER",
  permission: "ER",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Manager",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Gestor", row.adicionais) || "-";
      },
    },
    {
      name: "Period",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Período", row.adicionais) || "-";
      },
    },
    {
      name: "ER.HR.Group",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Grupo do RH", row.adicionais) || "-";
      },
    },
    {
      name: "ER.HR.Function",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Cargo do RH", row.adicionais) || "-";
      },
    },
    {
      name: "ER.Al.ada.Group",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Grupo de Alçada", row.adicionais) || "-";
      },
    },
    {
      name: "ER.Al.ada.Function",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Cargo de Alçada", row.adicionais) || "-";
      },
    },
  ],
};
