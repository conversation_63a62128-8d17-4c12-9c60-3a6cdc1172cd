import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PR: ReturnProps<ISap.ItemProps> = {
  title: "Requests",
  origin: "SAP",
  type: "PR",
  permission: "PR",
  approveItems: true,
  hasDetailModal: true,
  headerColumns: [
    {
      name: "N.Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Empresa", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data da Solicitação", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Provider.Pretended.Steady",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
  ],
  detailColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material.Order",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Quantity",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Unit.Value",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: "Item.Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro", row.adicionais) || "-";
      },
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro de Custo", row.adicionais) || "-";
      },
    },
    {
      name: "Order.PR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Ordem", row.adicionais) || "-";
      },
    },
    {
      name: "Item.Text",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Texto do Item", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Request.Date")}:`}</div>
            {getAdditionalField("Data da Solicitação", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Total.Value")}:`}</div>
            {rowHeader?.vldoc || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "Provider.Pretended.Steady"
            )}`}</div>
            {rowHeader?.fornecedor || "-"}
          </td>
      
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => (
    <tbody>
      {Array.isArray(rows) &&
        rows.map((row) => (
          <tr className="detailModalContent">
            <td>
              <span>{t("Item")}</span>
              {row.item || "-"}
            </td>
            <td>
              <span>{t("Material.Order")}</span>
              {row.material || "-"}
            </td>
            <td>
              <span>{t("Quantity")}</span>
              {row.qtd || "-"}
            </td>
            <td>
              <span>{t("Unit.Value")}</span>
              {row.vlunit || "-"}
            </td>
            <td>
              <span>{t("Item.Value")}</span>
              {row.vlitem || "-"}
            </td>
            <td>
              <span>{t("Type")}</span>
              {row.tipo || "-"}
            </td>
            <td>
              <span>{t("Center")}</span>
              {row.centro || "-"}
            </td>
            <td>
              <span>{t("Item.Text")}</span>
              {getAdditionalField("Texto do Item", row.adicionais) || "-"}
            </td>
          </tr>
        ))}
    </tbody>
  ),
};
