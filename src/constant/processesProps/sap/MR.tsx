import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const MR: ReturnProps<ISap.ItemProps> = {
  title: "MRP.Register",
  origin: "SAP",
  type: "MR",
  permission: "MR",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Approving.area",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("AREA APROVADORA", row.adicionais) || "-";
      },
    },
    {
      name: "Deposit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DEPOSITO", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Material.Type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("tipo material", row.adicionais) || "-";
      },
    },
    {
      name: "Solicitation.type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("tipo SOLICITACAO", row.adicionais) || "-";
      },
    },
    {
      name: "User.Area",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("AREA USUARIO", row.adicionais) || "-";
      },
    },
    {
      name: "Justification",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("JUSTIFICATIVA", row.adicionais) || "-";
      },
    },
  ],
};
