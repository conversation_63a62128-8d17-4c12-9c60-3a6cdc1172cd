import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const DT: ReturnProps<ISap.ItemProps> = {
  title: "Material.Deterioration",
  origin: "SAP",
  type: "DT",
  permission: "DT",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Data",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro", row.adicionais) || "-";
      },
    },
    {
      name: "Reason",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Motivo", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <h3>{t("Observation")}</h3>
        <h4>{getAdditionalField("Observacao", row.adicionais)}</h4>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Material")}</td>
              <td>{t("Amount")}</td>
              <td>{t("Converted.Amount")}</td>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(rows) &&
              rows.map((item: ISap.ItemProps) => {
                return (
                  <tr key={`${item.documento}-${item.item}`}>
                    <td data-head={t("Material")} className="noCheckbox">
                      {item.material}
                    </td>
                    <td data-head={t("Amount")}>{item.qtd}</td>
                    <td data-head={t("Converted.Amount")}>{item.vlunit}</td>
                  </tr>
                );
              })}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
