import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const LC: ReturnProps<ISap.ItemProps> = {
  title: "Accounting.entry",
  origin: "SAP",
  type: "LC",
  permission: "LC",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data da Solicitação", row.adicionais) || "-";
      },
    },
    {
      name: "Status",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Status", row.adicionais) || "-";
      },
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Empresa", row.adicionais) || "-";
      },
    },
    {
      name: "Currency",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Moeda", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    const comment = getAdditionalField("COM", row.adicionais);

    if (comment && comment.length > 0) {
      return (
        <div id={`collapse-${row.documento}-${row.item}`}>
          <h3>{t("Comments")}</h3>
          <h4>{comment}</h4>
        </div>
      );
    }

    return "-";
  },
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Request.Date")}:`}</div>
            {getAdditionalField("Data da Solicitação", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Status")}:`}</div>
            {getAdditionalField("Status", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {getAdditionalField("Empresa", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Currency")}:`}</div>
            {getAdditionalField("Moeda", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {rowHeader?.vldoc || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Comments")}:`}</div>
            {getAdditionalField("COM", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => (
    <tbody>
      {Array.isArray(rows) &&
        rows.map((row) => (
          <tr
            key={`modal-table-internal-${row.documento}-${row.item}`}
            className="detailModalContent"
          >
            <td>
              <span>{t("Item")}</span>
              {getAdditionalField("Número do Item", row.adicionais).substring(
                7
              )}
            </td>

            <td>
              <span>{t("Release.key")}</span>
              {getAdditionalField("Chave de Lançamento", row.adicionais)}
            </td>

            <td>
              <span>{t("Account")}</span>
              {getAdditionalField("Conta", row.adicionais)}
            </td>
            <td>
              <span>{t("Description")}</span>
              {getAdditionalField("Descrição", row.adicionais)}
            </td>
            <td>
              <span>CC</span>
              {getAdditionalField("Centro de Custo", row.adicionais)}
            </td>
            <td>
              <span>{t("Division")}</span>
              {getAdditionalField("Divisão", row.adicionais)}
            </td>
            <td>
              <span>{`${t("Order")} / ${t("Item")}`}</span>
              {`${getAdditionalField("Ordem", row.adicionais) || "-"} / ${
                getAdditionalField("Item", row.adicionais) || "-"
              }`}
            </td>
            <td>
              <span>{t("Expiration")}</span>
              {getAdditionalField("Vencimento", row.adicionais)}
            </td>
            <td>
              <span>{t("Value")}</span>
              {getAdditionalField("Valor", row.adicionais)}
            </td>
            <td>
              <span>{t("Text")}</span>
              {getAdditionalField("SGTXT", row.adicionais)}
            </td>
          </tr>
        ))}
    </tbody>
  ),
};
