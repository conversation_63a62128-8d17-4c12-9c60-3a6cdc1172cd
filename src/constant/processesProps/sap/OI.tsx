import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { dateBDtoRead } from "../../../utils/Date";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const OI: ReturnProps<ISap.ItemProps> = {
  title: "End.Closing.Investment.Order",
  origin: "SAP",
  type: "OI",
  permission: "OI",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Requester.ID",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Solicitante", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return (
          dateBDtoRead(
            getAdditionalField("Data Solicitação", row.adicionais)
          ) || "-"
        );
      },
    },
    {
      name: "Operation.Start.Date",
      selector: (row: ISap.ItemProps) => {
        return (
          dateBDtoRead(
            getAdditionalField("Data Início Operação", row.adicionais)
          ) || "-"
        );
      },
    },
    {
      name: "Order.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nº Ordem", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Descrição Ordem", row.adicionais) || "-";
      },
    },
  ],
};
