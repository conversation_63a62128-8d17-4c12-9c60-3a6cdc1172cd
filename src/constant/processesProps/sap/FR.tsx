import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const FR: ReturnProps<ISap.ItemProps> = {
  title: "Freight.Request",
  origin: "SAP",
  type: "FR",
  permission: "FR",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Document.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Dt. Solicitação", row.adicionais) || "-";
      },
    },
    {
      name: "Document.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Dt. Documento", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Provider",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Freight.value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Request.Date")}:`}</div>
            {getAdditionalField("Dt. Solicitação", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Document.Date")}:`}</div>
            {getAdditionalField("Dt. Documento", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {getAdditionalField("Empresa", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("CTRC.Number")}:`}</div>
            {getAdditionalField("Nº CTRC", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Provider")}:`}</div>
            {rowHeader?.fornecedor || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Freight.value")}:`}</div>
            {rowHeader?.vldoc || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Division")}:`}</div>
            {getAdditionalField("Divisão", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Text")}:`}</div>
            {getAdditionalField("Campo Texto", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Reason.account.Cost.Center")}:`}
            </div>
            {`${
              getAdditionalField("Conta do Razão", rowHeader?.adicionais) || "-"
            } / ${
              getAdditionalField("Centro de Custo", rowHeader?.adicionais) ||
              "-"
            }`}
          </td>
        </tr>
      </tbody>
    );
  },
};
