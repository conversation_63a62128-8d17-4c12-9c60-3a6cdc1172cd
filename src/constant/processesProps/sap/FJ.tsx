import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const FJ: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Sales.Orders.FJ",
  origin: "SAP",
  type: "FJ",
  permission: "FJ",
  hasDetailModal: false,
  approveItems: true,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },
  headerColumns: [
    {
      name: "Sales.Orders.FJ",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Rule",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "FH.Sales.Branch",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CODFIL", row.adicionais) || "-";
      },
    },
    {
      name: "Customer.AE",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Regional.Manager",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZGEREG", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Branch.Manager",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZGEFIL", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Sales.Region",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("BZIRK", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Total.KG.Volume",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KWMENG", row.adicionais) || "-";
      },
    },
    {
      name: "Rob.Suggested",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL_YPVD", row.adicionais) || "-";
      },
    },
    {
      name: "FJ.DiscountR$",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL_YDIN", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Discount%",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("YDIN_YPVD_TOTAL", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value.II",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NETWR", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Item.IF",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material.Description.OB",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Amount",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Sales.Unit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VRKME", row.adicionais) || "-";
      },
    },
    {
      name: "Item Category",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PSTYV", row.adicionais) || "-";
      },
    },
    {
      name: "Suggested.Price",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KBETR", row.adicionais) || "-";
      },
    },
    {
      name: "Entered.Price",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: ".Price.Difference",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PRICECHG", row.adicionais) || "-";
      },
    },
    {
      name: "FJ.DiscountR$",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("YDIN_ITEM", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Item.Value.FJ",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
  ],
};
