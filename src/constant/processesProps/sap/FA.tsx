import { t } from "i18next";
import { v4 } from "uuid";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const FA: ReturnProps<ISap.ItemProps> = {
  title: "Changes.in.grain.purchase.orders",
  origin: "SAP",
  type: "FA",
  permission: "FA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "FA.Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Purchase.Order",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("PEDIDO", row.adicionais) || "-",
    },
    {
      name: "Request.ID",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Original.Quantity",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Quantidade Original", row.adicionais) || "-",
    },
    {
      name: "Original.Net.Value",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Preço Líquido Origin", row.adicionais) || "-",
    },
    {
      name: "FA.Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Data Solicitação", row.adicionais) || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;
    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("New.Shipment.Start.Date")}</td>
              <td>{t("New.Shipment.End.Date")}</td>
              <td>{t("Old.Quantity")}</td>
              <td>{t("New.Quantity")}</td>
              <td>{t("FA.Previous.payment")}</td>
              <td>{t("FA.New.payment")}</td>
              <td>{t("Current.Net.Value")}</td>
              <td>{t("FA.New.Liquid.Value")}</td>
              <td>{t("FA.New.Gross.Value")}</td>
              <td>{t("FA.Current.Aliq")}</td>
              <td>{t("FA.New.Aliq")}</td>
              <td>{t("FA.Original.Approver")}</td>
              <td>{t("Justification")}</td>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(rows) &&
              rows.map((add: any) => {
                const uuidtr = v4();
                return (
                  <tr key={`tr-purchase-order-${add.DOCUMENTO}-${uuidtr}`}>
                    <td
                      data-head={t("New.Shipment.Start.Date")}
                      className="noCheckbox"
                    >
                      {getAdditionalField(
                        "Data Inic. Remessa N",
                        add.adicionais
                      )}
                    </td>
                    <td data-head={t("New.Shipment.End.Date")}>
                      {getAdditionalField(
                        "Data Fim Remessa Nov",
                        add.adicionais
                      )}
                    </td>
                    <td data-head={t("Old.Quantity")}>
                      {getAdditionalField("QTDE ANTIGA", add.adicionais)}
                    </td>
                    <td data-head={t("New.Quantity")}>
                      {getAdditionalField("QTDE NOVA", add.adicionais)}
                    </td>
                    <td data-head={t("FA.Previous.payment")}>
                      {getAdditionalField("COND.PGTO ANTIGA", add.adicionais)}
                    </td>
                    <td data-head={t("FA.New.payment")}>
                      {getAdditionalField("COND.PGTO NOVA", add.adicionais)}
                    </td>
                    <td data-head={t("Current.Net.Value")}>
                      {getAdditionalField("VALOR ATUAL", add.adicionais)}
                    </td>
                    <td data-head={t("FA.New.Liquid.Value")}>
                      {getAdditionalField("VALOR LIQ.NOVO", add.adicionais)}
                    </td>
                    <td data-head={t("FA.New.Gross.Value")}>
                      {getAdditionalField("VALOR BRUTO.NOVO", add.adicionais)}
                    </td>
                    <td data-head={t("FA.Current.Aliq")}>
                      {getAdditionalField("ALIQ. ATUAL", add.adicionais)}
                    </td>
                    <td data-head={t("FA.New.Aliq")}>
                      {getAdditionalField("ALIQ.NOVA", add.adicionais)}
                    </td>
                    <td data-head={t("FA.Original.Approver")}>
                      {getAdditionalField("Aprovador Original", add.adicionais)}
                    </td>

                    <td data-head={t("Justification")}>
                      {getAdditionalField("JUSTIFICATIVA", add.adicionais)}
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("From.To")}</td>
            </tr>
          </thead>
          <tbody>
            {(() => {
              const deParaFields = [
                getAdditionalField("DE_PARA1", row.adicionais),
                getAdditionalField("DE_PARA2", row.adicionais),
                getAdditionalField("DE_PARA3", row.adicionais),
                getAdditionalField("DE_PARA4", row.adicionais),
              ].filter((field) => field && field.trim() !== "");

              if (deParaFields.length === 0) {
                return (
                  <tr>
                    <td data-head={t("From.To")}>-</td>
                  </tr>
                );
              }

              return deParaFields.map((field, index) => (
                <tr key={`depara-$add.DOCUMENTO}-${index}`}>
                  <td data-head={t("From.To")}>
                    {(() => {
                      const paraIndex = field.toLowerCase().indexOf("para");
                      if (paraIndex !== -1) {
                        const before = field.slice(0, paraIndex + 4);
                        const after = field.slice(paraIndex + 4);
                        return (
                          <>
                            {before}
                            <b>{after}</b>
                          </>
                        );
                      }
                      return field;
                    })()}
                  </td>
                </tr>
              ));
            })()}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
