import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import {
  formatToBRLFloatAmount,
  toPtBrCurrencyWithoutDecimal,
} from "../../../utils/Currency";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const AO: ReturnProps<ISap.ItemProps> = {
  title: "Eggs.Payment",
  origin: "SAP",
  type: "AO",
  permission: "AO",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return row.fornecedor;
  },
  headerColumns: [
    {
      name: "Eggs.Payment.Producer.Code",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Eggs.Payment.Producer.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NAME1", row.adicionais) || "-";
      },
    },
    {
      name: "Eggs.Payment.Producer.Invoice",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Eggs.Payment.Matrix.Lot",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("LOTE", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) =>
        `R$ ${formatToBRLFloatAmount(row.vldoc) || "-"}`,
    },
  ],
  detailColumns: [
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Eggs.Payment.SAP.Product.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MAKTX", row.adicionais) || "-";
      },
    },
    {
      name: "Eggs.Payment.Number.Eggs",
      selector: (row: ISap.ItemProps) => {
        return toPtBrCurrencyWithoutDecimal(row.qtd) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) =>
        `R$ ${formatToBRLFloatAmount(row.vlitem) || "-"}`,
    },
  ],
};
