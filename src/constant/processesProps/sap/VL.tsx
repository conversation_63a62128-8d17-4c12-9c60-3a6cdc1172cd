import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const VL: ReturnProps<ISap.ItemProps> = {
  title: "Seal.Anomaly",
  origin: "SAP",
  type: "VL",
  permission: "VL",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return row.centro;
  },
  additional2: (row: ISap.ItemProps) => {
    return row.data_emissao;
  },
  headerColumns: [
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "N.Weighing.Ticket.CF",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Arrival.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Vendor.CF",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Purchasing.Doc.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PEDIDO", row.adicionais) || "-";
      },
    },
    {
      name: "Invoice.VL",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NF", row.adicionais) || "-";
      },
    },
    {
      name: "License.Plate",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PLACA", row.adicionais) || "-";
      },
    },
    {
      name: "Anomaly",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ANOMALIA", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SOLICITANTE", row.adicionais) || "-";
      },
    },
    {
      name: "Controllers.Observation.VL",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBSERVACAO", row.adicionais) || "-";
      },
    },
  ],
};
