import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const TR_SAP: ReturnProps<ISap.ItemProps> = {
  title: "Tributes.SAP",
  origin: "SAP",
  type: "TR_SAP",
  permission: "TR",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company.Code",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Type.Tax",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("tipo IMPOSTO", row.adicionais) || "-";
      },
    },
    {
      name: "Pstng.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA LCTO", row.adicionais) || "-";
      },
    },
    {
      name: "Due.Date.TR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA VCTO", row.adicionais) || "-";
      },
    },
    {
      name: "Vendor.TR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("fornecedor", row.adicionais) || "-";
      },
    },
    {
      name: "Value.TR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VALOR TOTAL", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Observations.TR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBSERVAÇÃO", row.adicionais) || "-";
      },
    },
    {
      name: "Account.TR",
      cell: (row: ISap.ItemProps) => {
        return (
          <div>
            <div> {getAdditionalField("CC_PRINC", row.adicionais) || "-"},</div>
            <div> {getAdditionalField("CC_MULTA", row.adicionais) || "-"},</div>
            <div>{getAdditionalField("CC_JUROS", row.adicionais) || "-"},</div>
            <div>{getAdditionalField("CC_OUTENT", row.adicionais) || "-"},</div>
          </div>
        );
      },
    },
    {
      name: "Business.Area.TR",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIVISÃO", row.adicionais) || "-";
      },
    },
    {
      name: "CC/OI",
      cell: (row: ISap.ItemProps) => {
        return (
          <div>
            <div>{getAdditionalField("CCOI_PRINC", row.adicionais) || "-"}</div>
            <div>{getAdditionalField("CCOI_MULTA", row.adicionais) || "-"}</div>
            <div>{getAdditionalField("CCOI_JUROS", row.adicionais) || "-"}</div>
            <div>
              {getAdditionalField("CCOI_OUTENT", row.adicionais) || "-"}
            </div>
          </div>
        );
      },
    },
    {
      name: "Amount.TR",
      cell: (row: ISap.ItemProps) => {
        return (
          <div>
            <div>{getAdditionalField("VL_PRINC", row.adicionais) || "-"}</div>
            <div>{getAdditionalField("VL_MULTA", row.adicionais) || "-"}</div>
            <div>{getAdditionalField("VL_JUROS", row.adicionais) || "-"}</div>
            <div>{getAdditionalField("VL_OUTENT", row.adicionais) || "-"}</div>
          </div>
        );
      },
    },
  ],
};
