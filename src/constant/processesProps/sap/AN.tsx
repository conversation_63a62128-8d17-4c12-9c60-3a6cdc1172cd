import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const AN: ReturnProps<ISap.ItemProps> = {
  title: "Document.Gan",
  origin: "SAP",
  type: "AN",
  permission: "AN",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Coletiva.Document.Gan",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Request.Of.Date.AF",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
  ],
  detailColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Documento", row.adicionais) || "-";
      },
    },
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Material", row.adicionais) || "-";
      },
    },
    {
      name: "Material.description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Descrição material", row.adicionais) || "-";
      },
    },
    {
      name: "Supplier",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nome Fornecedor", row.adicionais) || "-";
      },
    },
    {
      name: "Quantity",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Quantidade", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
  ],
};
