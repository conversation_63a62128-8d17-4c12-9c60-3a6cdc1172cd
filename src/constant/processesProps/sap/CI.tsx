import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const CI: ReturnProps<ISap.ItemProps> = {
  title: "Claim.International",
  origin: "SAP",
  type: "CI",
  permission: "CI",
  hasDetailModal: false,
  approveItems: false,
  headerColumns: [
    {
      name: "Number.Of.Case",
      cell: (row: ISap.ItemProps) => {
        const link = getAdditionalField("LINK", row?.adicionais);
        const document = getAdditionalField("PARENT_CASE", row?.adicionais);
        return link ? (
          <a href={link} target="_blank" rel="noreferrer">
            {document}
          </a>
        ) : (
          <p>{document || "-"}</p>
        );
      },
    },
    {
      name: "CI.Date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("CLIENT_COMPLAINT_DAT", row.adicionais) || "-",
    },
    {
      name: "Categorization",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("CATEGORIZATION", row.adicionais) || "-",
    },
    {
      name: "Marketplace",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("MARKET", row.adicionais) || "-",
    },
    {
      name: "Region",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("REGION", row.adicionais) || "-",
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("KUNNR", row.adicionais) || "-",
    },
    {
      name: "Currency",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("CURRENCY_SF", row.adicionais) || "-",
    },
    {
      name: "Claimed.Amount",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("CLAIMED_AMOUNT", row.adicionais) || "-",
    },
    {
      name: "Focal.point",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("FOCAL_POINT", row.adicionais) || "-",
    },
    {
      name: "Technical.manager",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("TECH_RESP", row.adicionais) || "-",
    },
  ],
  detailColumns: [
    {
      name: "SKU",
      cell: (row: ISap.ItemProps) => {
        const skus: string[] = [];
        row?.adicionais?.forEach((item) => {
          if (item.campo && item.campo.startsWith("MATNR_")) {
            skus.push(String(item.valor));
          }
        });

        return (
          <div>
            {skus.length > 0
              ? skus.map((sku, index) => <div key={index}>{sku}</div>)
              : "-"}
          </div>
        );
      },
    },
    {
      name: "Amount",
      cell: (row: ISap.ItemProps) => {
        const amounts: string[] = [];
        row?.adicionais?.forEach((item) => {
          if (item.campo && item.campo.startsWith("QUANTITY_CLAIMED_")) {
            amounts.push(String(item.valor));
          }
        });

        return (
          <div>
            {amounts.length > 0
              ? amounts.map((amount, index) => <div key={index}>{amount}</div>)
              : "-"}
          </div>
        );
      },
    },
    {
      name: "CI.Un",
      cell: (row: ISap.ItemProps) => {
        const CI_Uns: string[] = [];
        row?.adicionais?.forEach((item) => {
          if (item.campo && item.campo.startsWith("UNIT_UOM_")) {
            CI_Uns.push(String(item.valor));
          }
        });

        return (
          <div>
            {CI_Uns.length > 0
              ? CI_Uns.map((CI_Un, index) => <div key={index}>{CI_Un}</div>)
              : "-"}
          </div>
        );
      },
    },
    {
      name: "CI.Sif",
      cell: (row: ISap.ItemProps) => {
        const CI_Sifs: string[] = [];
        row?.adicionais?.forEach((item) => {
          if (item.campo && item.campo.startsWith("SIF_ID_")) {
            CI_Sifs.push(String(item.valor));
          }
        });

        return (
          <div>
            {CI_Sifs.length > 0
              ? CI_Sifs.map((CI_Sif, index) => <div key={index}>{CI_Sif}</div>)
              : "-"}
          </div>
        );
      },
    },
  ],
};
