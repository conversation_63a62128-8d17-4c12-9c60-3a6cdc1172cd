import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const RA: ReturnProps<ISap.ItemProps> = {
  title: "Analysis.And.Anomaly",
  origin: "SAP",
  type: "RA",
  permission: "RA",
  hasDetailModal: true,
  hasDetailRoute: true,
  additional1: (row: ISap.ItemProps) =>
    getAdditionalField("EXERCICIO", row.adicionais),
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Year",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("EXERCICIO", row.adicionais) || "-",
    },
    {
      name: "Currency",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("MOEDA", row.adicionais) || "-",
    },
    {
      name: "Month",
      selector: (row: ISap.ItemProps) => row.mes || "-",
    },
    {
      name: "Storage",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DEPOSITO", row.adicionais) || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("TIPO_INVENTARIO", row.adicionais) || "-",
    },
    {
      name: "Text",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("TXT_OQUE", row.adicionais) || "-",
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <tbody>
        {Array.isArray(rowHeader) &&
          rowHeader?.map((detail: ISap.ItemProps, index: number) => (
            <tr key={`detail-${index}-${detail.item || index}`}>
              <td>
                <div className="tableTitle">{`${t("Item")}:`}</div>
                {detail.item || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Document")}:`}</div>
                {getAdditionalField("IBLNR", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Currency")}:`}</div>
                {getAdditionalField("MOEDA", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Absolute")}:`}</div>
                {getAdditionalField("ABSOLUTO", detail.adicionais) || "-"}
              </td>
            </tr>
          ))}
      </tbody>
    );
  },
};
