import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const SP: ReturnProps<ISap.ItemProps> = {
  title: "Plate.Change",
  origin: "SAP",
  type: "SP",
  permission: "SP",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Plate",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PLACA", row.adicionais) || "-";
      },
    },
    {
      name: "Requested.Field",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CAMPO", row.adicionais) || "-";
      },
    },
    {
      name: "Create.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA", row.adicionais) || "-";
      },
    },
    {
      name: "Shipping.Location",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("LEXP", row.adicionais) || "-";
      },
    },
  ],
  hasDetailRoute: true,
  detailModalHeader: async (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    const attArray = String(
      getAdditionalField("ANEXO_1", rowHeader?.adicionais)
    )?.split(";");
    const attName = attArray[0];
    const attNumber = attArray[1];

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Solicitation.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Plate")}:`}</div>
            {getAdditionalField("PLACA", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Title")}:`}</div>
            {getAdditionalField("CAMPO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Start.Date")}:`}</div>
            {getAdditionalField("DATA", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("User.ID")}:`}</div>
            {getAdditionalField("ERNAM", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("New.Value")}:`}</div>
            {getAdditionalField("VL_NV", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Old.Value.SP")}:`}</div>
            {getAdditionalField("VL_ANT", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Observation.SP")}:`}</div>
            {getAdditionalField("JUSTF", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Attachment")}:`}</div>
            <a
              href={`${
                import.meta.env.VITE_SP_ATACHMENT_URL_BASE
              }${attNumber}?${import.meta.env.VITE_BLOB_STORAGE_TOKEN}`}
              download
              target="_blank"
            >
              {attName || "-"}
            </a>
          </td>
        </tr>
      </tbody>
    );
  },
};
