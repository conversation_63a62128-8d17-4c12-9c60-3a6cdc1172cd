import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const FH: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Sales.Orders.FH",
  origin: "SAP",
  type: "FH",
  permission: "FH",
  hasDetailModal: false,
  approveItems: true,
  additional1: (row: ISap.ItemProps) => row.tipo,
  headerColumns: [
    {
      name: "Sales.Orders",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Rule",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "FH.Sales.Branch",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CODFIL", row.adicionais) || "-";
      },
    },
    {
      name: "Client.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Regional.Manager",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZGEREG", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Branch.Manager",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZGEFIL", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Sales.Region",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("BZIRK", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Total.KG.Volume",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KWMENG", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Rob.Table",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ROB_SUG_TOTAL", row.adicionais) || "-";
      },
    },
    {
      name: "FH.FIFO.Waiver",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("RENUNCIA_FIFO", row.adicionais) || "-";
      },
    },
    {
      name: "FH.Discount%",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("FIFO_ROB_TOTAL", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material.Description.OB",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Quantity",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Seller.Unity",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VRKME", row.adicionais) || "-";
      },
    },
    {
      name: "Item Category",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PSTYV", row.adicionais) || "-";
      },
    },
    {
      name: "Sugest.Price",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KBETR", row.adicionais) || "-";
      },
    },
    {
      name: "Inserted.Price",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: ".Price.Difference",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PRICECHG", row.adicionais) || "-";
      },
    },
    {
      name: "FH.FIFO.Waiver",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("RENUNCIA_FIFO_ITEM", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Item.Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
  ],
};
