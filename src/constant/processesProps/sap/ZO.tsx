import { ISap } from "@/interfaces";
import { getAdditionalField } from "@/utils/GetItemsSap";
import { ReturnProps } from "..";

export const ZO: ReturnProps<ISap.ItemProps> = {
  title: "ODR.Requests",
  origin: "SAP",
  type: "ZO",
  permission: "ZO",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Claim.ZO",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "ODR.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Cost.Center.ZO",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Cliente", row.adicionais) || "-",
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  detailColumns: [
    {
      name: "Product.OneLog",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Descrição Material", row.adicionais) || "-";
      },
    },
  ],
};
