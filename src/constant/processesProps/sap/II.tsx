import { ExpandMoreOutlined } from "@mui/icons-material";
import { t } from "i18next";
import React from "react";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import {
  DivCollapse,
  Intbody,
  Intdcenter,
  Intdleft,
  Inthead,
  TableInternal,
} from "../styles";

export const II: ReturnProps<ISap.ItemProps> = {
  title: "Fixed.Asset",
  origin: "SAP",
  type: "II",
  permission: "II",
  hasDetailModal: true,
  hasDetailRoute: true,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Empresa", row.adicionais) || "-",
    },
    {
      name: "Solicitation.date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Data Solicitação", row.adicionais) || "-",
    },
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Centro", row.adicionais) || "-",
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Centro de Custo", row.adicionais) || "-",
    },
    {
      name: "II.Applicant.Name",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Solicitante Nome", row.adicionais) || "-",
    },
  ],
  detailModalHeader: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(rows) ? rows[0] : rows;

    const groupItemsByType = (items: ISap.ItemProps[]) => {
      const groupedItems: { [key: string]: ISap.ItemProps[] } = {};

      items.forEach((item) => {
        if (!groupedItems[item.tipo]) {
          groupedItems[item.tipo] = [];
        }
        groupedItems[item.tipo].push(item);
      });

      return groupedItems;
    };

    const groupedItems = groupItemsByType(Array.isArray(rows) ? rows : [rows]);

    const translateType = (type: string) => {
      switch (type) {
        case "BAIX":
          return t("Type.BAIX");
        case "TINT":
          return t("Type.TINT");
        case "TEXT":
          return t("Type.TEXT");
        case "OUT":
          return t("Type.OUT");
        default:
          return type;
      }
    };

    const callbackItemDropdown = (type: string) => {
      const collapseElement = document.getElementById(`collapse-${type}`);
      if (collapseElement) {
        collapseElement.classList.toggle("show");
      }
    };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Solicitation.Number")}:`}</div>
            {rowHeader.documento}
          </td>
        </tr>
        <tr>
          <td>
            {Object.keys(groupedItems).map((tipo) => (
              <React.Fragment key={tipo}>
                <TableInternal>
                  <Intbody className="borderBottom">
                    <tr>
                      <td style={{ width: "30%" }}>
                        <div className="tableTitle">{t("Type.Movement")}</div>
                        {translateType(tipo)}
                      </td>
                      <td style={{ width: "30%" }}>
                        <div className="tableTitle">{t("Quantity")}</div>
                        {groupedItems[tipo].length}
                      </td>
                      <td style={{ width: "30%" }}>
                        <div className="tableTitle">{t("Book.Value.II")}</div>
                        {groupedItems[tipo][0].vldoc}
                      </td>
                      <Intdcenter style={{ width: "10%" }}>
                        <div data-translation={t("Expand")}>
                          <ExpandMoreOutlined
                            id={`iconSpan-${tipo}`}
                            fontSize="medium"
                            onClick={(e) => {
                              callbackItemDropdown(tipo);
                              e.currentTarget.classList.toggle("show");
                            }}
                          />
                        </div>
                      </Intdcenter>
                    </tr>
                  </Intbody>
                </TableInternal>
                <DivCollapse id={`collapse-${tipo}`}>
                  <TableInternal>
                    <Inthead>
                      <tr>
                        <th style={{ width: "5%" }}>{t("Item")}</th>
                        <th style={{ width: "15%" }}>{t("Internal.number")}</th>
                        <th style={{ width: "10%" }}>{t("Quantity")}</th>
                        <th style={{ width: "35%" }}>
                          {t("Fixed.asset.description")}
                        </th>
                        <th style={{ width: "15%" }}>{t("Book.Value.II")}</th>
                        <th style={{ width: "20%" }}>
                          {t("Transfer.Reason.II")}
                        </th>
                      </tr>
                    </Inthead>
                    <Intbody>
                      {groupedItems[tipo].map((item: any) => (
                        <tr key={item.item}>
                          <Intdleft
                            style={{ width: "5%" }}
                            data-head={t("Item")}
                            className="noCheckbox"
                          >
                            <div>{item.item}</div>
                          </Intdleft>
                          <Intdleft
                            style={{ width: "15%" }}
                            data-head={t("Internal.number")}
                          >
                            <div>
                              {getAdditionalField(
                                "NRINTERNO",
                                item.adicionais
                              ) || "-"}
                            </div>
                          </Intdleft>
                          <Intdleft
                            style={{ width: "10%" }}
                            data-head={t("Quantity")}
                          >
                            <div>
                              {getAdditionalField(
                                "QUANTIDADE",
                                item.adicionais
                              ) || "-"}
                            </div>
                          </Intdleft>
                          <Intdleft
                            style={{ width: "35%" }}
                            data-head={t("Fixed.asset.description")}
                          >
                            <div>
                              {getAdditionalField(
                                "DESCRICAO",
                                item.adicionais
                              ) || "-"}
                            </div>
                          </Intdleft>
                          <Intdleft
                            style={{ width: "15%" }}
                            data-head={t("Book.Value.II")}
                          >
                            <div>
                              {getAdditionalField(
                                "VALORCONTABIL",
                                item.adicionais
                              ) || "-"}
                            </div>
                          </Intdleft>
                          <Intdleft
                            style={{ width: "20%" }}
                            data-head={t("Transfer.Reason.II")}
                          >
                            <div>
                              {getAdditionalField(
                                "JUSTIFICATIVA",
                                item.adicionais
                              ) || "-"}
                            </div>
                          </Intdleft>
                        </tr>
                      ))}
                    </Intbody>
                  </TableInternal>
                </DivCollapse>
              </React.Fragment>
            ))}
          </td>
        </tr>
      </tbody>
    );
  },
};
