import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const H5: ReturnProps<ISap.ItemProps> = {
  title: "Vacation",
  origin: "SAP",
  type: "H5",
  permission: "H5",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Employee",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nome", row.adicionais) || "-";
      },
    },
    {
      name: "Acquisition.Period",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Per. Aquisitivo", row.adicionais) || "-";
      },
    },
    {
      name: "Schedule",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Programação", row.adicionais) || "-";
      },
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data Solicit.", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Period",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Período", row.adicionais) || "-";
      },
    },
    {
      name: "Vacation.Days",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Dias Gozo", row.adicionais) || "-";
      },
    },
    {
      name: "Allowance",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Abono", row.adicionais) || "-";
      },
    },
    {
      name: "13th.Salary",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("13 Sal.", row.adicionais) || "-";
      },
    },
    {
      name: "Fractional.Vacation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Férias Frac.", row.adicionais) || "-";
      },
    },
  ],
};
