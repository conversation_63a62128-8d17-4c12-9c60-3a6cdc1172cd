import { ISap } from "@/interfaces";
import { getAdditionalField } from "@/utils/GetItemsSap";
import { ReturnProps } from "..";

export const LS: ReturnProps<ISap.ItemProps> = {
  title: "Request.Purchase.Materials.Starship",
  origin: "SAP",
  type: "LS",
  permission: "LS",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Surplus.Quantity",
      selector: (row: ISap.ItemProps) => {
        const qtd = row.qtd || "";
        const umsobra = getAdditionalField("UMSOBRA", row.adicionais) || "";
        return `${qtd} ${umsobra}`.trim();
      },
    },
    {
      name: "Surplus.Value",
      selector: (row: ISap.ItemProps) => {
        const vldoc = row.vldoc || "";
        const moeda = getAdditionalField("MOEDA", row.adicionais) || "";
        return `${vldoc} ${moeda}`.trim();
      },
    },
    {
      name: "Minimum.Lot",
      selector: (row: ISap.ItemProps) => {
        const qtdlotemin =
          getAdditionalField("QTDLOTEMIN", row.adicionais) || "";
        const umlotemin = getAdditionalField("UMLOTEMIN", row.adicionais) || "";
        return `${qtdlotemin} ${umlotemin}`.trim();
      },
    },
    {
      name: "Required.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
  ],
  detailColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Document.Type",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("OPCODE", row.adicionais || "") || "-",
    },
    {
      name: "Products",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("PRODUTO", row.adicionais || "") || "-",
    },
    {
      name: "Product.Quantity",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("QTDPROD", row.adicionais || "") || "-",
    },
  ],
};
