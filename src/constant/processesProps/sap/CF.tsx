import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const CF: ReturnProps<ISap.ItemProps> = {
  title: "Weight.Divergence.Aware",
  origin: "SAP",
  type: "CF",
  permission: "CF",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return `${row.tipo};${row.data_emissao}`;
  },
  additional2: (row: ISap.ItemProps) => {
    return row.centro;
  },
  headerColumns: [
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "N.Weighing.Ticket.CF",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Arrival.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Vendor.CF",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Purchasing.Doc.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PEDIDO", row.adicionais) || "-";
      },
    },
    {
      name: "Divergent.Quantity.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIVERGENCIA", row.adicionais) || "-";
      },
    },
    {
      name: "Aware.Type.CF",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
  ],
  detailColumns: [
    {
      name: "Invoice.Quantity.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("QUANT_NF", row.adicionais) || "-";
      },
    },
    {
      name: "Received.Quantity.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("QUANT_RECEB", row.adicionais) || "-";
      },
    },
    {
      name: "Identifier.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("INDICADOR", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SOLICITANTE", row.adicionais) || "-";
      },
    },
    {
      name: "Aware.Type.CF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TIPO_CIENCIA", row.adicionais) || "-";
      },
    },
  ],
};
