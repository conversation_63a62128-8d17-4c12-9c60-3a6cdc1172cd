import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const CA: ReturnProps<ISap.ItemProps> = {
  title: "Bonus.Request",
  origin: "SAP",
  type: "CA",
  permission: "CA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "CAP.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data CAP", row.adicionais) || "-";
      },
    },
    {
      name: "Seller.Organization",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Org. Vendas", row.adicionais) || "-";
      },
    },
    {
      name: "Seller.Office",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Escrit. Vendas", row.adicionais) || "-";
      },
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Cliente", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  detailColumns: [
    {
      name: "Order.Type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Tp.de Ordem", row.adicionais) || "-";
      },
    },
    {
      name: "Budget",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Verba", row.adicionais) || "-";
      },
    },
    {
      name: "Observation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Observação", row.adicionais) || "-";
      },
    },
  ],
};
