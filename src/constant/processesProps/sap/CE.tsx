import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import {
  getAdditionalField,
  getPartialWordFields,
} from "../../../utils/GetItemsSap";

export const CE: ReturnProps<ISap.ItemProps> = {
  title: "Complements.Approval.CE",
  origin: "SAP",
  type: "CE",
  permission: "CE",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Request.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Creation.Area",
      selector: (row: ISap.ItemProps) => {
        return `${row.centro} - ${
          getAdditionalField("NOME2", row.adicionais) || "-"
        }`;
      },
    },
    {
      name: "Integrated",
      selector: (row: ISap.ItemProps) => {
        return `${row.fornecedor} - ${
          getAdditionalField("NAME1", row.adicionais) || "-"
        }`;
      },
    },
    {
      name: "Lot",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("LOTE", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row) => row.vldoc.replace(/ /g, "") || "-",
    },
  ],
  detailColumns: [
    {
      name: "Integration.System",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CADEIA", row.adicionais) || "-";
      },
    },
    {
      name: "Reason",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MOTIVO", row.adicionais) || "-";
      },
    },
    {
      name: "Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESCRICAO", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc.replace(/ /g, "") || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return `${row.requisitante || "-"} - ${
          getAdditionalField("NOME3", row.adicionais) || "-"
        }`;
      },
    },
    {
      name: "Attachment.CA",
      selector: (row: ISap.ItemProps) => {
        const attachments = getPartialWordFields("ANEXO_1", row.adicionais);
        if (attachments.length) {
          return attachments
            .map((att) => {
              const [attName] = String(att).split(";");
              return attName || "anexo";
            })
            .join(", ");
        }
        return "-";
      },
    },
  ],
};
