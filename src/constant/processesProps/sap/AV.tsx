import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";
export const AV: ReturnProps<ISap.ItemProps> = {
  title: "Sales.Contract",
  origin: "SAP",
  type: "AV",
  permission: "AV",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro ?? "-",
    },
    {
      name: "Sequential.AV",
      selector: (row: ISap.ItemProps) => row.documento ?? "-",
    },
    {
      name: "Type.Of.Request.AV",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("SITUACAO", row.adicionais) || "-",
    },
    {
      name: "Material.NC",
      selector: (row: ISap.ItemProps) => row.material ?? "-",
    },
    {
      name: "Vendor.CF",
      selector: (row: ISap.ItemProps) => row.fornecedor ?? "-",
    },
    {
      name: "Request.date",
      selector: (row: ISap.ItemProps) => row.data_emissao ?? "-",
    },
    {
      name: "Quantity",
      selector: (row: ISap.ItemProps) => row.qtd ?? "-",
    },
    {
      name: "Total.Value.II",
      selector: (row: ISap.ItemProps) => row.vldoc ?? "-",
    },
    {
      name: "Start.Shipping",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_REM_INI", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Incoterms")}</td>
              <td>{t("Amount.Value")}</td>
              <td>{t("Date.Received")}</td>
              <td>{t("End.Shipping")}</td>
              <td>{t("Initial.Approver")}</td>
              <td>{t("AB.Requester")}</td>
              <td>{t("Client")}</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-head={t("Incoterms")}>
                {getAdditionalField("INCOT", row.adicionais) || "-"}
              </td>
              <td data-head={t("Amount.Value")}>
                {getAdditionalField("Montante", row.adicionais) || "-"}
              </td>
              <td data-head={t("Date.Received")}>
                {getAdditionalField("DATA_RECEB", row.adicionais) || "-"}
              </td>
              <td data-head={t("End.Shipping")}>
                {getAdditionalField("DATA_REM_FIM", row.adicionais) || "-"}
              </td>
              <td data-head={t("Initial.Approver")}>
                {getAdditionalField("Aprovador inicial", row.adicionais) || "-"}
              </td>
              <td data-head={t("AB.Requester")}>{row.requisitante || "-"}</td>
              <td data-head={t("Client")}>
                {getAdditionalField("CLIENTE", row.adicionais) || "-"}
              </td>
            </tr>
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("From.To")}</td>
            </tr>
          </thead>
          <tbody>
            {(() => {
              const deParaFields = [
                getAdditionalField("DE_PARA1", row.adicionais),
                getAdditionalField("DE_PARA2", row.adicionais),
                getAdditionalField("DE_PARA3", row.adicionais),
                getAdditionalField("DE_PARA4", row.adicionais),
                getAdditionalField("DE_PARA5", row.adicionais),
              ].filter((field) => field && field.trim() !== "");

              if (deParaFields.length === 0) {
                return (
                  <tr>
                    <td data-head={t("From.To")}>-</td>
                  </tr>
                );
              }

              return deParaFields.map((field) => (
                <tr key={`depara-${row.adicionais}-${field}`}>
                  <td data-head={t("From.To")}>
                    {(() => {
                      const paraIndex = field.toLowerCase().indexOf("para");
                      if (paraIndex !== -1) {
                        const before = field.slice(0, paraIndex + 4);
                        const after = field.slice(paraIndex + 4);
                        return (
                          <>
                            {before}
                            <b>{after}</b>
                          </>
                        );
                      }
                      return field;
                    })()}
                  </td>
                </tr>
              ));
            })()}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
