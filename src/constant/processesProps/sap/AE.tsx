import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { dateBDtoRead } from "../../../utils/Date";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const AE: ReturnProps<ISap.ItemProps> = {
  title: "Customer.Unload.Scheduling",
  origin: "SAP",
  type: "AE",
  permission: "AE",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "No.Group.AE",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Customer.AE",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Schedule.Date.AE",
      selector: (row: ISap.ItemProps) => {
        return row.data_emissao ? dateBDtoRead(row.data_emissao) : "-";
      },
    },
    {
      name: "Cap.Day",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CAP_DIA", row.adicionais) || "-";
      },
    },
    {
      name: "Cap.Used",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CAP_UTILIZADA", row.adicionais) || "-";
      },
    },
    {
      name: "Cap.Available",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CAP_DISP", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Category",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CATEGORIA", row.adicionais) || "-";
      },
    },
    {
      name: "Conservation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CONSERVACAO", row.adicionais) || "-";
      },
    },
    {
      name: "Start.Unload",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("INICIO_DESCARGA", row.adicionais) || "-";
      },
    },
    {
      name: "End.Unload",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("FIM_DESCARGA", row.adicionais) || "-";
      },
    },
    {
      name: "Protocol",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PROTOCOLO", row.adicionais) || "-";
      },
    },
    {
      name: "Observation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBSERVACAO", row.adicionais) || "-";
      },
    },
  ],
};
