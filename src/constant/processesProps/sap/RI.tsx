import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const RI: ReturnProps<ISap.ItemProps> = {
  title: "Inventory.Rescheduling",
  origin: "SAP",
  type: "RI",
  permission: "RI",
  hasDetailModal: true,
  hasDetailRoute: true,
  additional1: (row: ISap.ItemProps) =>
    getAdditionalField("EXERCICIO", row.adicionais),
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Storage",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DEPOSITO", row.adicionais) || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("TIPO_INVENTARIO", row.adicionais) || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("SOLICITANTE", row.adicionais) || "-",
    },
    {
      name: "Current.Date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DT_INI_PROG_INV_ANTI", row.adicionais) || "-",
    },
    {
      name: "Requested.Date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DT_INI_PROG_INV_NOVA", row.adicionais) || "-",
    },
    {
      name: "Reason",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("MOTIVO", row.adicionais) || "-",
    },
  ],
};
