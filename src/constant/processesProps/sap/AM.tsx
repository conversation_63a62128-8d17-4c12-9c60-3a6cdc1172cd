import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const AM: ReturnProps<ISap.ItemProps> = {
  title: "Self.Management.Payment.of.Medical.Assistance",
  origin: "SAP",
  type: "AM",
  permission: "AM",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
  detailColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Event.Start.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_INICIO", row.adicionais) || "-";
      },
    },
    {
      name: "Due.date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_VENCTO", row.adicionais) || "-";
      },
    },
    {
      name: "Health.insurance.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CONVENIO", row.adicionais) || "-";
      },
    },
    {
      name: "Worker.BRF.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("REGIONAL", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("valor", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    return (
      <TableInternal>
        <thead>
          <tr>
            <td>{t("Item")}</td>
            <td>{t("Event.Start.Date")}</td>
            <td>{t("AM.Due.Date")}</td>
            <td>{t("Health.insurance.Description")}</td>
            <td>{t("Worker.BRF.Center")}</td>
            <td>{t("Value")}</td>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(rows) &&
            rows?.map((item: ISap.ItemProps) => {
              return (
                <tr>
                  <td data-head={t("Item")} className="noCheckbox">
                    {item.item || "-"}
                  </td>
                  <td data-head={t("Event.Start.Date")} className="noCheckbox">
                    {getAdditionalField("DATA_INICIO", item.adicionais) || "-"}
                  </td>
                  <td data-head={t("Due.date")}>
                    {getAdditionalField("DATA_VENCTO", item.adicionais) || "-"}
                  </td>
                  <td data-head={t("Health.insurance.Description")}>
                    {getAdditionalField("CONVENIO", item.adicionais) || "-"}
                  </td>
                  <td data-head={t("Worker.BRF.Center")}>
                    {getAdditionalField("REGIONAL", item.adicionais) || "-"}
                  </td>
                  <td data-head={t("Value")}>
                    {getAdditionalField("valor", item.adicionais) || "-"}
                  </td>
                </tr>
              );
            })}
        </tbody>
      </TableInternal>
    );
  },
};
