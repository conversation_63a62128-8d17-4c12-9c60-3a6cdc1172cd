import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const DP: ReturnProps<ISap.ItemProps> = {
  title: "Weight.Divergence.Request",
  origin: "SAP",
  type: "DP",
  permission: "DP",
  hasDetailModal: true,
  additional2: (row: ISap.ItemProps) => {
    return row.centro;
  },
  headerColumns: [
    {
      name: "Car.plate",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PLACA", row.adicionais) || "-";
      },
    },
    {
      name: "PP.Avaliation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("AVALIACAO_PP", row.adicionais) || "-";
      },
    },
    {
      name: "Ticket",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TICKET", row.adicionais) || "-";
      },
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Weight.DOC.Pallets",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PESO_DOC_PALETE", row.adicionais) || "-";
      },
    },
    {
      name: "DNIT.Difference",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIVER_PES_DNIT", row.adicionais) || "-";
      },
    },
    {
      name: "NF.Difference",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIVER_PES_NF", row.adicionais) || "-";
      },
    },
    {
      name: "Divergence",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIVERGENCIA", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Operation")}:`}</div>
            {getAdditionalField("TIPO_OPERACAO", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("PP.Reason")}:`}</div>
            {getAdditionalField("MOTIVO", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("PP.Observation")}:`}</div>
            {getAdditionalField("OBSERVACAO_PP", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Weight.out")}:`}</div>
            {getAdditionalField("PESO_SAIDA", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("DNIT.Weight.limit")}:`}</div>
            {getAdditionalField("LIMITE_DNIT", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "DNIT Weight difference"
            )}:`}</div>
            {getAdditionalField("DIVER_PES_DNIT", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Net.weight")}:`}</div>
            {getAdditionalField("PESO_LIQ", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Weight.DOC.Pallets")}:`}</div>
            {getAdditionalField("PESO_DOC_PALETE", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("NF.Difference")}:`}</div>
            {getAdditionalField("DIVER_PES_NF", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Vehicle.type")}:`}</div>
            {getAdditionalField("TIPO_VEICULO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Divergence")}:`}</div>
            {getAdditionalField("DIVERGENCIA", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("NF.total.tolerance")}:`}</div>
            {getAdditionalField("TOTAL_TOLER", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
};
