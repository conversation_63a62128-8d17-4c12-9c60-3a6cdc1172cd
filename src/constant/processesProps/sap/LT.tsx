import { t } from "i18next";
import React from "react";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

const formatData = (date: string | null) => {
  if (date) {
    const year = date.slice(0, 4);
    const month = date.slice(4, 6);
    const day = date.slice(6);
    return `${day}/${month}/${year}`;
  }
  return "";
};

const getAditional1 = (row: ISap.ItemProps) => {
  const WERKS = row.centro.padStart(4, "0");
  const MATNR = row.material.padStart(18, "0");
  const CHARG = getAdditionalField("LOTE ORIGEM", row.adicionais).padStart(
    10,
    "0"
  );
  const GJAGH = getAdditionalField("EXERCÍCIO", row.adicionais).padStart(
    4,
    "0"
  );
  const APRNR = getAdditionalField(
    "COD MOTIVO APROVAÇÃO",
    row.adicionais
  ).padStart(5, "0");
  return `${WERKS}${MATNR}${CHARG}${GJAGH}${APRNR}`;
};

const getAditional2 = (row: ISap.ItemProps) => {
  return encodeURI(
    getAdditionalField("DESC MOTIVO", row.adicionais).slice(0, 200)
  );
};

export const LT: ReturnProps<ISap.ItemProps> = {
  title: "Batch.Rectification",
  origin: "SAP",
  type: "LT",
  permission: "LT",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps) => {
    return getAditional1(row);
  },
  additional2: (row: ISap.ItemProps) => {
    return getAditional2(row);
  },
  approveItems: true,
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA DE SOLICITAÇÃO", row.adicionais) || "-";
      },
    },
    {
      name: "Reason.code",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("COD MOTIVO ALTERAÇÃO", row.adicionais) || "-"
        );
      },
    },
    {
      name: "Total.items",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL DE ITENS", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Weight",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PESO TOTAL", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA DE SOLICITAÇÃO", row.adicionais) || "-";
      },
    },
    {
      name: "Reason.row",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("COD MOTIVO ALTERAÇÃO", row.adicionais) || "-"
        );
      },
    },
    {
      name: "Total.items",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL DE ITENS", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Weight",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PESO TOTAL", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <React.Fragment
        key={`frag-batch-${rowHeader?.documento}-${rowHeader?.item}`}
      >
        <tr>
          <td>
            <div className="tableTitle">{`${t("Material")}:`}</div>
            {rowHeader?.material || "-"}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Material.description.short.text.")}:`}
            </div>
            {getAdditionalField(
              "DESCRIÇÃO DO MATERIA",
              rowHeader?.adicionais
            ) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Quantity")}:`}</div>
            {getAdditionalField("QUANTIDADE", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Batch.Origin")}:`}</div>
            {getAdditionalField("LOTE ORIGEM", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Due.date")}:`}</div>
            {formatData(
              getAdditionalField(
                "DATA VENC LOTE ORIGE",
                rowHeader?.adicionais || "-"
              )
            )}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Source.track")}:`}</div>
            {getAdditionalField("FAIXA ORIGEM", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination.batch")}:`}</div>
            {getAdditionalField("LOTE DESTINO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "Destination.expiration"
            )}:`}</div>
            {formatData(
              getAdditionalField(
                "DATA VENC LOTE DESTI",
                rowHeader?.adicionais || "-"
              )
            )}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Target.Range")}:`}</div>
            {getAdditionalField("FAIXA DESTINO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date.difference")}:`}</div>
            {getAdditionalField(
              "DIVERGENCIAS DE DIAS",
              rowHeader?.adicionais
            ) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Detailing")}:`}</div>
            {getAdditionalField("DETALHAMENTO", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Reason.code")}:`}</div>
            {getAdditionalField(
              "COD MOTIVO ALTERAÇÃO",
              rowHeader?.adicionais
            ) || "-"}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Reason.description")}:`}</div>
            {getAdditionalField("DESC MOTIVO", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>&nbsp;</td>
        </tr>
        <tr>
          <td
            colSpan={3}
            style={{
              background: "#00000057",
              height: "4px",
              marginBottom: "30px",
              lineHeight: "1px",
              padding: "0",
            }}
          >
            &nbsp;
          </td>
        </tr>
        <tr>
          <td colSpan={3}>&nbsp;</td>
        </tr>
      </React.Fragment>
    );
  },
};
