import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const IF: ReturnProps<ISap.ItemProps> = {
  title: "Inventory.Differences.Record",
  origin: "SAP",
  type: "IF",
  permission: "IF",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return getAdditionalField("EXERCICIO", row.adicionais);
  },
  headerColumns: [
    {
      name: "N.Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Fiscal.Year",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EXERCICIO", row.adicionais) || "-";
      },
    },
    {
      name: "Inventory.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
  ],
  detailColumns: [
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CENTRO", row.adicionais) || "-";
      },
    },
    {
      name: "Item.IF",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Amount",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Unit.Value.IF",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: "Item.Value.IF",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: "Doc.Value.IF",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
};
