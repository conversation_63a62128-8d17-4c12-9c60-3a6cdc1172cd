import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PO: ReturnProps<ISap.ItemProps> = {
  title: "Purchase.Orders",
  origin: "SAP",
  type: "PO",
  permission: "PO",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Empresa", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Create.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data.da.Criação", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Doc.Type",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Provider",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Delegated.Purchasing",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Compras Delegadas", row.adicionais) || "-";
      },
    },
    {
      name: "Approval.Level",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Aprovação CEO", row.adicionais) === "X"
          ? "CEO"
          : "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Amount",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Unit.Value",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: "Item.Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: "Unit.Price",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PRICE_UNIT", row.adicionais) || "-";
      },
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Center", row.adicionais) || "-";
      },
    },
    {
      name: "Accounting.Classification",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro de Custo", row.adicionais)?.length > 0
          ? `${"Cost.Center"}: ${getAdditionalField(
              "Centro de Custo",
              row.adicionais
            )}`
          : "-";
      },
    },
  ],
};
