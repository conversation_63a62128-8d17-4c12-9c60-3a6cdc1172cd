import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const DV: ReturnProps<ISap.ItemProps> = {
  title: "Approval.Divergence.DV",
  origin: "SAP",
  type: "DV",
  permission: "DV",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps, isApproval?: boolean) => {
    return isApproval ? "1" : "2";
  },
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Invoice.DV",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NOTA FISCAL", row.adicionais) || "-";
      },
    },
    {
      name: "Purchase.Order.DV",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PEDIDO", row.adicionais) || "-";
      },
    },
    {
      name: "Cod.Provider.DV",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NOME_FORNECEDOR", row.adicionais) || "-";
      },
    },
    {
      name: "Vl.Tot.Item.NF.DV",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VL_TOT_ITEM_NF", row.adicionais) || "-";
      },
    },
    {
      name: "Justification",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("JUSTIFICATIVA", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Invoice.DV")}:`}</div>
            {getAdditionalField("NOTA FISCAL", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Purchase.Order.DV")}:`}</div>
            {getAdditionalField("PEDIDO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Cod.Provider.DV")}:`}</div>
            {rowHeader?.fornecedor || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Name")}:`}</div>
            {getAdditionalField("NOME_FORNECEDOR", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Vl.Tot.Item.NF.DV")}:`}</div>
            {getAdditionalField("VL_TOT_ITEM_NF", rowHeader?.adicionais) || "-"}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Justification")}:`}</div>
            {getAdditionalField("JUSTIFICATIVA", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(rows) ? rows[0] : rows;

    return (
      <>
        <tbody>
          <tr>
            <td colSpan={7} style={{ color: "#389243" }}>
              <span>{t("Approve.Action.DV")}</span>
              {getAdditionalField("APROVAR_TXT", rowHeader?.adicionais) || "-"}
            </td>
            <td colSpan={5} style={{ color: "#e31f26" }}>
              <span>{t("Disapprove.Action.DV")}</span>
              {getAdditionalField("REPROVAR_TXT", rowHeader?.adicionais) || "-"}
            </td>
          </tr>
          {Array.isArray(rows) &&
            rows.map((item) => (
              <tr
                key={`modal-table-internal-${item.documento}-${item.item}`}
                className="detailModalContent"
              >
                <td>
                  <span>{t("Contract")}</span>
                  {getAdditionalField("NUMERO_CONTRATO", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Item.DV")}</span>
                  {getAdditionalField("ITEM_CONTRATO", item.adicionais) || "-"}
                </td>
                <td>
                  <span>{t("Material.Desc.DV")}</span>
                  {getAdditionalField("DESCRICAO_MATERIAL", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Amount")}</span>
                  {getAdditionalField("QUANTIDADE", item.adicionais) || "-"}
                </td>
                <td>
                  <span>{t("Net.Value.Ped.DV")}</span>
                  {getAdditionalField("VALOR_LIQ_PEDIDO", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Net.Value.Nf.DV")}</span>
                  {getAdditionalField("VALOR_LIQ_NF", item.adicionais) || "-"}
                </td>
                <td>
                  <span>{t("Total.Divergence.Net.DV")}</span>
                  {getAdditionalField("TOTAL_DIVERG_LIQ", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Gross.Order.DV")}</span>
                  {getAdditionalField("VL_BRUTO_PEDIDO", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Gross.Value.Nf.DV")}</span>
                  {getAdditionalField("VL_BRUTO_NF", item.adicionais) || "-"}
                </td>
                <td>
                  <span>{t("Total.Gross.Divergence.DV")}</span>
                  {getAdditionalField("TOT_DIVERG_BRUTO", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Unity.Price.Order.DV")}</span>
                  {getAdditionalField("PRECO_UNIT_PEDIDO", item.adicionais) ||
                    "-"}
                </td>
                <td>
                  <span>{t("Unity.Price.Note.DV")}</span>
                  {getAdditionalField("PRECO_UNIT_NF", item.adicionais) || "-"}
                </td>
              </tr>
            ))}
        </tbody>
      </>
    );
  },
};
