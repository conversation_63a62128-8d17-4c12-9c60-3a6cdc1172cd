import { t } from "i18next";
import React from "react";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const RT: ReturnProps<ISap.ItemProps> = {
  title: "Black.belt",
  origin: "SAP",
  type: "RT",
  permission: "RT",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DT. SOLICITACAO", row.adicionais) || "-";
      },
    },
    {
      name: "Code",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MOTNR", row.adicionais) || "-";
      },
    },
    {
      name: "Request.Reason",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MOTIVO SOLICITACAO", row.adicionais) || "-";
      },
    },
    {
      name: "Justification",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("JUSTIFICATIVA", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Weight",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PESO", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <React.Fragment
        key={`frag-batch-${rowHeader?.documento}-${rowHeader?.material}`}
      >
        <tr>
          <td>
            <div className="tableTitle">{`${t("Material")}:`}</div>
            {rowHeader?.material || "-"}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Material.description.short.text.")}:`}
            </div>
            {getAdditionalField("DESC. material", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Weight")}:`}</div>
            {getAdditionalField("PESO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Source.track")}:`}</div>
            {getAdditionalField("LOTE ORIGEM", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Due.date")}:`}</div>
            {getAdditionalField("VENC. ORIGEM", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Source.track")}:`}</div>
            {getAdditionalField("FAIXA ORIGEM", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination.batch")}`}</div>
            {getAdditionalField("LOTE DESTINO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "Destination.expiration"
            )}:`}</div>
            {getAdditionalField("VENC. DESTINO", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Target.Range")}:`}</div>
            {getAdditionalField("FAIXA DESTINO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date.difference")}`}</div>
            {getAdditionalField("DIAS DE DIFERENCA", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td></td>
        </tr>
        <tr>
          <td colSpan={3}>&nbsp;</td>
        </tr>
        <tr>
          <td
            colSpan={3}
            style={{
              background: "#00000057",
              height: "4px",
              marginBottom: "30px",
              lineHeight: "1px",
              padding: "0",
            }}
          >
            &nbsp;
          </td>
        </tr>
        <tr>
          <td colSpan={3}>&nbsp;</td>
        </tr>
      </React.Fragment>
    );
  },
};
