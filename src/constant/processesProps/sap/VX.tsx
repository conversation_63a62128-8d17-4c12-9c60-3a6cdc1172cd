import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import {
  InTable,
  Intbody,
  Intdleft,
  Inthead,
  Intrbody,
  ModalTableCellTitle,
} from "../styles";

export const VX: ReturnProps<ISap.ItemProps> = {
  title: "Status.Flow.Vistex",
  origin: "SAP",
  type: "VX",
  permission: "VX",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps) => {
    return getAdditionalField("ACNUM", row.adicionais);
  },
  headerColumns: [
    {
      name: "Aggrement.Order",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Client.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KUNNR", row.adicionais) || "-";
      },
    },
    {
      name: "Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("data_emissao", row.adicionais) || "-";
      },
    },
    {
      name: "Solicitation.type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBJTYP", row.adicionais) || "-";
      },
    },
    {
      name: "Aggrement.type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("REQTYPE", row.adicionais) || "-";
      },
    },
  ],
  hasDetailRoute: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    const rowDetail = Array.isArray(row) ? (row[1] as ISap.ItemProps) : row;

    const vrTpLayout = getAdditionalField("TPLAYOUT", rowHeader?.adicionais);

    const part1 = (vrTpLayout === "APPS" || vrTpLayout === "APBS") && (
      <tr>
        <td>
          <div className="tableTitle">{`${t("Document.calculation")}`}</div>
          {getAdditionalField("CALC_RUN", rowDetail?.adicionais)}
        </td>
      </tr>
    );

    const part2 = (vrTpLayout === "AGRP" || vrTpLayout === "AGRB") && (
      <tr>
        <td>
          <div className="tableTitle">{`${t("Seller")}:`}</div>
          {getAdditionalField("SALESMAN", rowDetail?.adicionais)}
        </td>
        <td>
          <div className="tableTitle">{`${t("Supplier")}:`}</div>
          {getAdditionalField("VENDOR", rowDetail?.adicionais)}
        </td>
      </tr>
    );

    const part3 = (vrTpLayout === "AGRP" ||
      vrTpLayout === "AGRB" ||
      vrTpLayout === "AGRSP" ||
      vrTpLayout === "AGRSA") && (
      <tr>
        <td>
          <div className="tableTitle">{`${t("Contract.validity")}:`}</div>
          {`${getAdditionalField(
            "BEGDATE",
            rowDetail?.adicionais
          )} - ${getAdditionalField("ENDDATE", rowDetail?.adicionais)}`}
        </td>
      </tr>
    );

    const drivers =
      getAdditionalField("DRIVERS", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("DRIVERS", rowDetail?.adicionais).split("<br>");

    const driversDetails =
      drivers &&
      drivers.map((item: any) => {
        return item.split("|");
      });

    const driversTable2 = driversDetails && driversDetails[0].length === 2 && (
      <InTable>
        <Inthead>
          <tr>
            <th>{t("Driver")}</th>
            <th>{t("Driver.description")}</th>
          </tr>
        </Inthead>
        <Intbody>
          {driversDetails &&
            driversDetails.map((rows, index) => (
              <Intrbody key={index}>
                <Intdleft>{rows[0]}</Intdleft>
                <Intdleft>{rows[1]}</Intdleft>
              </Intrbody>
            ))}
        </Intbody>
      </InTable>
    );

    const driversTable3 = driversDetails && driversDetails[0].length === 3 && (
      <InTable>
        <Inthead>
          <tr>
            <th>{t("Driver")}</th>
            <th>{t("Driver.description")}</th>
            <th>{t("Central.tax")}</th>
          </tr>
        </Inthead>
        <Intbody>
          {driversDetails &&
            driversDetails.map((rows, index) => (
              <Intrbody key={index}>
                <Intdleft>{rows[0]}</Intdleft>
                <Intdleft>{rows[1]}</Intdleft>
                <Intdleft>{rows[2]}</Intdleft>
              </Intrbody>
            ))}
        </Intbody>
      </InTable>
    );

    const part4 = (vrTpLayout === "AGRP" ||
      vrTpLayout === "APPS" ||
      vrTpLayout === "AGRB" ||
      vrTpLayout === "APBS" ||
      vrTpLayout === "APSS") &&
      getAdditionalField("DRIVERS", rowDetail?.adicionais).length > 0 && (
        <>
          <tr>
            {driversTable2 && (
              <ModalTableCellTitle colSpan={2}>
                <h4>{`${t("Drivers")}:`}</h4>
                {driversTable2}
              </ModalTableCellTitle>
            )}
            {driversTable3 && (
              <ModalTableCellTitle colSpan={2}>
                <h4>{`${t("Driver")}:`}</h4>
                {driversTable3}
              </ModalTableCellTitle>
            )}
          </tr>
        </>
      );

    // Inicio da parte 5
    // materiais excluidos INIT
    const materialsexcluded =
      getAdditionalField("MAT_EXC", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("MAT_EXC", rowDetail?.adicionais).split("<br>");

    const materialsExcludedDetails =
      materialsexcluded &&
      materialsexcluded.map((item: any) => {
        return item.split("|");
      });

    const excludedMaterials = materialsExcludedDetails &&
      materialsExcludedDetails[0].length > 0 && (
        <InTable>
          <Inthead>
            <tr>
              <th>{t("Code")}</th>
              <th>{t("Material")}</th>
            </tr>
          </Inthead>
          <Intbody>
            {materialsExcludedDetails &&
              materialsExcludedDetails.map((rows, index) => (
                <Intrbody key={index}>
                  <Intdleft>{rows[0]}</Intdleft>
                  <Intdleft>{rows[1]}</Intdleft>
                </Intrbody>
              ))}
          </Intbody>
        </InTable>
      );
    // materiais excluidos FIM

    // materiais Elegíveis INIT
    const materialsElegible =
      getAdditionalField("MAT_ELIG", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("MAT_ELIG", rowDetail?.adicionais).split("<br>");

    const materialsElegibleDetails =
      materialsElegible &&
      materialsElegible.map((item: any) => {
        return item.split("|");
      });

    const elegibleMaterials = materialsElegibleDetails &&
      materialsElegibleDetails[0].length > 0 && (
        <InTable>
          <Inthead>
            <tr>
              <th>{t("Code")}</th>
              <th>{t("Material")}</th>
            </tr>
          </Inthead>
          <Intbody>
            {materialsElegibleDetails &&
              materialsElegibleDetails.map((rows, index) => (
                <Intrbody key={index}>
                  <Intdleft>{rows[0]}</Intdleft>
                  <Intdleft>{rows[1]}</Intdleft>
                </Intrbody>
              ))}
          </Intbody>
        </InTable>
      );
    // materiais elegíveis FIM

    // Brand INIT
    const tagBrand =
      getAdditionalField("BRAND", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("BRAND", rowDetail?.adicionais).split("<br>");

    const tagBrandDetails =
      tagBrand &&
      tagBrand.map((item: any) => {
        return item.split("|");
      });

    const brand = tagBrandDetails && tagBrandDetails[0].length > 0 && (
      <InTable>
        <Inthead>
          <tr>
            <th>{t("Code")}</th>
            <th>{t("Description")}</th>
          </tr>
        </Inthead>
        <Intbody>
          {tagBrandDetails &&
            tagBrandDetails.map((rows, index) => (
              <Intrbody key={index}>
                <Intdleft>{rows[0]}</Intdleft>
                <Intdleft>{rows[1]}</Intdleft>
              </Intrbody>
            ))}
        </Intbody>
      </InTable>
    );
    // Brand FIM

    // Products Hierarchy INIT
    const productsH =
      getAdditionalField("PROD_HIER", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("PROD_HIER", rowDetail?.adicionais).split("|");

    const productsHierarchy = productsH && productsH[0].length > 0 && (
      <InTable>
        <Intbody>
          {productsH &&
            productsH.map((rows, index) => (
              <Intrbody key={index}>
                <Intdleft>{`${rows[0]}   `}</Intdleft>
              </Intrbody>
            ))}
        </Intbody>
      </InTable>
    );
    // Products Hierarchy  FIM

    // Client qualification INIT
    const clientQ =
      getAdditionalField("CUST_ELIG", rowDetail?.adicionais).length > 0 &&
      getAdditionalField("CUST_ELIG", rowDetail?.adicionais).split("<br>");

    const clientQualificationDetails =
      clientQ &&
      clientQ.map((item: any) => {
        return item.split(" - ");
      });

    const clientQualification = clientQualificationDetails &&
      clientQualificationDetails[0].length > 0 && (
        <InTable>
          <Inthead>
            <tr>
              <th>{t("Code")}</th>
              <th>{t("Description")}</th>
            </tr>
          </Inthead>
          <Intbody>
            {clientQualificationDetails &&
              clientQualificationDetails.map((rows, index) => (
                <Intbody key={index}>
                  <Intdleft>{rows[0]}</Intdleft>
                  <Intdleft>{rows[1]}</Intdleft>
                </Intbody>
              ))}
          </Intbody>
        </InTable>
      );
    // Client qualification FIM

    const part5 = (vrTpLayout === "AGRP" || vrTpLayout === "AGRB") && (
      <>
        <tr>
          <td>
            <h4>{`${t("Cost.Center")}:`}</h4>
          </td>
        </tr>
        <tr>
          {getAdditionalField("COST_CENT", rowDetail?.adicionais) && (
            <td>
              <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
              {getAdditionalField("COST_CENT", rowDetail?.adicionais)}
            </td>
          )}
          {getAdditionalField("MAT_ELIG_ALL", rowDetail?.adicionais) && (
            <td>
              <div className="tableTitle">
                {`${t("All.material.eligibility")}`}
              </div>
              {getAdditionalField("MAT_ELIG_ALL", rowDetail?.adicionais)}
            </td>
          )}
        </tr>
        <tr>
          <td>
            <h4>{`${t("Excluded.materials")}:`}</h4>
          </td>
        </tr>
        <tr>{excludedMaterials}</tr>
        <tr>
          <td>
            <h4>{`${t("Elegible.materials")}:`}</h4>
          </td>
        </tr>
        <tr>{elegibleMaterials}</tr>
        <tr>
          <td>
            <h4>{`${t("Brand")}:`}</h4>
          </td>
        </tr>
        <tr>{brand}</tr>
        <tr>
          <td>
            <h4>{`${t("Products.Hierarchy")}:`}</h4>
          </td>
        </tr>
        <tr>{productsHierarchy}</tr>
        <tr>
          <td>
            <h4>{`${t("Client.Qualification")}:`}</h4>
          </td>
        </tr>
        <tr>{clientQualification}</tr>
      </>
    );

    const part6 = vrTpLayout === "AGRP" && (
      <>
        <tr>
          <td>
            <h4>{`${t("Remarks")}:`}</h4>
          </td>
        </tr>
        {getAdditionalField("REMARKS", rowDetail?.adicionais) && (
          <tr>
            <td>
              <div className="tableTitle">{`${t("Remarks")}:`}</div>
              {getAdditionalField("REMARKS", rowDetail?.adicionais)}
            </td>
          </tr>
        )}
      </>
    );

    const part7 = vrTpLayout === "AGRB" && (
      <>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Contract.description")}:`}</div>
            {getAdditionalField("AGR_DESC", rowDetail?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Renovation.type")}:`}</div>
            {getAdditionalField("RENEWAL_TYP", rowDetail?.adicionais)}
          </td>
        </tr>
      </>
    );

    const part8 = (vrTpLayout === "AGRSA" || vrTpLayout === "AGRSP") && (
      <>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Contract.description")}:`}</div>
            {getAdditionalField("AGR_DESC", rowDetail?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Renovation.type")}:`}</div>
            {getAdditionalField("RENEWAL_TYP", rowDetail?.adicionais)}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Local.currency")}`}</div>
            {getAdditionalField("LOC_CUR", rowDetail?.adicionais)}
          </td>
        </tr>
      </>
    );

    const part9 = (vrTpLayout === "APPS" ||
      vrTpLayout === "APBS" ||
      vrTpLayout === "APSS") && (
      <>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Provisionated.value")}:`}</div>
            {getAdditionalField("PROV_AMT", rowDetail?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Settled.amount")}`}</div>
            {getAdditionalField("SETTL_AMT", rowDetail?.adicionais)}
          </td>
        </tr>
      </>
    );

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">
              {`${t("Aggrement.Solicitation")}:`}
            </div>
            {rowDetail?.documento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Client")}:`}</div>
            {getAdditionalField("CUSTOMER", rowDetail?.adicionais)}
          </td>
        </tr>
        <>oi</>
        {part1}
        {part2}
        {part3}
        {part4}
        {part5}
        {part6}
        {part7}
        {part8}
        {part9}
      </tbody>
    );
  },
};
