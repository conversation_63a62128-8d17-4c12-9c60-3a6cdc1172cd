import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const OT: ReturnProps<ISap.ItemProps> = {
  title: "Payment.Order.Approval",
  origin: "SAP",
  type: "OT",
  permission: "OT",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Project.Code",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ORDEM", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA CRIACAO", row.adicionais) || "-";
      },
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CENTRO DE CUSTO", row.adicionais) || "-";
      },
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("material", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VALOR", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NOME RESPONSAVEL", row.adicionais) || "-";
      },
    },
  ],
};
