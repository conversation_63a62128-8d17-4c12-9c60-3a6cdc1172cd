import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const XP: ReturnProps<ISap.ItemProps> = {
  title: "Special.Price.Export.Sale",
  origin: "SAP",
  type: "XP",
  permission: "XP",
  hasDetailModal: false,
  approveItems: true,
  headerColumns: [
    {
      name: "SAP.Sales.Order.Number.XP",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Order.Date.XP",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Sales.Region.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("BZIRK", row.adicionais) || "-";
      },
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Trader.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "Reason",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MOTV", row.adicionais) || "-";
      },
    },
    {
      name: "Remarks.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBS", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Incoterm.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("INCO1", row.adicionais) || "-";
      },
    },
    {
      name: "Currency",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUR", row.adicionais) || "-";
      },
    },
    {
      name: "Volume",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Sales.UoM.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VRKME", row.adicionais) || "-";
      },
    },
    {
      name: "Price.Reference.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PRREF", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Price.XP",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: ".Price.Difference",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PDESC", row.adicionais) || "-";
      },
    },
    {
      name: "Discount.Value.XP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESCT", row.adicionais) || "-";
      },
    },
  ],
};
