import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const ID: ReturnProps<ISap.ItemProps> = {
  title: "Difference.Final.Stock.Inventory",
  origin: "SAP",
  type: "ID",
  permission: "ID",
  hasDetailModal: true,
  hasDetailRoute: true,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Year",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("EXERCICIO", row.adicionais) || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("TIPO_INVENTARIO", row.adicionais) || "-",
    },
    {
      name: "Plant",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Storage",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DEPOSITO", row.adicionais) || "-",
    },
    {
      name: "Currency",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("MOEDA", row.adicionais) || "-",
    },
    {
      name: "Surplus",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("SOBRA", row.adicionais) || "-",
    },
    {
      name: "Lack",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("FALTA", row.adicionais) || "-",
    },
    {
      name: "Absolute",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("ABSOLUTO", row.adicionais) || "-",
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    return (
      <tbody>
        {Array.isArray(row) &&
          row?.map((detail: ISap.ItemProps, index: number) => (
            <tr key={`detail-${index}-${detail.item || index}`}>
              <td>
                <div className="tableTitle">{`${t("Item")}:`}</div>
                {detail.item || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Plant")}:`}</div>
                {getAdditionalField("DESC_WERKS", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Deposit")}:`}</div>
                {getAdditionalField("DESC_LGORT", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Classification")}:`}</div>
                {getAdditionalField("CLASSIFICACAO", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Surplus")}:`}</div>
                {getAdditionalField("SOBRA", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Lack")}:`}</div>
                {getAdditionalField("FALTA", detail.adicionais) || "-"}
              </td>
              <td>
                <div className="tableTitle">{`${t("Absolute")}:`}</div>
                {getAdditionalField("ABSOLUTO", detail.adicionais) || "-"}
              </td>
            </tr>
          ))}
      </tbody>
    );
  },
};
