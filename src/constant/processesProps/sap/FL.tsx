import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const FL: ReturnProps<ISap.ItemProps> = {
  title: "Final.Payroll.Approval.Process",
  origin: "SAP",
  type: "FL",
  permission: "FL",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Project.Code",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Country",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PAIS", row.adicionais) || "-";
      },
    },
    {
      name: "Execution.date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA DE EXECUCAO", row.adicionais) || "-";
      },
    },
    {
      name: "Identification",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("IDENTIFICACAO", row.adicionais) || "-";
      },
    },
    {
      name: "File.name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NOME DO ARQUIVO", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VALOR", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SOLICITANTE", row.adicionais) || "-";
      },
    },
  ],
};
