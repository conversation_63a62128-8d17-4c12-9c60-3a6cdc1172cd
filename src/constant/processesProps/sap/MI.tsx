import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const MI: ReturnProps<ISap.ItemProps> = {
  title: "Fixed.Asset.Transactions",
  origin: "SAP",
  type: "MI",
  permission: "MI",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Document.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Empresa", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Requisitante", row.adicionais) || "-";
      },
    },
    {
      name: "Description.of.Movement",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("Descrição Imobilizad", row.adicionais) || "-"
        );
      },
    },
    {
      name: "Accounting.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Status",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Status", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number.MI")}:`}</div>
            {rowHeader?.documento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Status")}:`}</div>
            {getAdditionalField("Status", rowHeader?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {getAdditionalField("Empresa", rowHeader?.adicionais)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Accounting.Value.MI")}:`}</div>
            {rowHeader?.vldoc}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t(
              "Description.Movement.MI"
            )}:`}</div>
            {getAdditionalField("Desc. Movimento", rowHeader?.adicionais)}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    return (
      <tbody>
        {Array.isArray(rows) &&
          rows.map((row) => (
            <tr
              key={`modal-table-internal-${row.documento}-${row.item}`}
              className="detailModalContent"
            >
              <td>
                <span>{t("Item")}</span>
                {row.item}
              </td>
              <td>
                <span>{t("Fixed.Asset.MI")}</span>
                {`${getAdditionalField(
                  "Número Imobilizado",
                  row.adicionais
                )} - ${getAdditionalField(
                  "Descrição Imobilizad",
                  row.adicionais
                )}`}
              </td>
              <td>
                <span>{t("Origin.Branch")}</span>
                {`${getAdditionalField(
                  "Filial Origem",
                  row.adicionais
                )} - ${getAdditionalField(
                  "Desc. Filial Origem",
                  row.adicionais
                )}`}
              </td>
              <td>
                <span>{t("Origin.Cost.Center")}</span>
                {`${getAdditionalField(
                  "Centro Custo Origem",
                  row.adicionais
                )} - ${getAdditionalField(
                  "Desc. Centro Custo O",
                  row.adicionais
                )}`}
              </td>
              <td>
                <span>{t("Destination.Branch")}</span>
                {`${getAdditionalField(
                  "Filial Destino",
                  row.adicionais
                )} - ${getAdditionalField(
                  "Desc.Filial.Destino",
                  row.adicionais
                )}`}
              </td>
              <td>
                <span>{t("Destination.Cost.Center")}</span>
                {`${getAdditionalField(
                  "Centro Custo Destino",
                  row.adicionais
                )} - ${getAdditionalField(
                  "Desc. Centro Custo D",
                  row.adicionais
                )}`}
              </td>
              <td>
                <span>{t("Item.text.")}</span>
                {getAdditionalField("Texto Solicitação", row.adicionais)}
              </td>
            </tr>
          ))}
      </tbody>
    );
  },
};
