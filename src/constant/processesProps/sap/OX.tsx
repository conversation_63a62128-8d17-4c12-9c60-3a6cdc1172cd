import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const OX: ReturnProps<ISap.ItemProps> = {
  title: "Ticket.SOX",
  origin: "SAP",
  type: "OX",
  permission: "OX",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return `${getAdditionalField("WERKS", row.adicionais)}_${getAdditionalField(
      "TP_TICKET",
      row.adicionais
    )}_${getAdditionalField(
      "IDENTIF_PROCESS",
      row.adicionais
    )}_${getAdditionalField("DATA_ENTRADA", row.adicionais)}`;
  },
  additional2: (row: ISap.ItemProps) => {
    //  "jutificativa modal reprovar"
    return "";
  },
  headerColumns: [
    {
      name: "SOX.Process",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("IDENTIF_PROCESS_DESC", row.adicionais) || "-"
        );
      },
    },
    {
      name: "SOX.EntryDate",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_ENTRADA", row.adicionais) || "-";
      },
    },
    {
      name: "SOX.Ticket",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TICKET", row.adicionais) || "-";
      },
    },
    {
      name: "SOX.TicketType",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TP_TICKET", row.adicionais) || "-";
      },
    },
    {
      name: "SOX.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("WERKS", row.adicionais) || "-";
      },
    },
    {
      name: "SOX.Opinion",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PARECER", row.adicionais) || "-";
      },
    },
    {
      name: "SOX.Time",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("FORMAT_CONTAGEM_SOX", row.adicionais) || "-";
      },
    },
  ],
};
