import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const AB: ReturnProps<ISap.ItemProps> = {
  title: "Component.Approval",
  origin: "SAP",
  type: "AB",
  permission: "AB",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "AB.Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
  ],
  detailColumns: [
    {
      name: "Summary",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TXT", row.adicionais) || "-";
      },
    },
  ],
};
