import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PA: ReturnProps<ISap.ItemProps> = {
  title: "Egg.Supply.Request",
  origin: "SAP",
  type: "PA",
  permission: "PA",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA SOLICITAÇÃO", row.adicionais) || "-";
      },
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CENTRO", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("requisitante", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Batch",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NÚMERO LOTE", row.adicionais) || "-";
      },
    },
    {
      name: "Matriz.Age",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("IDADE MATRIZ", row.adicionais) || "-";
      },
    },
    {
      name: "Order",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ORDEM POSTURA", row.adicionais) || "-";
      },
    },
    {
      name: "Qtd.Production",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("QUANTIDADE PREVISTA", row.adicionais) || "-";
      },
    },
    {
      name: "Release.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA CARGA", row.adicionais) || "-";
      },
    },
    {
      name: "Slaughter.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA ABATE", row.adicionais) || "-";
      },
    },
    {
      name: "Perc.Female.Mortality",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("PERCENTUAL MORTE FÊM", row.adicionais) || "-"
        );
      },
    },
    {
      name: "Perc.Male.Mortality",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("PERCENTUAL MORTE MAC", row.adicionais) || "-"
        );
      },
    },
  ],
};
