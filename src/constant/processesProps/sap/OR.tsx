import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const OR: ReturnProps<ISap.ItemProps> = {
  title: "Investment.Order",
  origin: "SAP",
  type: "OR",
  permission: "OR",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return getAdditionalField("BUZEI", row.adicionais);
  },
  headerColumns: [
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("BUKRS", row.adicionais) || "-";
      },
    },
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NRSOL", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("AUFNR", row.adicionais) || "-";
      },
    },
    {
      name: "Imobilizeted",
      selector: (row: ISap.ItemProps) => {
        const anln1 = getAdditionalField("ANLN1", row.adicionais) || "-";
        const anln2 = getAdditionalField("ANLN2", row.adicionais) || "-";
        return `${anln1} ${anln2}`;
      },
    },
    {
      name: "Sub.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ANLN2", row.adicionais) || "-";
      },
    },
    {
      name: "Amount.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("WRTBR", row.adicionais) || "-";
      },
    },
    {
      name: "FA.Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("USNAM_SOLI", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Fixed.asset.description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TXT50", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Center.Cost",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("WERKS", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Division",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("GSBER", row.adicionais) || "-";
      },
    },
    {
      name: "Order.Imobilizeted.Cost",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KOSTL", row.adicionais) || "-";
      },
    },
    {
      name: "Cost.Center.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("LTEXT", row.adicionais) || "-";
      },
    },
  ],
};
