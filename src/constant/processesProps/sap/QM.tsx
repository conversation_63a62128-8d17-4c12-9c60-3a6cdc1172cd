import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const QM: ReturnProps<ISap.ItemProps> = {
  title: "Mesh.Breakage",
  origin: "SAP",
  type: "QM",
  permission: "QM",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return "097";
  },
  headerColumns: [
    {
      name: "Qm.Number.Solicitacion",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Qm.Center.Mesh",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Qm.Center.Breakage",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro Quebra", row.adicionais) || "-";
      },
    },
    {
      name: "Difference",
      selector: (row: ISap.ItemProps) => `R$ ${row.vldoc || "-"}`,
    },
    {
      name: "Center.Destiny",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro Destino", row.adicionais) || "-";
      },
    },
    {
      name: "Qm.Create.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
  ],
  detailColumns: [
    {
      name: "Qm.Sales.Order",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nº da Ordem", row.adicionais) || "-";
      },
    },
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.item || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Volume",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Qm.Mesh.Cost",
      selector: (row: ISap.ItemProps) => {
        return `R$ ${getAdditionalField("Custo Malha", row.adicionais) || "-"}`;
      },
    },
    {
      name: "Qm.Breakage.Cost",
      selector: (row: ISap.ItemProps) => {
        return `R$ ${
          getAdditionalField("Custo Quebra", row.adicionais) || "-"
        }`;
      },
    },
    {
      name: "Qm.Breakage.Reason",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Motivo Quebra", row.adicionais) || "-";
      },
    },
  ],
};
