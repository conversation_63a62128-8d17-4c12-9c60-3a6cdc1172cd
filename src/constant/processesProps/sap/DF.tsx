import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { DivLine, DivTitle, DivValue } from "../styles";

export const DF: ReturnProps<ISap.ItemProps> = {
  title: "Release.Shipping.Conditions",
  origin: "SAP",
  type: "DF",
  permission: "DF",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Emission.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <DivLine>
          <div>
            <div>
              <DivTitle>{t("Application")}</DivTitle>
              <DivValue>
                {getAdditionalField("Aplicação", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Entr.AS.VAREJO")}</DivTitle>
              <DivValue>
                {getAdditionalField("Entr. AS/VAREJO", row?.adicionais)}
              </DivValue>
            </div>
          </div>
          <div>
            <div>
              <DivTitle>{t("Forn.Services")}</DivTitle>
              <DivValue>
                {getAdditionalField("Forn.serviços", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Hier.Prd.1")}</DivTitle>
              <DivValue>
                {getAdditionalField("Hier.prd.1", row?.adicionais)}
              </DivValue>
            </div>
          </div>
        </DivLine>
        <DivLine>
          <div>
            <div>
              <DivTitle>{t("Hier.Prd.2")}</DivTitle>
              <DivValue>
                {getAdditionalField("Hier.prd.2", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Hier.Prd.3")}</DivTitle>
              <DivValue>
                {getAdditionalField("Hier.prd.3", row?.adicionais)}
              </DivValue>
            </div>
          </div>
          <div>
            <div>
              <DivTitle>{t("Start.Interv")}</DivTitle>
              <DivValue>
                {getAdditionalField("Início interv.", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Shipping.Location")}</DivTitle>
              <DivValue>
                {getAdditionalField("Local expedição", row?.adicionais)}
              </DivValue>
            </div>
          </div>
        </DivLine>
        <DivLine>
          <div>
            <div>
              <DivTitle>{t("Local.Org.Trsp")}</DivTitle>
              <DivValue>
                {getAdditionalField("Local org.trsp.", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Currency.Scale")}</DivTitle>
              <DivValue>
                {getAdditionalField("Moeda escala", row?.adicionais)}
              </DivValue>
            </div>
          </div>
          <div>
            <div>
              <DivTitle>{t("Amount.Value")}</DivTitle>
              <DivValue>
                {getAdditionalField("Montante", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("St.Release")}</DivTitle>
              <DivValue>
                {getAdditionalField("St.liberação", row?.adicionais)}
              </DivValue>
            </div>
          </div>
        </DivLine>
        <DivLine>
          <div>
            <div>
              <DivTitle>{t("Status.Proces")}</DivTitle>
              <DivValue>
                {getAdditionalField("StatusProces", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Condition.Status")}</DivTitle>
              <DivValue>
                {getAdditionalField("Tipo condição", row?.adicionais)}
              </DivValue>
            </div>
          </div>
          <div>
            <div>
              <DivTitle>{t("Transport.Type")}</DivTitle>
              <DivValue>
                {getAdditionalField("Tipo transporte", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("One.Of.The.Condition")}</DivTitle>
              <DivValue>
                {getAdditionalField("UM da condição", row?.adicionais)}
              </DivValue>
            </div>
          </div>
        </DivLine>
        <DivLine>
          <div>
            <div>
              <DivTitle>{t("Unit.Price")}</DivTitle>
              <DivValue>
                {getAdditionalField("Unidade preço", row?.adicionais)}
              </DivValue>
            </div>
            <div>
              <DivTitle>{t("Valid.Until")}</DivTitle>
              <DivValue>
                {getAdditionalField("Válido até", row?.adicionais)}
              </DivValue>
            </div>
          </div>
          <div>
            <div>
              <DivTitle>{t("Valid.Since")}</DivTitle>
              <DivValue>
                {getAdditionalField("Válido desde", row?.adicionais)}
              </DivValue>
            </div>
          </div>
        </DivLine>
      </>
    );
  },
};
