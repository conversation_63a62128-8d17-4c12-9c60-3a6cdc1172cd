import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import {
  Intbody,
  Intdcenter,
  Intdleft,
  Inthead,
  Intrbody,
  Inttitle,
  TableInternal,
} from "../styles";

export const FF: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Sales.Orders.Rule.51",
  origin: "SAP",
  type: "FF",
  permission: "FF",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },
  headerColumns: [
    {
      name: "Sales.Orders",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Rule",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Lock.description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_REGRA", row.adicionais) || "-";
      },
    },
    {
      name: "Lock.date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_BLOQUEIO", row.adicionais) || "-";
      },
    },
    {
      name: "Sell.Office",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKBUR", row.adicionais) || "-";
      },
    },
    {
      name: "Client.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "Total.FIFO.Amount.Approved",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL_VENDA_FIFO", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NETWR", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{`${t("HDD.request")}: ${
                Array.isArray(rows) ? rows.length : 1
              }`}</td>
              <td>{t("Order.motivation")}</td>
              {row.tipo === "001" && <td>{t("Credit Limit")}</td>}
              {row.tipo === "001" && <td>{t("Total.Compromisse")}</td>}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-head={t("HDD.request")}>
                {getAdditionalField("HDDREQ", row.adicionais)}
              </td>
              <td data-head={t("Order.motivation")}>
                {getAdditionalField("AUGRU", row.adicionais)}
              </td>
              {row.tipo === "001" && (
                <td>{getAdditionalField("KLIMK", row.adicionais)}</td>
              )}
              {row.tipo === "001" && (
                <td>{getAdditionalField("SAUFT", row.adicionais)}</td>
              )}
            </tr>
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Item")}</td>
              <td>{t("Material.Description.OB")}</td>
              <td>{t("Quantity")}</td>
              <td>{t("Seller.Unity")}</td>
              <td>{t("Item Category")}</td>
              <td>{t("Sugest.Price")}</td>
              <td>{t("Inserted.Price")}</td>
              <td>{t("Total.Item.Value")}</td>
              <td>{t(".Price.Difference")}</td>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(rows) &&
              rows.map((item: ISap.ItemProps) => {
                return (
                  <tr>
                    <td data-head={t("Item")}>{item.item}</td>
                    <td data-head={t("Material.Description.OB")}>
                      {item.material}
                    </td>
                    <td data-head={t("Quantity")}>{item.qtd}</td>
                    <td data-head={t("Seller.Unity")}>
                      {getAdditionalField("VRKME", item.adicionais)}
                    </td>
                    <td data-head={t("Item Category")}>
                      {getAdditionalField("PSTYV", item.adicionais)}
                    </td>
                    <td data-head={t("Sugest.Price")}>
                      {getAdditionalField("KBETR", item.adicionais)}
                    </td>
                    <td data-head={t("Inserted.Price")}>{item.vlunit}</td>
                    <td data-head={t("Total.Item.Value")}>{item.vlitem}</td>
                    <td data-head={t(".Price.Difference")}>
                      {getAdditionalField("PRICECHG", item.adicionais)}
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </TableInternal>
      </>
    );
  },
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Lock.date")}:`}</div>
            {getAdditionalField("DATA_BLOQUEIO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Sell.Office")}:`}</div>
            {getAdditionalField("VKBUR", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Client.Name")}:`}</div>
            {getAdditionalField("CUSTNAME", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Seller")}:`}</div>
            {getAdditionalField("SALESPERSON", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Total.Value")}:`}</div>
            {getAdditionalField("NETWR", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <Inttitle>{`${t("Sales.Orders")}:`}</Inttitle>
            <TableInternal>
              <Inthead>
                <tr>
                  <th>{`${t("Item")}`}</th>
                  <th>{`${t("Material.Description.OB")}`}</th>
                  <th>{`${t("Quantity")}`}</th>
                  <th>{`${t("Seller.Unity")}`}</th>
                  <th>{`${t("Sugest.Price")}`}</th>
                  <th>{`${t("Inserted.Price")}`}</th>
                  <th>{`${t("Total.Item.Value")}`}</th>
                  <th>{`${t(".Price.Difference")}`}</th>
                </tr>
              </Inthead>
              <Intbody>
                <Intrbody>
                  <Intdleft data-head={t("Item")} className="noCheckbox">
                    {rowHeader?.item || "-"}
                  </Intdleft>
                  <Intdleft data-head={t("Material.Description.OB")}>
                    {rowHeader?.material || "-"}
                  </Intdleft>
                  <Intdleft data-head={t("Quantity")}>
                    {rowHeader?.qtd || "-"}
                  </Intdleft>
                  <Intdcenter data-head={t("Seller.Unity")}>
                    {getAdditionalField("VRKME", rowHeader?.adicionais) || "-"}
                  </Intdcenter>
                  <Intdcenter data-head={t("Sugest.Price")}>
                    {getAdditionalField("KBETR", rowHeader?.adicionais) || "-"}
                  </Intdcenter>
                  <Intdcenter data-head={t("Inserted.Price")}>
                    {rowHeader?.vlunit || "-"}
                  </Intdcenter>
                  <Intdcenter data-head={t("Total.Item.Value")}>
                    {rowHeader?.vlitem || "-"}
                  </Intdcenter>
                  <Intdcenter data-head={t(".Price.Difference")}>
                    {getAdditionalField("PRICECHG", rowHeader?.adicionais) ||
                      "-"}
                  </Intdcenter>
                </Intrbody>
              </Intbody>
            </TableInternal>
          </td>
        </tr>
      </tbody>
    );
  },
};
