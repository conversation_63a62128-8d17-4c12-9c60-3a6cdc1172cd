import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const EA: ReturnProps<ISap.ItemProps> = {
  title: "Anticipation.of.Delivery.Billing",
  origin: "SAP",
  type: "EA",
  permission: "EA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "No.Group.EA",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Customer.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Branch.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("FILIA", row.adicionais) || "-";
      },
    },
    {
      name: "Original.Billing.Date.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("AGENDA_ORIGINAL", row.adicionais) || "-";
      },
    },
    {
      name: "Anticipated.Billing.Date.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_ANTECIPACAO", row.adicionais) || "-";
      },
    },
    {
      name: "Anticipation.Days.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DIAS_ANTECIPACAO", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <TableInternal>
        <thead>
          <tr>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Material")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Net.Weight.EA")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Total.Amount.EA")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Capacity.Day.EA")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Capacity.Used.EA")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Available.Capacity.EA")}
            </td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td data-head={t("Material")} style={{ paddingTop: "0px" }}>
              {row?.material || "-"}
            </td>
            <td data-head={t("Net.Weight.EA")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("PESO_LIQUIDO", row?.adicionais) || "-"}
            </td>
            <td data-head={t("Total.Amount.EA")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("VALOR_TOTAL", row?.adicionais) || "-"}
            </td>

            <td data-head={t("Capacity.Day.EA")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("CAP_DIA", row?.adicionais) || "-"}
            </td>
            <td data-head={t("Capacity.Used.EA")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("CAP_UTILIZADA", row?.adicionais) || "-"}
            </td>
            <td
              data-head={t("Available.Capacity.EA")}
              style={{ paddingTop: "0px" }}
            >
              {getAdditionalField("CAP_DISP", row?.adicionais) || "-"}
            </td>
          </tr>
        </tbody>

        <thead>
          <tr>
            <td></td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Category")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Conservation")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Protocol.Password.EA")}
            </td>
            <td colSpan={2} style={{ paddingBottom: "0px" }}>
              {t("Observation")}
            </td>
          </tr>
        </thead>
        <tbody>
          <tr key={`fragintern-${row?.documento}`}>
            <td></td>
            <td data-head={t("Category")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("CATEGORIA", row?.adicionais) || "-"}
            </td>
            <td data-head={t("Conservation")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("CONSERVACAO", row?.adicionais) || "-"}
            </td>
            <td
              data-head={t("Protocol.Password.EA")}
              style={{ paddingTop: "0px" }}
            >
              {getAdditionalField("PROTOCOLO", row?.adicionais) || "-"}
            </td>
            <td
              colSpan={2}
              data-head={t("Observation")}
              style={{ paddingTop: "0px" }}
            >
              {getAdditionalField("OBSERVACAO", row?.adicionais) || "-"}
            </td>
          </tr>
        </tbody>
      </TableInternal>
    );
  },
};
