import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { toPtBrCurrency } from "../../../utils/Currency";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const KM: ReturnProps<ISap.ItemProps> = {
  title: "Dispute.KM.Particular",
  origin: "SAP",
  type: "KM",
  permission: "KM",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },
  headerColumns: [
    {
      name: "Driver",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Funcionário", row.adicionais) || "-";
      },
    },
    {
      name: "Disputed.KM",
      selector: (row: ISap.ItemProps) => {
        return `${
          getAdditionalField("KM Contestado", row.adicionais) || "-"
        } Km`;
      },
    },
    {
      name: "Km.Charged",
      selector: (row: ISap.ItemProps) => {
        return `${getAdditionalField("KM Cobrado", row.adicionais) || "-"} Km`;
      },
    },
    {
      name: "Amount.Charged",
      selector: (row: ISap.ItemProps) => {
        const amount = getAdditionalField("Valor Cobrado", row.adicionais);
        return amount ? `R$ ${toPtBrCurrency(amount)}` : "-";
      },
    },
  ],
};
