import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PV: ReturnProps<ISap.ItemProps> = {
  title: "Securities.Extension",
  origin: "SAP",
  type: "PV",
  permission: "PV",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Empresa", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Emission.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Credit.Account.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nome Conta Crédito", row.adicionais) || "-";
      },
    },
    {
      name: "Abbreviated.quantity",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "New.Maturity",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("Novo vencimento da S", row.adicionais) || "-"
        );
      },
    },
  ],
};
