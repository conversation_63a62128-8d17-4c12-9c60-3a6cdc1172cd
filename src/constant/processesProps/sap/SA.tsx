import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const SA: ReturnProps<ISap.ItemProps> = {
  title: "Advance.Requests",
  origin: "SAP",
  type: "SA",
  permission: "SA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Provider",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Expiry.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data Venc. Pgto", row.adicionais) || "-";
      },
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
  ],
  detailColumns: [
    {
      name: "Purchasing.Document.No",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nº Doc de Compras", row.adicionais) || "-";
      },
    },
    {
      name: "Advance.Money.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Descr. Adto.", row.adicionais) || "-";
      },
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Data Criação", row.adicionais) || "-";
      },
    },
    {
      name: "Expected.Discharge.Date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Dt. Previsão Baixa", row.adicionais) || "-";
      },
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Observation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Observação", row.adicionais) || "-";
      },
    },
  ],
};
