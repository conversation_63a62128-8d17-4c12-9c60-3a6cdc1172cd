import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const OL: ReturnProps<ISap.ItemProps> = {
  title: "Blocked.Clients.Release.Monitor",
  origin: "SAP",
  type: "OL",
  permission: "OL",
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Type.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_REGRA", row.adicionais) || "-";
      },
    },
    {
      name: "Emission.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Client",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Payment.terms",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZTERM", row.adicionais) || "-";
      },
    },
    {
      name: "New.Payment.terms",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ZTERM_NEW", row.adicionais) || "-";
      },
    },
    {
      name: "Credit.Limit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KLIMK", row.adicionais) || "-";
      },
    },
    {
      name: "New.Credit.Limit",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("KLIMK_NEW", row.adicionais) || "-";
      },
    },
    {
      name: "Sales.value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SAUFT", row.adicionais) || "-";
      },
    },
    {
      name: "Debt.receivable",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SKFOR", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Compromisse",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBLIG", row.adicionais) || "-";
      },
    },
    {
      name: "Overduo",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OVERDUO_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Days.overdue.all.invoices",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("D_TOTAL_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Overduo.days.delay",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("D_OVERDUO_AC", row.adicionais) || "-";
      },
    },
    {
      name: "Manager.s.Note",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("OBS_GESTOR", row.adicionais) || "-";
      },
    },
  ],
  hasDetailModal: true,
  hasDetailRoute: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document")}:`}</div>
            {rowHeader?.documento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Emission.Date")}:`}</div>
            {rowHeader?.data_emissao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Process")}:`}</div>
            OL
          </td>
          <td>
            <div className="tableTitle">{`${t("Lock.rule.description")}:`}</div>
            {getAdditionalField("DESC_REGRA", rowHeader?.adicionais)}
          </td>
        </tr>
      </tbody>
    );
  },
};
