import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

const typeOfAdditional = (row: ISap.ItemProps) => {
  switch (row.material) {
    case "POS_VIAGEM":
      return 1;
    case "PRE_VIAGEM":
      return 2;
    case "DESLOCAMENTO_VAZIO":
      return 3;
    default:
      break;
  }
};

export const AF: ReturnProps<ISap.ItemProps> = {
  title: "Additional.Primary.Travel.Shipping",
  origin: "SAP",
  type: "AF",
  permission: "AF",
  hasDetailModal: true,
  additional1: (row: ISap.ItemProps) => {
    return getAdditionalField("VBELN", row.adicionais);
  },
  additional2: (row: ISap.ItemProps) => {
    return getAdditionalField("SEQUENCIAL", row.adicionais);
  },
  headerColumns: [
    {
      name: "Type.Of.Additional.AF",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Forwarding.Agent.AF",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Origin",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_CENTRO", row.adicionais) || "-";
      },
    },
    {
      name: "Arrival.Point.AF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_DEST", row.adicionais) || "-";
      },
    },
    {
      name: "Additional.Value.AF",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VAL_ADIC_SOLIC", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td className="afProcess">
            <div className="tableTitle">{t("Type.Of.Additional.AF")}</div>
            {rowHeader?.material || "-"}
          </td>
          <td className="afProcess">
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
          <td className="afProcess">
            <div className="tableTitle">{t("Forwarding.Agent.AF")}</div>
            {rowHeader?.fornecedor || "-"}
          </td>
        </tr>
        <tr>
          <td className="afProcess">
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {getAdditionalField("DESC_CENTRO", rowHeader?.adicionais) || "-"}
          </td>
          <td className="afProcess">
            <div className="tableTitle">{`${t("Arrival.Point.AF")}:`}</div>
            {getAdditionalField("DESC_DEST", rowHeader?.adicionais) || "-"}
          </td>
          <td className="afProcess">
            <div className="tableTitle">{t("Additional.Value.AF")}</div>
            {getAdditionalField("VAL_ADIC_SOLIC", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowDetail = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <tbody>
        <tr>
          <td className="afProcessDetailsTable">
            <div className="tableTitle">{t("Request.Of.Date.AF")}</div>
            {rowDetail?.data_emissao}
          </td>
          <td className="afProcessDetailsTable">
            <div className="tableTitle">{`${t("Trip")}:`}</div>
            {rowDetail?.documento}
          </td>

          {typeOfAdditional(rowDetail) === 3 && (
            <td>
              <div className="tableTitle">{t("KM.Distance.AF")}</div>
              {getAdditionalField("NUDISTKM", rowDetail?.adicionais)}
            </td>
          )}

          {typeOfAdditional(rowDetail) !== 1 && (
            <td colSpan={typeOfAdditional(rowDetail) === 2 ? 2 : 1}>
              <div className="tableTitle">{`${t("Delivery.AF")}:`}</div>
              {getAdditionalField("VBELN", rowDetail?.adicionais)}
            </td>
          )}

          {typeOfAdditional(rowDetail) === 1 && (
            <td colSpan={2}>
              <div className="tableTitle">{`${t("Travel.Date.AF")}:`}</div>
              {getAdditionalField("DATA_VIAGEM", rowDetail?.adicionais)}
            </td>
          )}
        </tr>
        <tr>
          {typeOfAdditional(rowDetail) !== 1 && (
            <td colSpan={typeOfAdditional(rowDetail) === 3 ? 4 : 2}>
              <div className="tableTitle">{`${t("Reason")}:`}</div>
              {getAdditionalField("DESC_MOTIVO", rowDetail?.adicionais)}
            </td>
          )}

          {typeOfAdditional(rowDetail) !== 3 && (
            <td colSpan={typeOfAdditional(rowDetail) === 1 ? 4 : 2}>
              <div className="tableTitle">{`${t("Observation")}:`}</div>
              {getAdditionalField("OBS", rowDetail?.adicionais)}
            </td>
          )}
        </tr>
        <tr className="detailModalContent">
          {typeOfAdditional(rowDetail) === 1 && (
            <td>
              <div className="tableTitle">{t("Paid.Freight.AF")}</div>
              {getAdditionalField("VAL_FRETE_ORIG", rowDetail?.adicionais)}
            </td>
          )}
          {typeOfAdditional(rowDetail) === 2 && (
            <>
              <td>
                <div className="tableTitle">{t("Total.Freight.AF")}</div>
                {getAdditionalField("VAL_SOL_VIAG", rowDetail?.adicionais)}
              </td>
              <td>
                <div className="tableTitle">{t("Freight.Table.AF")}</div>
                {getAdditionalField("VAL_TAB_FRETE", rowDetail?.adicionais)}
              </td>
              <td>
                <div className="tableTitle">{t("Additional.AF")}</div>
                {getAdditionalField("VAL_ADIC_SOLIC", rowDetail?.adicionais)}
              </td>
            </>
          )}

          {typeOfAdditional(rowDetail) === 1 && (
            <>
              <td>
                <div className="tableTitle">{t("Additional.Requested.AF")}</div>
                {getAdditionalField("VAL_ADIC_SOLIC", rowDetail?.adicionais)}
              </td>
              <td>
                <div className="tableTitle">{t("Total.Freight.AF")}</div>
                {getAdditionalField("VAL_TOT_VIAG", rowDetail?.adicionais)}
              </td>
            </>
          )}

          {typeOfAdditional(rowDetail) !== 3 && (
            <td>
              <div className="tableTitle">{`${t("%.Detour.AF")}:`}</div>
              {getAdditionalField("PERC_DESV", rowDetail?.adicionais)}
            </td>
          )}

          {typeOfAdditional(rowDetail) === 3 && (
            <>
              <td colSpan={2}>
                <div className="tableTitle">{`${t("Package.Origin.AF")}:`}</div>
                {getAdditionalField("ORIG_DESC_VIAGEM", rowDetail?.adicionais)}
              </td>
              <td colSpan={2}>
                <div className="tableTitle">{t("Package.Destination.AF")}</div>
                {getAdditionalField("DEST_DESC_VIAGEM", rowDetail?.adicionais)}
              </td>
            </>
          )}
        </tr>
      </tbody>
    );
  },
};
