import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const OD: ReturnProps<ISap.ItemProps> = {
  title: "New.Customer.Creation.Req",
  origin: "SAP",
  type: "OD",
  permission: "OD",
  hasDetailModal: false,
  additional1: (row: ISap.ItemProps) => {
    return row.tipo;
  },

  headerColumns: [
    {
      name: "Sales.Orders",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Rule",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "Lock.description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESC_REGRA", row.adicionais) || "-";
      },
    },
    {
      name: "Lock.date",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_BLOQUEIO", row.adicionais) || "-";
      },
    },
    {
      name: "Branch.EA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKORG", row.adicionais) || "-";
      },
    },
    {
      name: "Sales.District",
      selector: (row: ISap.ItemProps) => {
        return (
          `${getAdditionalField(
            "BZIRK",
            row.adicionais
          )} - ${getAdditionalField("BZIRK_DESC", row.adicionais)}` || "-"
        );
      },
    },
    {
      name: "Sell.Office",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKBUR", row.adicionais) || "-";
      },
    },
    {
      name: "Client.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CUSTNAME", row.adicionais) || "-";
      },
    },
    {
      name: "Seller",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SALESPERSON", row.adicionais) || "-";
      },
    },
    {
      name: "Total.FIFO.Amount.Approved",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TOTAL_VENDA_FIFO", row.adicionais) || "-";
      },
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NETWR", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{`${t("HDD.request")}: ${
                Array.isArray(rows) ? rows.length : 1
              }`}</td>
              <td>{t("Order.motivation")}</td>
              {row?.tipo === "001" && <td>{t("Credit Limit")}</td>}
              {row?.tipo === "001" && <td>{t("Total.Compromisse")}</td>}
              <td>URL</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-head={t("HDD.request")}>
                {getAdditionalField("HDDREQ", row?.adicionais) || "-"}
              </td>
              <td data-head={t("Order.motivation")}>
                {getAdditionalField("AUGRU", row?.adicionais) || "-"}
              </td>
              {row?.tipo === "001" && (
                <td>{getAdditionalField("KLIMK", row?.adicionais) || "-"}</td>
              )}
              {row?.tipo === "001" && (
                <td>{getAdditionalField("SAUFT", row?.adicionais) || "-"}</td>
              )}
              <td>
                {getAdditionalField("URL", row?.adicionais) ? (
                  <a
                    href={getAdditionalField("URL", row?.adicionais)}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {getAdditionalField("URL", row?.adicionais)}
                  </a>
                ) : (
                  "-"
                )}
              </td>
            </tr>
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Item")}</td>
              <td>{t("Material.Description.OB")}</td>
              <td>{t("Quantity")}</td>
              <td>{t("Seller.Unity")}</td>
              <td>{t("Item Category")}</td>
              <td>{t("Sugest.Price")}</td>
              <td>{t("Inserted.Price")}</td>
              <td>{t("Total.Item.Value")}</td>
              <td>{t(".Price.Difference")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={row.item}>
              <td data-head={t("Item")}>{row.item || "-"}</td>
              <td data-head={t("Material.Description.OB")}>
                {row.material || "-"}
              </td>
              <td data-head={t("Quantity")}>{row.qtd || "-"}</td>
              <td data-head={t("Seller.Unity")}>
                {getAdditionalField("VRKME", row.adicionais) || "-"}
              </td>
              <td data-head={t("Item Category")}>
                {getAdditionalField("PSTYV", row.adicionais) || "-"}
              </td>
              <td data-head={t("Sugest.Price")}>
                {getAdditionalField("KBETR", row.adicionais) || "-"}
              </td>
              <td data-head={t("Inserted.Price")}>{row.vlunit || "-"}</td>
              <td data-head={t("Total.item.Value")}>{row.vlitem || "-"}</td>
              <td data-head={t(".Price.Difference")}>
                {getAdditionalField("PRICECHG", row.adicionais) || "-"}
              </td>
            </tr>
          </tbody>
        </TableInternal>
      </>
    );
  },
};
