import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const LP: ReturnProps<ISap.ItemProps> = {
  title: "Losting.Of.Losses",
  origin: "SAP",
  type: "LP",
  permission: "LP",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "N.Solicitation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NR.SOLIC", row.adicionais) || "-";
      },
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Center.LP",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("CENTRO", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("USUARIO", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <TableInternal>
        <thead>
          <tr>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Ledger.Account.LP")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Discard.Code")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Reason")}
            </td>
            <td width={200} style={{ paddingBottom: "0px" }}>
              {t("Total.Value")}
            </td>
          </tr>
        </thead>
        <tbody>
          <tr
            style={{
              border: "solid 1px rgba(0, 0, 0, 0.25)",
              borderLeft: "0px",
              borderRight: "0px",
            }}
          >
            <td
              data-head={t("Ledger.Account.LP")}
              style={{ paddingTop: "0px" }}
            >
              {getAdditionalField("CTA RAZAO", row?.adicionais)}
            </td>
            <td data-head={t("Discard.Code")} style={{ paddingTop: "0px" }}>
              {`${getAdditionalField(
                "COD.DESCARTE",
                row?.adicionais
              )} - ${getAdditionalField("DESCRICAO", row?.adicionais)}`}
            </td>
            <td data-head={t("Reason")} style={{ paddingTop: "0px" }}>
              {getAdditionalField("MOTIVO", row?.adicionais)}
            </td>

            <td data-head={t("Total.Value")} style={{ paddingTop: "0px" }}>
              {row?.vldoc}
            </td>
          </tr>
        </tbody>

        <thead>
          <tr>
            <td
              width={200}
              style={{
                paddingBottom: "0px",
                paddingTop: "20px",
              }}
            >
              {t("Material")}
            </td>
            <td
              width={200}
              style={{
                paddingBottom: "0px",
                paddingTop: "20px",
              }}
            >
              {t("Material.Description.LP")}
            </td>
            <td
              width={200}
              style={{
                paddingBottom: "0px",
                paddingTop: "20px",
              }}
            >
              {t("Quantity")}
            </td>
            <td
              width={200}
              style={{
                paddingBottom: "0px",
                paddingTop: "20px",
              }}
            >
              {t("Value")}
            </td>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(rows) &&
            rows.map((item: ISap.ItemProps) => {
              return (
                <tr key={`fragintern-${item?.documento}`}>
                  <td
                    data-head={t("Material")}
                    width={200}
                    style={{
                      paddingBottom: "0px",
                    }}
                  >
                    {getAdditionalField("MATERIAL", item?.adicionais)}
                  </td>
                  <td
                    data-head={t("Material.Description.LP")}
                    width={200}
                    style={{
                      paddingBottom: "0px",
                    }}
                  >
                    {getAdditionalField("DESC_MAT", item?.adicionais)}
                  </td>
                  <td
                    data-head={t("Quantity")}
                    width={200}
                    style={{
                      paddingBottom: "0px",
                    }}
                  >
                    {getAdditionalField("QUANTIDADE", item?.adicionais)}
                  </td>
                  <td
                    data-head={t("Value")}
                    width={200}
                    style={{
                      paddingBottom: "0px",
                    }}
                  >
                    {getAdditionalField("valor", item?.adicionais)}
                  </td>
                </tr>
              );
            })}
        </tbody>
      </TableInternal>
    );
  },
};
