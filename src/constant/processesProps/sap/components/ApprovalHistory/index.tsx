/* eslint-disable react/jsx-one-expression-per-line */
import React from "react";
import { useTranslation } from "react-i18next";
import { BoxStatus, Container } from "./styles";

export interface ApprovalStep {
  state: "Pendente" | "Aprovado";
  name: string;
}

const handleStatusColor = (state: string) => {
  switch (state) {
    case "Pendente":
      return "#E1E1E1";
    case "Aprovado":
      return "#2BA4F2";
    default:
      return "#E1E1E1";
  }
};

interface ApprovalHistoryProps {
  approvalRequests: ApprovalStep[];
}

export const ApprovalHistory: React.FC<ApprovalHistoryProps> = ({
  approvalRequests,
}) => {
  const { t } = useTranslation();

  const StatusLine: React.FC = () => (
    <div
      className="statusLine"
      style={{ width: "64px", border: "1px solid rgba(0, 0, 0, 0.2)" }}
    />
  );

  return (
    <Container>
      {approvalRequests.map((step: ApprovalStep, index: number) => (
        <div key={index}>
          <BoxStatus
            $colorStatus={handleStatusColor(step.state)}
            $index={index}
            $length={approvalRequests.length}
          >
            <div>{t(step.state)}</div>
            <div style={{ fontSize: "12px" }}>{step.name || "-"}</div>
          </BoxStatus>
          {index !== approvalRequests.length - 1 && <StatusLine />}
        </div>
      ))}
    </Container>
  );
};
