import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  flex-direction: row;
  color: black;
  align-items: center;
  background-color: white;
  padding: 16px;
  flex-wrap: wrap;

  > div {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
`;
export const BoxStatus = styled.div<{
  $colorStatus: string;
  $index: number;
  $length: number;
}>`
  display: flex;
  flex-direction: column;
  border-radius: 3px;
  color: black;
  text-align: center;
  width: 170px;
  border: 3px solid ${(props) => props.$colorStatus};

  @media (min-width: 749px) {
    margin-bottom: ${(props) =>
      props.$length &&
      props.$length > 3 &&
      (props.$index === 1 || props.$index === 2 || props.$index === 3)
        ? "16px"
        : ""};
  }
  > :first-of-type {
    padding: 16px;
    background-color: ${(props) => props.$colorStatus};
  }

  > :last-child {
    padding: 16px;
  }
`;

export const BoxInitFinalHistory = styled.div`
  padding: 16px;
  background-color: #fcf8e2;
  width: 170px;
  height: 60px;
  justify-content: center;
  align-items: center;
`;
