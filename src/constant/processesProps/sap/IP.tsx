import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const IP: ReturnProps<ISap.ItemProps> = {
  title: "Release.Payment",
  origin: "SAP",
  type: "IP",
  permission: "IP",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "N.Solicitation",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Type",
      selector: (row: ISap.ItemProps) => row.tipo || "-",
    },
    {
      name: "ME.Value",
      selector: (row: ISap.ItemProps) => row.vlitem || "-",
    },
    {
      name: "MI.Value",
      selector: (row: ISap.ItemProps) => row.vlunit || "-",
    },
    {
      name: "Close.Tax",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
  ],
};
