import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const GA: ReturnProps<ISap.ItemProps> = {
  title: "ATP.Request",
  origin: "SAP",
  type: "GA",
  permission: "GA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Sales.Org",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VKORG", row.adicionais) || "-";
      },
    },
    {
      name: "Plant.GA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("WERKS", row.adicionais) || "-";
      },
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MATNR", row.adicionais) || "-";
      },
    },
    {
      name: "Old.Material.Number",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("BISMT", row.adicionais) || "-";
      },
    },
    {
      name: "Description.GA",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("MAKTX", row.adicionais) || "-";
      },
    },
    {
      name: "Net.weight",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("NTGEW", row.adicionais) || "-";
      },
    },
  ],
};
