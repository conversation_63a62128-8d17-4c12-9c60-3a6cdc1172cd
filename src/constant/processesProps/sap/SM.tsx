import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const SM: ReturnProps<ISap.ItemProps> = {
  title: "Request.DMS.Materials",
  origin: "SAP",
  type: "SM",
  permission: "SM",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Solicitation.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Solicitation.date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Solicitation.type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Tipo Solicitação", row.adicionais) || "-";
      },
    },
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Order",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("ORDEM", row.adicionais) || "-";
      },
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro de Custo", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <h3>{t("Observation")}</h3>
        <span>{getAdditionalField("Observação", row.adicionais)}</span>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Item")}</td>
              <td>{t("Material")}</td>
              <td>{t("Request.quantity")}</td>
              <td>{t("Origin.department")}</td>
              <td>{t("Destination.center")}</td>
              <td>{t("Destination.departament")}</td>
              <td>{t("Batch")}</td>
              <td>{t("Cost.Center")}</td>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(rows) &&
              rows.map((add: ISap.ItemProps, index: number) => {
                return (
                  <tr key={`tr-purchase-order-${add.documento}-${index}`}>
                    <td data-head={t("Item")} className="noCheckbox">
                      {add.item}
                    </td>
                    <td data-head={t("Material")}>{add.material}</td>
                    <td data-head={t("Request.quantity")}>{add.qtd}</td>
                    <td data-head={t("Origin.department")}>
                      {getAdditionalField("Deposito Origem", add.adicionais)}
                    </td>
                    <td data-head={t("Destination.center")}>
                      {getAdditionalField("Centro Destino", add.adicionais)}
                    </td>
                    <td data-head={t("Destination.departament")}>
                      {getAdditionalField("Deposito Destino", add.adicionais)}
                    </td>
                    <td data-head={t("Batch")}>
                      {getAdditionalField("Lote", add.adicionais)}
                    </td>
                    <td data-head={t("Cost.Center")}>
                      {getAdditionalField("Centro Custo Item", add.adicionais)}
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
