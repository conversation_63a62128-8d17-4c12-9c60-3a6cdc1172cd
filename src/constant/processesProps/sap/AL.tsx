import { Warning } from "@mui/icons-material";
import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export interface DataPropsAL {
  verify: string;
  object: ISap.ItemProps[];
}

export const getUniqueDocAL = (items: ISap.ItemProps[]): DataPropsAL[] => {
  // obter docs unicos
  const equals = [] as DataPropsAL[];
  items.forEach((element) => {
    const verify = `${element.requisitante}_${getAdditionalField(
      "Transporte",
      element.adicionais
    )}_${getAdditionalField("Placa", element.adicionais)}_${getAdditionalField(
      "Data da Criação",
      element.adicionais
    )}_${getAdditionalField("LEXP", element.adicionais)}_${getAdditionalField(
      "Valor Total",
      element.adicionais
    )}`;

    if (equals.length === 0) {
      equals.push({ verify, object: [element] });
    } else {
      let newInclude = true;
      equals.forEach((e, index) => {
        if (e.verify === verify) {
          e.object.push(element);
          newInclude = false;
        }
        if (index === equals.length - 1 && newInclude) {
          equals.push({ verify, object: [element] });
        }
      });
    }
  });
  return equals;
};

export const AL: ReturnProps<ISap.ItemProps> = {
  title: "Additional.Freight",
  origin: "SAP",
  type: "AL",
  permission: "AL",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Car.plate",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Placa", row.adicionais) || "-",
    },
    {
      name: "Date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Data da Criação", row.adicionais) || "-",
    },
    {
      name: "Transport",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Transporte", row.adicionais) || "-",
    },
    {
      name: "LEXP",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("LEXP", row.adicionais) || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Alert.Divergence.Unload",
      cell: (row: ISap.ItemProps) => {
        return getAdditionalField("JUSTIFICATIVA", row.adicionais) ? (
          <Warning color="warning" />
        ) : (
          <span>N/A</span>
        );
      },
    },
    {
      name: "Total",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Valor Total", row.adicionais) || "-";
      },
    },
  ],
  detailColumns: [
    {
      name: "Alert.Divergence.Unload",
      selector: (row) => {
        return getAdditionalField("JUSTIFICATIVA", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(rows) ? rows[0] : rows;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{t("Transport")}</div>
            {getAdditionalField("Transporte", rowHeader.adicionais)}
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div className="tableTitle">{t("Total.Daily.Surplus")}</div>
            {getAdditionalField("Total Diar.excedent", rowHeader.adicionais)}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Additional.Total.Overnight")}`}
            </div>
            {getAdditionalField("Total Adic. Pernoite", rowHeader.adicionais)}
          </td>
        </tr>
        {rows && (
          <tr>
            <td colSpan={3}>
              <TableInternal>
                <thead>
                  <tr>
                    <td></td>
                    <td colSpan={2}>{t("Daily.surplus")}</td>
                    <td colSpan={2}>{t("Additional.Overnight")}</td>
                  </tr>
                  <tr>
                    <td>{t("AL.Requester")}</td>
                    <td>{t("Quantity")}</td>
                    <td>{t("Value")}</td>
                    <td>{t("Quantity")}</td>
                    <td>{t("Value")}</td>
                  </tr>
                </thead>
                <tbody>
                  {Array.isArray(rows) &&
                    rows.map((row) => {
                      return (
                        <tr
                          key={`tr-additionalfreight-intern-${row.documento}`}
                        >
                          <td data-head={t("AL.Requester")}>{row.documento}</td>
                          <td
                            data-head={`${t("Daily.surplus")} - ${t(
                              "Quantity"
                            )}`}
                          >
                            {getAdditionalField(
                              "Quant.Diar.excedent",
                              row.adicionais
                            )}
                          </td>
                          <td
                            data-head={`${t("Daily.surplus")} - ${t("Value")}`}
                          >
                            {getAdditionalField(
                              "Diar.excedent",
                              row.adicionais
                            )}
                          </td>
                          <td
                            data-head={`${t("Additional.Overnight")} - ${t(
                              "Quantity"
                            )}`}
                          >
                            {getAdditionalField(
                              "Quant.Adic.Pernoite",
                              row.adicionais
                            )}
                          </td>
                          <td
                            data-head={`${t("Additional.Overnight")} - ${t(
                              "Value"
                            )}`}
                          >
                            {getAdditionalField(
                              "Adic. Pernoite",
                              row.adicionais
                            )}
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </TableInternal>
            </td>
          </tr>
        )}
      </tbody>
    );
  },
};
