import { ThumbDownOffAltOutlined, ThumbUpOutlined } from "@mui/icons-material";
import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PG: ReturnProps<ISap.ItemProps> = {
  title: "Grain.Order",
  origin: "SAP",
  type: "PG",
  permission: "PG",
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Sequential.AV",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Amount",
      selector: (row: ISap.ItemProps) =>
        `${row.qtd || "-"} ${
          getAdditionalField("Unid. medida (Qtde)", row.adicionais) || "-"
        }`,
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Net.Price",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PREÇO LIQUIDO", row.adicionais) || "-";
      },
    },
    {
      name: "FA.Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Price.table",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("PREÇO TABELA", row.adicionais) || "-";
      },
    },
    {
      name: "Price.inside.table",
      cell: (row: ISap.ItemProps) => (
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          {(() => {
            const value = getAdditionalField(
              "PRC DENTRO TABELA",
              row.adicionais
            );
            switch (value) {
              case "SIM":
                return <ThumbUpOutlined color="success" />;
              case "NAO":
                return <ThumbDownOffAltOutlined color="error" />;
              default:
                return "N/A";
            }
          })()}
          {getAdditionalField("PRC DENTRO TABELA", row.adicionais) || "-"}
        </div>
      ),
    },
  ],
  hasDetailModal: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Sequential.AV")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Request.Date")}:`}</div>
            {rowHeader?.data_emissao
              ? `${rowHeader?.data_emissao} ${getAdditionalField(
                  "Hora da Emissão",
                  rowHeader?.adicionais
                )}`
              : "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Provider")}:`}</div>
            {rowHeader?.fornecedor || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Material")}:`}</div>
            {rowHeader?.material || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Amount")}:`}</div>
            {`${rowHeader?.qtd} ${getAdditionalField(
              "Unid. medida (Qtde)",
              rowHeader?.adicionais
            )}` || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Total.Value")}:`}</div>
            {rowHeader?.vldoc || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Net.Price")}:`}</div>
            {getAdditionalField("PREÇO LIQUIDO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Table.price")}:`}</div>
            {getAdditionalField("PREÇO TABELA", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Price.justification")}:`}</div>
            {getAdditionalField("JUSTIF.PREÇO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Start.Shipping.Date")}:`}</div>
            {getAdditionalField("Data Remessa (De)", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("End.Shipping.Date")}:`}</div>
            {getAdditionalField("Data Remessa (Até)", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Withdrawal")}:`}</div>
            {getAdditionalField("Retirada", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Incoterm.XP")}:`}</div>
            {getAdditionalField("Incoterms", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Payment.terms")}:`}</div>
            {getAdditionalField("Cond. Pgto", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("IVA")}:`}</div>
            {getAdditionalField("IVA", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Buyer")}:`}</div>
            {getAdditionalField("Comprador", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("FA.Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Observations")}:`}</div>
            {getAdditionalField("Observação", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
};
