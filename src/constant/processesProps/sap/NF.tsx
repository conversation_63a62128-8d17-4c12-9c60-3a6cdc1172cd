import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const NF: ReturnProps<ISap.ItemProps> = {
  title: "Invoice.GNF",
  origin: "SAP",
  type: "NF",
  permission: "NF",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Document",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Scenario",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Cenário", row.adicionais) || "-";
      },
    },
    {
      name: "Business.Place",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Local de negócio", row.adicionais) || "-";
      },
    },
    {
      name: "Partner",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Parceiro", row.adicionais) || "-";
      },
    },
    {
      name: "Value",
      selector: (row: ISap.ItemProps) => row.vldoc.replace(/\ /g, "") || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;
    return (
      <div id={`collapse-${row.documento}-${row.item}`}>
        <h3>{t("Comments")}</h3>
        <h4>{getAdditionalField("COM", row.adicionais)}</h4>
      </div>
    );
  },
  hasDetailRoute: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    const rowDetail = Array.isArray(row) ? (row[1] as ISap.ItemProps) : row;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Request.Date")}:`}</div>
            {rowHeader?.data_emissao || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Scenario")}:`}</div>
            {getAdditionalField("Cenário", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Business.Place")}:`}</div>
            {getAdditionalField("Local de negócio", rowHeader?.adicionais) ||
              "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Partner")}:`}</div>
            {`${getAdditionalField("Parceiro", rowHeader?.adicionais) || "-"} -
          ${getAdditionalField("Nome parceiro", rowDetail?.adicionais) || "-"}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {rowHeader?.vldoc || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {`${rowHeader?.requisitante || "-"} - ${
              getAdditionalField("Nome solicitante", rowDetail?.adicionais) ||
              "-"
            }`}
          </td>
          <td></td>
        </tr>
      </tbody>
    );
  },
  detailModalContent: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    return (
      <tbody>
        {Array.isArray(rows) &&
          rows.map((row: ISap.ItemProps) => (
            <tr
              key={`modal-table-internal-${row.documento}-${row.item}`}
              className="detailModalContent"
            >
              <td>
                <span>{t("Item")}</span>
                {row.item}
              </td>
              <td>
                <span>{t("Material")}</span>
                {getAdditionalField("Material", row.adicionais).substring(4)}
              </td>
              <td>
                <span>{t("Material.description")}</span>
                {getAdditionalField("Descrição material", row.adicionais)}
              </td>
              <td>
                <span>{t("Quantity")}</span>
                {getAdditionalField("Quantidade", row.adicionais)}
              </td>
              <td>
                <span>{t("Value")}</span>
                {getAdditionalField("Valor", row.adicionais)}
              </td>
            </tr>
          ))}
      </tbody>
    );
  },
};
