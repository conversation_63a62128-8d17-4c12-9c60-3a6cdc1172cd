import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { dateBDtoRead } from "../../../utils/Date";
import {
  getAdditionalField,
  getPartialWordFields,
} from "../../../utils/GetItemsSap";

export const IL: ReturnProps<ISap.ItemProps> = {
  title: "Fixed.Assets.Report",
  origin: "SAP",
  type: "IL",
  permission: "IL",
  headerColumns: [
    {
      name: "Report.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Company",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("EMPRESA", row.adicionais) || "-";
      },
    },
    {
      name: "Imobilizeted",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESCRIÇÃO", row.adicionais) || "-";
      },
    },
    {
      name: "Market.Value",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("VALOR_MERCADO", row.adicionais) || "-";
      },
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("SOLICITANTE", row.adicionais) || "-";
      },
    },
  ],
  hasDetailModal: true,
  hasDetailRoute: true,
  detailModalHeader: async (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    const attachments = getPartialWordFields("ANEXO", rowHeader?.adicionais);
    const storageToken = import.meta.env.VITE_BLOB_STORAGE_TOKEN;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Report.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {dateBDtoRead(getAdditionalField("DATA", rowHeader?.adicionais)) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Imobilizeted")}:`}</div>
            {getAdditionalField("IMOBILIZADO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Fixed.asset.description.IL")}:`}
            </div>
            {getAdditionalField("DESCRICAO_IMOB", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Acquisition.Value")}:`}</div>
            {getAdditionalField("VALOR_AQUISICAO", rowHeader?.adicionais) ||
              "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Residual.Value")}:`}</div>
            {getAdditionalField("VALOR_RESIDUAL", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Market.Value")}:`}</div>
            {getAdditionalField("VALOR_MERCADO", rowHeader?.adicionais) || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {getAdditionalField("EMPRESA", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Attachments")}:`}</div>
            {attachments?.map((att) => {
              const attArray = String(att)?.split(";");
              const attName = attArray[0];
              const attNumber = attArray[1];

              return (
                <a
                  key={`attach-${attNumber}`}
                  href={`${
                    import.meta.env.VITE_IL_ATACHMENT_URL_BASE
                  }${attNumber}?${storageToken}`}
                  rel="noreferrer"
                  target="_blank"
                  download
                  style={{
                    display: "block",
                    fontSize: 14,
                    marginBottom: 8,
                  }}
                >
                  <span>{attName}</span>
                </a>
              );
            })}
          </td>
        </tr>
      </tbody>
    );
  },
};
