import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";
import { ApprovalHistory, ApprovalStep } from "./components/ApprovalHistory";

export const CC_SAP: ReturnProps<ISap.ItemProps> = {
  title: "SAP.Contracts",
  origin: "SAP",
  type: "CC_SAP",
  permission: "CC",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Gr.Purchasing",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("grupo de compras", row.adicionais) ||
        getAdditionalField("grupo-compras", row.adicionais) ||
        getAdditionalField("purch. group", row.adicionais) ||
        "-",
    },
    {
      name: "Buyer.Group.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Nome gpr. compras", row.adicionais) || "-";
      },
    },
    {
      name: "Creation.date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("data da criação", row.adicionais) ||
        getAdditionalField("creado el", row.adicionais) ||
        getAdditionalField("created on", row.adicionais) ||
        "-",
    },
    {
      name: "Total.Value",
      selector: (row: ISap.ItemProps) => row.vldoc || "-",
    },
    {
      name: "Expiration.date",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Data de Validade", row.adicionais) || "-",
    },
    {
      name: "Provider",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Delegated.Purchasing",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Compras Delegadas", row.adicionais) || "-",
    },
    {
      name: "Approval.Level",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Nivel de aprovação", row.adicionais) || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    const anexoField = getAdditionalField("ANEXO_1", row.adicionais);
    let attName = "";
    let attNumber = "";

    if (anexoField && anexoField.includes(";")) {
      const attArray = String(anexoField).split(";");
      attName = attArray[0] || "";
      attNumber = attArray[1] || "";
    } else {
      attName = anexoField || "";
      attNumber = encodeURIComponent(attName);
    }

    const approvalRequests = row.adicionais
      .filter(
        (adicional) =>
          adicional.campo === "Pendente" || adicional.campo === "Aprovado"
      )
      .map((adicional) => ({
        state: adicional.campo as "Pendente" | "Aprovado",
        name: adicional.valor,
      }));
    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{`${t("Information")}`}</td>
              <td>{`${t("Last.Value")}`}</td>
              <td>{`${t("Actual.Value")}`}</td>
              <td>{`${t("Modification.Date")}`}</td>
            </tr>
          </thead>
          <tbody>
            {(row as ISap.ItemProps).adicionais.some(
              (list: ISap.AdicionalProps) => list.campo === "LOG_CAB"
            ) ? (
              (row as ISap.ItemProps).adicionais.map(
                (list: ISap.AdicionalProps) => {
                  if (list.campo === "LOG_CAB") {
                    // Formato esperado: "Condição de Pagamento :|D071|D070|Data de Modificação :|16/04/2025 17:54:21"
                    const valor = list.valor;

                    // Extrair as partes da string
                    const partes = String(valor).split("|");
                    let information = "";
                    let lastValue = "";
                    let actualValue = "";
                    let modificationDate = "";

                    if (partes.length >= 1) {
                      // Extrair information - primeira parte antes de :|
                      information = partes[0].split(":|")[0].trim();
                    }

                    if (partes.length >= 2) {
                      // Extrair lastValue
                      lastValue = partes[1];
                    }

                    if (partes.length >= 3) {
                      // Extrair actualValue
                      actualValue = partes[2];
                    }

                    if (partes.length >= 4) {
                      // Extrair modificationDate - última parte após "Data de Modificação :|"
                      const dataParte = partes.slice(3).join("|");
                      if (dataParte.includes(":|")) {
                        modificationDate = dataParte.split(":|")[1].trim();
                      }
                    }

                    return (
                      <tr key={`log-sap-${information}`}>
                        <td data-head={t("Information")} className="noCheckbox">
                          {information.replace(":", "")}
                        </td>
                        <td data-head={t("Last.Value")}>{lastValue || "-"}</td>
                        <td data-head={t("Actual.Value")}>
                          {actualValue || "-"}
                        </td>
                        <td data-head={t("Modification.Date")}>
                          {modificationDate || "-"}
                        </td>
                      </tr>
                    );
                  }
                  return null;
                }
              )
            ) : (
              <tr>
                <td data-head={t("Information")} className="noCheckbox">
                  -
                </td>
                <td data-head={t("Last.Value")}>-</td>
                <td data-head={t("Actual.Value")}>-</td>
                <td data-head={t("Modification.Date")}>-</td>
              </tr>
            )}
          </tbody>
          <thead>
            <tr>
              <td colSpan={3}>{t("Executive.Justification")}</td>
              <td>{t("Annex")}</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-head={t("Executive.Justification")} colSpan={3}>
                {getAdditionalField(
                  "Justificativa",
                  (row as ISap.ItemProps).adicionais
                ) || "-"}
              </td>
              <td data-head={t("Annex")}>
                {attName ? (
                  <div>
                    <a
                      href={`${process.env.REACT_APP_CC_ATACHMENT_URL_BASE}${attNumber}?${process.env.REACT_APP_BLOB_STORAGE_TOKEN}`}
                      download={attName || "anexo"}
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={(e) => {
                        if (!attNumber) {
                          e.preventDefault();
                          alert("Anexo não disponível");
                        }
                      }}
                    >
                      {attName}
                    </a>
                  </div>
                ) : (
                  "-"
                )}
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <td colSpan={12}>{t("Contract.Approval.Flow")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={`tr2-${row.documento}`}>
              <td colSpan={3} data-head={t("Fluxo de Aprovações da Tarefa")}>
                {!!approvalRequests.length ? (
                  <ApprovalHistory
                    approvalRequests={approvalRequests as ApprovalStep[]}
                  />
                ) : (
                  "-"
                )}
              </td>
            </tr>
          </tbody>
        </TableInternal>
      </>
    );
  },
  detailModalHeader: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(rows) ? rows[0] : rows;
    return (
      <tbody>
        <tr>
          <td>
            <TableInternal>
              <thead>
                <tr>
                  <th>{`${t("Material")}`}</th>
                  <th>{`${t("Quantity")}`}</th>
                  <th>{`${t("Item.Value")}`}</th>
                </tr>
              </thead>
              <tbody>
                {rowHeader && (
                  <tr key={`document-${rowHeader.item.trim()}`}>
                    <td data-head={t("Material")}>{rowHeader.material}</td>
                    <td data-head={t("Quantity")}>{rowHeader.qtd}</td>
                    <td data-head={t("Item.Value")}>{rowHeader.vlitem}</td>
                  </tr>
                )}
              </tbody>
            </TableInternal>
          </td>
        </tr>
      </tbody>
    );
  },
};
