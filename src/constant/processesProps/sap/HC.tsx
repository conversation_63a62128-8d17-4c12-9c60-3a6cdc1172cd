import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const HC: ReturnProps<ISap.ItemProps> = {
  title: "Maintenance.Request.Cost.Center",
  origin: "SAP",
  type: "HC",
  permission: "HC",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Document.Number",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "Requester",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Cost.Center",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Centro.de.Custo", row.adicionais) || "-";
      },
    },
    {
      name: "Observation",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Observação", row.adicionais) || "-";
      },
    },
  ],
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;
    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {rowHeader?.documento || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {rowHeader?.data_emissao || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {rowHeader?.requisitante || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {getAdditionalField("Centro de Custo", rowHeader?.adicionais) ||
              "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Current.Vision")}:`}</div>
            <TableInternal className="mobileExpand">
              <thead>
                <tr>
                  <td>{t("Hierarchy")}</td>
                  <td>{t("Responsible")}</td>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-head={t("Hierarchy")} className="noCheckbox">
                    {getAdditionalField(
                      "Atual Hierarquia",
                      rowHeader?.adicionais
                    ) || "-"}
                  </td>
                  <td data-head={t("Responsible")}>
                    {getAdditionalField(
                      "Atual Responsavel",
                      rowHeader?.adicionais
                    ) || "-"}
                  </td>
                </tr>
              </tbody>
            </TableInternal>
          </td>
          <td>
            <div className="tableTitle">{`${t("New.Vision")}:`}</div>
            <TableInternal className="mobileExpand">
              <thead>
                <tr>
                  <td>{t("Hierarchy")}</td>
                  <td>{t("Responsible")}</td>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-head={t("Hierarchy")} className="noCheckbox">
                    {getAdditionalField(
                      "Nova Hierarquia",
                      rowHeader?.adicionais
                    ) || "-"}
                  </td>
                  <td data-head={t("Responsible")}>
                    {getAdditionalField(
                      "Novo Responsavel",
                      rowHeader?.adicionais
                    ) || "-"}
                  </td>
                </tr>
              </tbody>
            </TableInternal>
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Observation")}:`}</div>
            {getAdditionalField("Observação", rowHeader?.adicionais) || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
};
