import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const FC: ReturnProps<ISap.ItemProps> = {
  title: "Grain.Purchase",
  origin: "SAP",
  type: "FC",
  permission: "FC",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Purchase.Order",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Solicitation.type",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("Tipo da solicitação", row.adicionais) || "-",
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Provider",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Modality",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("MODALIDADE", row.adicionais) || "-",
    },
    {
      name: "Start.Shipping",
      selector: (row: ISap.ItemProps) =>
        getAdditionalField("DT INIC. REMESSA", row.adicionais) || "-",
    },
    {
      name: "Amount",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;

    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("FC.Requester")}</td>
              <td>{t("End.Shipping")}</td>
              <td>{t("Incoterms")}</td>
              <td>{t("Freight")}</td>
              <td>{t("Initial.Approver")}</td>
              <td>{t("Delivery.Location")}</td>
              <td>{t("Payment.terms")}</td>
              <td>{t("Price.Note")}</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-head={t("FC.Requester")}>{row.requisitante || "-"}</td>
              <td data-head={t("End.Shipping")}>
                {getAdditionalField("DT FIM REMESSA", row.adicionais) || "-"}
              </td>
              <td data-head={t("Incoterms")}>
                {getAdditionalField("INCOTERMS", row.adicionais) || "-"}
              </td>
              <td data-head={t("Freight")}>
                {getAdditionalField("Frete", row.adicionais) || "-"}
              </td>
              <td data-head={t("Initial.Approver")}>
                {getAdditionalField("Aprovador inicial", row.adicionais) || "-"}
              </td>
              <td data-head={t("Delivery.Location")}>
                {getAdditionalField("LOCAL RETIRADA", row.adicionais) || "-"}
              </td>
              <td data-head={t("Payment.terms")}>
                {getAdditionalField("Cond. Pgto", row.adicionais) || "-"}
              </td>
              <td data-head={t("Price.Note")}>
                {getAdditionalField("Observação (Preço)", row.adicionais) ||
                  "-"}
              </td>
            </tr>
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("From.To")}</td>
            </tr>
          </thead>
          <tbody>
            {(() => {
              const deParaFields = Array.from({ length: 25 }, (_, index) =>
                getAdditionalField(`DE_PARA${index + 1}`, row.adicionais)
              ).filter((field) => field && field.trim() !== "");

              if (deParaFields.length === 0) {
                return (
                  <tr>
                    <td data-head={t("From.To")}>-</td>
                  </tr>
                );
              }

              return deParaFields.map((field, index) => (
                <tr key={`depara-${row.documento}-${index}`}>
                  <td data-head={t("From.To")}>
                    {(() => {
                      const paraIndex = field.toLowerCase().indexOf("para");
                      if (paraIndex !== -1) {
                        const before = field.slice(0, paraIndex + 4);
                        const after = field.slice(paraIndex + 4);
                        return (
                          <>
                            {before}
                            <b>{after}</b>
                          </>
                        );
                      }
                      return field;
                    })()}
                  </td>
                </tr>
              ));
            })()}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
