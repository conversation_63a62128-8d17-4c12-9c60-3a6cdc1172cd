import { t } from "i18next";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { mathDate } from "../../../utils/Date";
import { getAdditionalField } from "../../../utils/GetItemsSap";

export const PE: ReturnProps<ISap.ItemProps> = {
  title: "Installation.PPE.Requests",
  origin: "SAP",
  type: "PE",
  permission: "PE",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "SAP.Worksheet.ID",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DISPLAY_ID", row.adicionais) || "-";
      },
    },
    {
      name: "Worksheet.Name",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("WORKSHEET_NAME", row.adicionais) || "-";
      },
    },
    {
      name: "App.Description",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("APP_DESC", row.adicionais) || "-";
      },
    },
    {
      name: "Start.Date",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("BEGIN_DATE", row.adicionais)?.replace(
            /\./g,
            "/"
          ) || "-"
        );
      },
    },
    {
      name: "End.Date",
      selector: (row: ISap.ItemProps) => {
        return (
          getAdditionalField("END_DATE", row.adicionais)?.replace(/\./g, "/") ||
          "-"
        );
      },
    },
  ],
  hasDetailRoute: true,
  detailModalHeader: (row: ISap.ItemProps | ISap.ItemProps[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as ISap.ItemProps) : row;

    let obj = {
      ID_ITEM: {
        value: null,
        header: "Item number",
      },
      MAIN_ID_ITEM: {
        value: null,
        header: "Main Item Number",
      },
      BAZ_ORAN: {
        value: null,
        header: "Base Rate",
      },
      UYG_ORAN: {
        value: null,
        header: "Real Rate",
      },
      KBETR_NEW: {
        value: null,
        header: "New value",
      },
      KONWA_NEW: {
        value: null,
        header: "New unit",
      },
      KSTBM_NEW: {
        value: null,
        header: "New scale quantity",
      },
      YF_BEGDAT: {
        value: null,
        header: "NV Start Date",
      },
      YF_ENDDAT: {
        value: null,
        header: "NV End Date",
      },
      STATU: {
        value: null,
        header: "Status",
      },
      KBETR_OLD: {
        value: null,
        header: "Old Value",
      },
      KONWA_OLD: {
        value: null,
        header: "Old unit",
      },
      DATBI: {
        value: null,
        header: "OV Start Date",
      },
      DATAB: {
        value: null,
        header: "OV End Date",
      },
      KSTBM_OLD: {
        value: null,
        header: "Old scale quantity",
      },
      VKORG: {
        value: null,
        header: "Sales Organization",
      },
      MATNR: {
        value: null,
        header: "Material Number",
      },
      KDGRP: {
        value: null,
        header: "Customer group",
      },
      KUNNR: {
        value: null,
        header: "Customer number",
      },
      KONDM: {
        value: null,
        header: "Material pricing group",
      },
      ZZPRODH2: {
        value: null,
        header: "Product hierarchy",
      },
      KFRST: {
        value: null,
        header: "Release status",
      },
      PLTYP: {
        value: null,
        header: "Price list type",
      },
    };
    obj = Object.entries(obj)
      .filter((act) => getAdditionalField(act[0], rowHeader?.adicionais) !== "")
      .reduce((acc: any, act) => {
        let value = getAdditionalField(act[0], rowHeader?.adicionais);
        if (mathDate(value)) {
          value = value?.replace(/\./g, "/");
        }
        acc[act[0]] = {
          ...act[1],
          value,
        };
        return acc;
      }, {});
    const entries = Object.entries(obj);

    return (
      <tbody>
        {entries.map((line, index) => {
          if (index % 2 === 0) {
            return (
              <tr>
                <td>
                  <div className="tableTitle">
                    {`${t(entries[index][1].header)}:`}
                  </div>
                  {entries[index][1].value}
                </td>
                {entries.length > index + 1 && (
                  <td>
                    <div className="tableTitle">
                      {`${t(entries[index + 1][1].header)}:`}
                    </div>
                    {entries[index + 1][1].value}
                  </td>
                )}
              </tr>
            );
          }
          return null;
        })}
      </tbody>
    );
  },
};
