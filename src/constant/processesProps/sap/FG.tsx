import { t } from "i18next";
import { v4 } from "uuid";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { getAdditionalField } from "../../../utils/GetItemsSap";
import { TableInternal } from "../styles";

export const FG: ReturnProps<ISap.ItemProps> = {
  title: "Grain.Freight",
  origin: "SAP",
  type: "FG",
  permission: "FG",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "Center",
      selector: (row: ISap.ItemProps) => row.centro || "-",
    },
    {
      name: "Purchase.Order.DV",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Solicitation.type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("Tipo Solicitação", row.adicionais) || "-";
      },
    },
    {
      name: "Material",
      selector: (row: ISap.ItemProps) => row.material || "-",
    },
    {
      name: "Supplier",
      selector: (row: ISap.ItemProps) => row.fornecedor || "-",
    },
    {
      name: "Request.Date",
      selector: (row: ISap.ItemProps) => row.data_emissao || "-",
    },
    {
      name: "FG.Order.Quantity",
      selector: (row: ISap.ItemProps) => row.qtd || "-",
    },
    {
      name: "Total.Freight.Value",
      selector: (row: ISap.ItemProps) => row.requisitante || "-",
    },
    {
      name: "Start.Shipping",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DT INICIO ENTREGA", row.adicionais) || "-";
      },
    },
  ],
  documentDetailHtml: (rows: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as ISap.ItemProps) : rows;
    return (
      <>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Budgeted.Freight")}</td>
              <td>{t("Negotiated.Freight")}</td>
              <td>{t("Modal")}</td>
              <td>{t("Conveyor")}</td>
              <td>{t("Contracted.Quantity")}</td>
              <td>{t("End.Shipping.Date")}</td>
              <td>{t("Location.Emb")}</td>
              <td>{t("FA.Requester")}</td>
            </tr>
          </thead>
          <tbody>
            {(rows as ISap.ItemProps[]).map((add: ISap.ItemProps) => {
              const uuidtr = v4();
              return (
                <tr key={`tr-purchase-order-${add.documento}-${uuidtr}`}>
                  <td data-head={t("Budgeted.Freight")} className="noCheckbox">
                    {getAdditionalField("FRETE TON", add.adicionais)}
                  </td>
                  <td data-head={t("Negotiated.Freight")}>
                    {getAdditionalField("FRETE VIAGEM", add.adicionais)}
                  </td>
                  <td data-head={t("Modal")}>
                    {getAdditionalField("MODALIDADE", add.adicionais)}
                  </td>
                  <td data-head={t("Conveyor")}>
                    {getAdditionalField("TRANSPORTADOR", add.adicionais)}
                  </td>
                  <td data-head={t("Contracted.Quantity")}>
                    {getAdditionalField("QTDE TON", add.adicionais)}
                  </td>
                  <td data-head={t("End.Shipping.Date")}>
                    {getAdditionalField("DT FIM ENTREGA", add.adicionais)}
                  </td>
                  <td data-head={t("Location.Emb")}>
                    {getAdditionalField("LOCAL RETIRADA", add.adicionais)}
                  </td>
                  <td data-head={t("FA.Requester")}>{row.requisitante}</td>
                </tr>
              );
            })}
          </tbody>
        </TableInternal>
        <TableInternal>
          <thead>
            <tr>
              <td>{t("From.To")}</td>
            </tr>
          </thead>
          <tbody>
            {(() => {
              const deParaFields = Array.from({ length: 25 }, (_, index) =>
                getAdditionalField(`DE_PARA${index + 1}`, row.adicionais)
              ).filter((field) => field && field.trim() !== "");

              if (deParaFields.length === 0) {
                return (
                  <tr>
                    <td data-head={t("From.To")}>-</td>
                  </tr>
                );
              }

              return deParaFields.map((field, index) => (
                <tr key={`depara-${row.documento}-${index}`}>
                  <td data-head={t("From.To")}>
                    {(() => {
                      const paraIndex = field.toLowerCase().indexOf("para");
                      if (paraIndex !== -1) {
                        const before = field.slice(0, paraIndex + 4);
                        const after = field.slice(paraIndex + 4);
                        return (
                          <>
                            {before}
                            <b>{after}</b>
                          </>
                        );
                      }
                      return field;
                    })()}
                  </td>
                </tr>
              ));
            })()}
          </tbody>
        </TableInternal>
      </>
    );
  },
};
