import { t } from "i18next";
import { IOracle } from "../../../interfaces";
import { BoldHeader, TableInternal } from "../styles";
import { ReturnProps } from "../types";

export const TMS_4: ReturnProps<IOracle.FreightSurcharge> = {
  title: "TMS.Freight.Surcharge",
  origin: "ORACLE",
  type: "TMS_4",
  permission: "TMS",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "ID",
      selector: (row: IOracle.FreightSurcharge) => row.id,
    },
    {
      name: "Applicant",
      selector: (row: IOracle.FreightSurcharge) => row.createdBy,
    },
    {
      name: "ServiceProvider.ID",
      selector: (row: IOracle.FreightSurcharge) => row.servprovXid,
    },
    {
      name: "ServiceProvider",
      selector: (row: IOracle.FreightSurcharge) => row.servprovName,
    },
    {
      name: "City.Origin",
      selector: (row: IOracle.FreightSurcharge) => row.sourceCity,
    },
    {
      name: "Province.Origin",
      selector: (row: IOracle.FreightSurcharge) => row.sourceProvinceCode,
    },
    {
      name: "City.Destination",
      selector: (row: IOracle.FreightSurcharge) => row.destCity,
    },
    {
      name: "Province.Destination",
      selector: (row: IOracle.FreightSurcharge) => row.destProvinceCode,
    },
    {
      name: "Load",
      selector: (row: IOracle.FreightSurcharge) => row.shipmentXid,
    },
    {
      name: "Additional.Value.AF",
      selector: (row: IOracle.FreightSurcharge) => row.chargeAmount,
    },
  ],
  documentDetailHtml: (
    rows: IOracle.FreightSurcharge | IOracle.FreightSurcharge[]
  ) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    return (
      <TableInternal>
        <thead>
          <tr>
            <BoldHeader>{t("Applicant")}</BoldHeader>
            <BoldHeader>{t("ServiceProvider.ID")}</BoldHeader>
            <BoldHeader>{t("ServiceProvider")}</BoldHeader>
            <BoldHeader>{t("City.Origin")}</BoldHeader>
            <BoldHeader>{t("Province.Origin")}</BoldHeader>
            <BoldHeader>{t("City.Destination")}</BoldHeader>
            <BoldHeader>{t("Province.Destination")}</BoldHeader>
            <BoldHeader>{t("Load")}</BoldHeader>
            <BoldHeader>{t("Market.Type.TMS")}</BoldHeader>
            <BoldHeader>{t("Service.Type")}</BoldHeader>
            <BoldHeader>{t("Additional.Value.AF")}</BoldHeader>
            <BoldHeader>{t("Total.Actual.Cost")}</BoldHeader>
            <BoldHeader>{t("Date.Request")}</BoldHeader>
            <BoldHeader>{t("Reason")}</BoldHeader>
          </tr>
        </thead>
        <tbody>
          <tr key={row.id}>
            <td data-head={t("Applicant")} className="noCheckbox">
              {row?.createdBy || "-"}
            </td>
            <td data-head={t("ServiceProvider.ID")}>
              {row?.servprovXid || "-"}
            </td>
            <td data-head={t("ServiceProvider")}>{row?.servprovName || "-"}</td>
            <td data-head={t("City.Origin")}>{row?.sourceCity || "-"}</td>
            <td data-head={t("Province.Origin")}>
              {row?.sourceProvinceCode || "-"}
            </td>
            <td data-head={t("City.Destination")}>{row?.destCity || "-"}</td>
            <td data-head={t("Province.Destinationt")}>
              {row?.destProvinceCode || "-"}
            </td>
            <td data-head={t("Load")}>{row?.shipmentXid || "-"}</td>
            <td data-head={t("Market.Type.TMS")}>{row?.marketType || "-"}</td>
            <td data-head={t("Service.Type")}>{row?.serviceType || "-"}</td>
            <td data-head={t("Additional.Value.AF")}>
              {row?.chargeAmount || "-"}
            </td>
            <td data-head={t("Total.Actual.Cost")}>
              {row?.totalActualCost || "-"}
            </td>
            <td data-head={t("Date.Request")}>{row?.eventDate || "-"}</td>
            <td data-head={t("Reason")}>{row?.reason || "-"}</td>
          </tr>
        </tbody>
      </TableInternal>
    );
  },
};
