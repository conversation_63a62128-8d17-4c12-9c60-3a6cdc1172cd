import { ExpandMoreOutlined } from "@mui/icons-material";
import { t } from "i18next";
import React from "react";
import { IOracle } from "../../../interfaces";
import {
  BoldHeader,
  DivCollapse,
  Intbody,
  Intdcenter,
  TableInternal,
} from "../styles";
import { ReturnProps } from "../types";

export const TMS_5: ReturnProps<IOracle.CostBatch> = {
  title: "TMS.Cost.Batch",
  origin: "ORACLE",
  type: "TMS_5",
  permission: "TMS",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Registration.Type",
      selector: (row: IOracle.CostBatch) => "PROJETO",
    },
    {
      name: "Estimated.impact",
      selector: (row: IOracle.CostBatch) => row.rptAdjustment,
    },
    {
      name: "Attendant",
      selector: (row: IOracle.CostBatch) => row.attendant,
    },
  ],
  detailModalHeader: (row: IOracle.CostBatch | IOracle.CostBatch[]) => {
    const rowHeader = Array.isArray(row) ? (row[0] as IOracle.CostBatch) : row;
    const groupedItems = Array.isArray(rowHeader.rRCostBatchGroup?.items)
      ? rowHeader.rRCostBatchGroup.items
      : [];

    const callbackItemDropdown = (servprovXid: string) => {
      const collapseElement = document.getElementById(
        `collapse-${servprovXid}`
      );
      if (collapseElement) {
        collapseElement.classList.toggle("show");
      }
    };

    return (
      <tbody>
        <tr>
          <td>
            {groupedItems.length > 0 &&
              groupedItems.map((item) => {
                const itemDetails = Array.isArray(
                  item.rRCostBatchLinesCollection?.items
                )
                  ? item.rRCostBatchLinesCollection?.items || []
                  : [];

                return (
                  <React.Fragment
                    key={`group-${rowHeader.id}-${
                      item?.id || item?.servprovXid
                    }`}
                  >
                    <TableInternal>
                      <Intbody className="borderBottom">
                        <tr>
                          <td width="15%">
                            <div className="tableTitle">{`${t(
                              "Registration.Type"
                            )}:`}</div>
                            PROJETO
                          </td>
                          <td width="15%">
                            <div className="tableTitle">{`${t(
                              "ServiceProvider.ID"
                            )}:`}</div>
                            {item?.servprovXid || "-"}
                          </td>

                          <td width="55%">
                            <div className="tableTitle">{`${t(
                              "ServiceProvider"
                            )}:`}</div>
                            {item?.servprovName || "-"}
                          </td>

                          <td width="12%">
                            <div className="tableTitle">{`${t(
                              "Monthly.Impact"
                            )}:`}</div>
                            {item?.rptAdjustment || "-"}
                          </td>

                          <Intdcenter style={{ width: "3%" }}>
                            <div data-translation={t("Expand")}>
                              <ExpandMoreOutlined
                                id={`iconSpan-${item?.servprovXid}`}
                                fontSize="medium"
                                onClick={(e) => {
                                  callbackItemDropdown(item?.servprovXid);
                                  (
                                    e.currentTarget as SVGElement
                                  ).classList.toggle("show");
                                }}
                              />
                            </div>
                          </Intdcenter>
                        </tr>
                      </Intbody>
                    </TableInternal>
                    <DivCollapse id={`collapse-${item?.servprovXid}`}>
                      <TableInternal>
                        <thead>
                          <tr>
                            <BoldHeader>{t("City.Origin")}</BoldHeader>
                            <BoldHeader>{t("Province.Origin")}</BoldHeader>
                            <BoldHeader>{t("Country.Origin")}</BoldHeader>
                            <BoldHeader>{t("City.Destination")}</BoldHeader>
                            <BoldHeader>{t("Province.Destination")}</BoldHeader>
                            <BoldHeader>{t("Country.Destination")}</BoldHeader>
                            <BoldHeader>{t("Empty.Collection")}</BoldHeader>
                            <BoldHeader>{t("Base.Cost")}</BoldHeader>
                            <BoldHeader>{t("%.Project.TMS")}</BoldHeader>
                            <BoldHeader>{t("Final.Cost")}</BoldHeader>
                            <BoldHeader>{t("Service.Type")}</BoldHeader>
                            <BoldHeader>{t("Equipament.TMS")}</BoldHeader>
                            <BoldHeader>{t("Impact")}</BoldHeader>
                            <BoldHeader>{t("Effective.Date.TMS")}</BoldHeader>
                            <BoldHeader>{t("Expiration.Date.TMS")}</BoldHeader>
                          </tr>
                        </thead>
                        <tbody>
                          {itemDetails?.map(
                            (detail: IOracle.CostBatchDetail) => (
                              <tr key={`detail-${detail.id}`}>
                                <td data-head={t("City.Origin")}>
                                  {detail.sourceCity || "-"}
                                </td>
                                <td data-head={t("Province.Origin")}>
                                  {detail.sourceProvinceCode || "-"}
                                </td>
                                <td data-head={t("Country.Origin")}>
                                  {detail.sourceCountryCode3Gid || "-"}
                                </td>
                                <td data-head={t("City.Destination")}>
                                  {detail.destCity || "-"}
                                </td>
                                <td data-head={t("Province.Destination")}>
                                  {detail.destProvinceCode || "-"}
                                </td>
                                <td data-head={t("Country.Destination")}>
                                  {detail.destCountryCode3Gid || "-"}
                                </td>
                                <td data-head={t("Empty.Collection")}>
                                  {detail.lowValue4 || "-"}
                                </td>
                                <td data-head={t("Base.Cost")}>
                                  {detail.chargeAmount || "-"}
                                </td>
                                <td data-head={t("%.Project.TMS")}>
                                  {detail.appChargeMultiplierScalar || "-"}
                                </td>
                                <td data-head={t("Final.Cost")}>
                                  {detail.appCalculatedCost || "-"}
                                </td>
                                <td data-head={t("Service.Type")}>
                                  {detail.lowValue2 || "-"}
                                </td>
                                <td data-head={t("Equipament.TMS")}>
                                  {detail.lowValue3 || "-"}
                                </td>
                                <td data-head={t("Impact")}>
                                  {detail.rptAdjustment || "-"}
                                </td>
                                <td data-head={t("Effective.Date.TMS")}>
                                  {detail.appEffectiveDate || "-"}
                                </td>
                                <td data-head={t("Expiration.Date.TMS")}>
                                  {detail.appExpirationDate || "-"}
                                </td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </TableInternal>
                    </DivCollapse>
                  </React.Fragment>
                );
              })}
          </td>
        </tr>
      </tbody>
    );
  },
};
