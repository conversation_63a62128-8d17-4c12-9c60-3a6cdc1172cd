import { t } from "i18next";
import { IOracle } from "../../../interfaces";
import { ReturnProps } from "../types";

export const TMS_3: ReturnProps<IOracle.AccessorialCost> = {
  title: "FreightRateApprovals.TMS.Accessorial.Cost",
  origin: "ORACLE",
  type: "TMS_3",
  permission: "TMS",
  hasDetailModal: false,
  headerColumns: [
    {
      name: t("ID"),
      selector: (row: IOracle.AccessorialCost) => row?.id || "-",
    },
    {
      name: t("Description"),
      selector: (row: IOracle.AccessorialCost) =>
        row?.accessorialCodeXid || "-",
    },
    {
      name: t("ID.Cost"),
      selector: (row: IOracle.AccessorialCost) =>
        row?.accessorialCostXid || "-",
    },
    {
      name: t("Market.Type.TMS"),
      selector: (row: IOracle.AccessorialCost) => row?.lowValue2 || "-",
    },
    {
      name: t("Number.Of.Spots"),
      selector: (row: IOracle.AccessorialCost) => row?.lowValue3 || "-",
    },
    {
      name: t("Distance"),
      selector: (row: IOracle.AccessorialCost) => row?.lowValue4 || "-",
    },
    {
      name: t("Cost"),
      selector: (row: IOracle.AccessorialCost) => row?.chargeAmount || "-",
    },
    {
      name: t("Validity.Date.TMS"),
      selector: (row: IOracle.AccessorialCost) => row?.effectiveDate || "-",
    },
    {
      name: t("Expiration.Date.TMS"),
      selector: (row: IOracle.AccessorialCost) => row?.expirationDate || "-",
    },
  ],
};
