import { t } from "i18next";
import { IOracle } from "../../../interfaces";
import { RateFactorLineDetail } from "../../../interfaces/IOracle";
import { BoldHeader, TableInternal } from "../styles";
import { ReturnProps } from "../types";

const defaultDetail: RateFactorLineDetail = {
  cost: "-",
  totalAdjustment: "-",
  adjustment: "-",
  percent: "-",
  rateFactorLinesId: "-",
  referenceDate: "-",
  estimatedAnnualValue: "-",
};

export const TMS_1: ReturnProps<IOracle.RateFactor> = {
  title: "Freight.Rate.Approvals",
  origin: "ORACLE",
  type: "TMS_1",
  permission: "TMS",
  approveItems: true,
  hasDetailModal: false,
  headerColumns: [
    {
      name: "ID",
      selector: (row: IOracle.RateFactor) => row.id,
    },
    {
      name: "Evaluate.Date",
      selector: (row: IOracle.RateFactor) => row.effectiveDate,
    },
  ],
  documentDetailHtml: (rows: IOracle.RateFactor | IOracle.RateFactor[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    return (
      <TableInternal>
        <thead>
          <tr>
            <BoldHeader>{t("Update.Type")}</BoldHeader>
            <BoldHeader>{t("Service")}</BoldHeader>
            <BoldHeader>{t("update")}</BoldHeader>
            <BoldHeader>{t("Referer.Month")}</BoldHeader>
            <BoldHeader>{t("Month.Expend")}</BoldHeader>
            <BoldHeader>{t("Estimated.Month.Expend")}</BoldHeader>
            <BoldHeader>{t("Estimated.impact")}</BoldHeader>
            <BoldHeader>{t("Estimated.Annual.Impact")}</BoldHeader>
          </tr>
        </thead>
        <tbody>
          {row.rateFactorLines?.map((line) => {
            const details = line?.rateFactorLineDetailsCollection ?? [
              defaultDetail,
            ];

            return details?.map((detail) => (
              <tr key={`${row.id}-${line?.serviceTypeName}-${detail?.id}`}>
                <td data-head={t("Update.Type")} className="noCheckbox">
                  DIESEL
                </td>
                <td data-head={t("Service")}>{line?.serviceTypeName || "-"}</td>
                <td data-head={t("update")}>{line?.value || "-"}</td>
                <td data-head={t("Referer.Month")}>
                  {detail?.referenceDate || "-"}
                </td>
                <td data-head={t("Month.Expend")}>{detail?.cost || "-"}</td>
                <td data-head={t("Estimated.Month.Expend")}>
                  {detail?.totalAdjustment || "-"}
                </td>
                <td data-head={t("Estimated.impact")}>
                  {detail?.adjustment || "-"}
                </td>
                <td data-head={t("Estimated.Annual.Impact")}>
                  {detail?.estimatedAnnualValue || "-"}
                </td>
              </tr>
            ));
          })}
        </tbody>
      </TableInternal>
    );
  },
};
