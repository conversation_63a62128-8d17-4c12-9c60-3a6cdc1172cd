import { t } from "i18next";
import React from "react";
import {
  RateRecord,
  RateRecordCostItem,
  RateRecordGroupItem,
} from "../../../interfaces/IOracle";
import { TableInternal } from "../styles";
import { ReturnProps } from "../types";

export const TMS_2: ReturnProps<RateRecord> = {
  title: "FreightRateApprovals.TMS.Base.Project",
  origin: "ORACLE",
  type: "TMS_2",
  permission: "TMS",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "ID",
      selector: (row: RateRecord) => row.id || "-",
    },
    {
      name: "Rate.Type",
      selector: (row: RateRecord) => row.rateType || "-",
    },
    {
      name: "Rate.ID",
      selector: (row: RateRecord) => row.rateGeoXid || "-",
    },
    {
      name: "City.Origin",
      selector: (row: RateRecord) => row.sCity || "-",
    },
    {
      name: "Province.Origin",
      selector: (row: RateRecord) => row.sProvince || "-",
    },
    {
      name: "Country.Origin",
      selector: (row: RateRecord) => row.sCountry || "-",
    },
    {
      name: "City.Destination",
      selector: (row: RateRecord) => row.dCity || "-",
    },
    {
      name: "Province.Destination",
      selector: (row: RateRecord) => row.dProvince || "-",
    },
    {
      name: "Country.Destination",
      selector: (row: RateRecord) => row.dCountry || "-",
    },
    {
      name: "ServiceProvider.ID",
      selector: (row: RateRecord) => row.serviceProviderXid || "-",
    },
    {
      name: "ServiceProvider",
      selector: (row: RateRecord) => row.serviceProviderName || "-",
    },
  ],
  documentDetailHtml: (rows: RateRecord | RateRecord[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const filteredGroups = row?.rateRecordGroupsCollection?.items?.filter(
      (group: RateRecordGroupItem) =>
        group?.rateRecordCostsCollection?.items?.some(
          (cost) => cost?.operation === "CREATE" || cost?.operation === "UPDATE"
        )
    );

    const filteredGroupsAccNewCollection =
      row?.rateRecordAccNewCollection?.items?.filter(
        (cost: RateRecordCostItem) =>
          cost?.operation === "CREATE" || cost?.operation === "UPDATE"
      );

    const filteredGroupsAccessorialsCollection =
      row?.rateRecordAccessorialsCollection?.items?.filter(
        (cost: RateRecordCostItem) =>
          cost?.operation === "CREATE" || cost?.operation === "UPDATE"
      );
    return (
      <TableInternal>
        {filteredGroups?.map((line: RateRecordGroupItem) => {
          const detailsCostsCollection =
            line?.rateRecordCostsCollection?.items?.filter(
              (detail: RateRecordCostItem) =>
                detail?.operation === "CREATE" || detail?.operation === "UPDATE"
            );

          if (detailsCostsCollection?.length > 0) {
            return (
              <React.Fragment key={line.id}>
                <thead>
                  <tr>
                    <td>{t("Service.Type")}</td>
                    <td>{t("Equipment")}</td>
                    <td>{t("Rate.Value.ABS")}</td>
                    <td>{t("Rate.Value.Percent")}</td>
                    <td>{t("Validity.Date")}</td>
                    <td>{t("Expiration.Date.TMS")}</td>
                    <td>{t("Base.Cost")}</td>
                    <td>{t("Final.Cost")}</td>
                  </tr>
                </thead>
                <tbody>
                  {detailsCostsCollection.map((detail: RateRecordCostItem) => (
                    <tr key={`${line.id}-${detail?.id}`}>
                      <td data-head={t("Service.Type")} className="noCheckbox">
                        {detail?.lowValue2 || "-"}
                      </td>
                      <td data-head={t("Equipment")}>
                        {detail?.lowValue3 || "-"}
                      </td>
                      <td data-head={t("Rate.Value.ABS")}>
                        {detail?.chargeAmount || "-"}
                      </td>
                      <td data-head={t("Rate.Value.Percent")}>
                        {detail?.chargeMultiplierScalar || "-"}
                      </td>
                      <td data-head={t("Validity.Date")}>
                        {detail?.effectiveDate || "-"}
                      </td>
                      <td data-head={t("Expiration.Date.TMS")}>
                        {detail?.expirationDate || "-"}
                      </td>
                      <td data-head={t("Base.Cost")}>
                        {detail?.baseCost || "-"}
                      </td>
                      <td data-head={t("Final.Cost")}>
                        {detail?.calculatedValue || "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </React.Fragment>
            );
          }
          return null;
        })}

        {filteredGroupsAccNewCollection?.length > 0 && (
          <>
            <thead>
              <tr>
                <td>{t("Rate.Type.TMS")}</td>
                <td>{t("Rate.ID.TMS")}</td>
                <td>{t("Market.Type.TMS")}</td>
                <td>{t("Number.Of.Spots")}</td>
                <td>{t("Distance")}</td>
                <td>{t("Cost")}</td>
                <td>{t("Effective.Date.TMS")}</td>
                <td>{t("Expiration.Date.TMS")}</td>
              </tr>
            </thead>
            <tbody>
              {filteredGroupsAccNewCollection?.map(
                (detail: RateRecordCostItem) => (
                  <tr key={`${detail?.id}-${detail?.accessorialCostXid}`}>
                    <td data-head={t("Rate.Type.TMS")} className="noCheckbox">
                      {detail?.accessorialCodeXid || "-"}
                    </td>
                    <td data-head={t("Rate.ID.TMS")}>
                      {detail?.accessorialCostXid || "-"}
                    </td>
                    <td data-head={t("Market.Type.TMS")}>
                      {detail?.lowValue2 || "-"}
                    </td>
                    <td data-head={t("Number.Of.Spots")}>
                      {detail?.lowValue3 || "-"}
                    </td>
                    <td data-head={t("Distance")}>
                      {detail?.lowValue4 || "-"}
                    </td>
                    <td data-head={t("Cost")}>{detail?.chargeAmount || "-"}</td>
                    <td data-head={t("Effective.Date.TMS")}>
                      {detail?.effectiveDate || "-"}
                    </td>
                    <td data-head={t("Expiration.Date.TMS")}>
                      {detail?.expirationDate || "-"}
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </>
        )}

        {filteredGroupsAccessorialsCollection?.length > 0 && (
          <>
            <thead>
              <tr>
                <td colSpan={1}>{t("Rate.Type.TMS")}</td>
                <td colSpan={2}>{t("Rate.ID.TMS")}</td>
                <td colSpan={2}>{t("Cost")}</td>
                <td colSpan={3}>{t("Rate.Value.Percent")}</td>
              </tr>
            </thead>
            <tbody>
              {filteredGroupsAccessorialsCollection?.map(
                (detail: RateRecordCostItem) => (
                  <tr key={`${detail?.id}-${detail?.accessorialCostXid}`}>
                    <td
                      colSpan={1}
                      data-head={t("Rate.Type.TMS")}
                      className="noCheckbox"
                    >
                      {detail?.accessorialCode || "-"}
                    </td>
                    <td colSpan={2} data-head={t("Rate.ID.TMS")}>
                      {detail?.accessorialCost || "-"}
                    </td>

                    <td colSpan={2} data-head={t("Cost")}>
                      {detail?.chargeAmount || "-"}
                    </td>
                    <td colSpan={3} data-head={t("Rate.Value.Percent")}>
                      {detail?.chargeMultiplierScalar || "-"}
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </>
        )}
      </TableInternal>
    );
  },
};
