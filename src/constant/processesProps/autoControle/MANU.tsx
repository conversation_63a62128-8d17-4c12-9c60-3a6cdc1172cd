import { dateBDtoReadWithSeparator } from "@/utils/Date";
import { ISeoDigital } from "../../../interfaces";
import { ReturnProps } from "../types";

export const MANU: ReturnProps<ISeoDigital.ISeoDigitalBase> = {
  title: "Manu.Portal.Actions",
  origin: "SEODIGITAL",
  type: "MANU",
  permission: "MANU",
  headerColumns: [
    {
      name: "What",
      selector: (row: ISeoDigital.ISeoDigitalBase) => row.what,
    },
    {
      name: "Responsible",
      selector: (row: ISeoDigital.ISeoDigitalBase) => row.responsibleName,
    },
    {
      name: "Date",
      selector: (row: ISeoDigital.ISeoDigitalBase) =>
        dateBDtoReadWithSeparator(row.created),
    },
  ],
  detailColumns: [
    {
      name: "Cause",
      selector: (row: ISeoDigital.ISeoDigitalBase) => row.subElement,
    },
    {
      name: "How",
      selector: (row: ISeoDigital.ISeoDigitalBase) => row.how,
    },
    {
      name: "Investment",
      selector: (row: ISeoDigital.ISeoDigitalBase) => row.investment,
      format: (row: ISeoDigital.ISeoDigitalBase) => {
        return new Intl.NumberFormat("pt-BR", {
          style: "currency",
          currency: "BRL",
        }).format(Number(row.investment));
      },
    },
  ],
};
