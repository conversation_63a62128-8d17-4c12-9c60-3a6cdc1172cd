import { AribaService } from "@/services";
import { t } from "i18next";
import { ReturnProps } from "..";
import { IAriba } from "../../../interfaces";
import {
  formatToBRLFloatAmount,
  FullCurrencyAndValueSeparete,
} from "../../../utils/Currency";
import { TableInternal } from "../styles";
import { ApprovalHistory } from "./components/ApprovalHistory";

export const AP: ReturnProps<IAriba.ItemPropsAP> = {
  title: "Ariba.Sourcing.Projects",
  origin: "ARIBA",
  type: "AP",
  permission: "AP",
  hasDetailModal: false,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "Requester",
      selector: (row: IAriba.ItemPropsAP) => row.ownerName || "-",
    },
    {
      name: "Event",
      selector: (row: IAriba.ItemPropsAP) => row.id || "-",
    },
    {
      name: "Subcategory",
      selector: (row: IAriba.ItemPropsAP) => row.subcategoryNegotiation || "-",
    },
    {
      name: "Baseline.Value",
      selector: (row: IAriba.ItemPropsAP) => row.contractAmount || "-",
    },
    {
      name: "Contract.Value",
      selector: (row: IAriba.ItemPropsAP) => {
        const value = FullCurrencyAndValueSeparete(row.closedNegotiation, 1);
        return formatToBRLFloatAmount(value) || "-";
      },
    },
    {
      name: "Currency",
      selector: (row: IAriba.ItemPropsAP) =>
        FullCurrencyAndValueSeparete(row.closedNegotiation, 2) || "-",
    },
  ],
  documentDetailHtml: (rows: IAriba.ItemPropsAP | IAriba.ItemPropsAP[]) => {
    // passar tasks
    const row = Array.isArray(rows) ? (rows[0] as IAriba.ItemPropsAP) : rows;
    const taskDescription = row?.task;
    const sortedApprovalRequests = AribaService.sortApprovalRequests(
      row?.approvalRequests
    );

    return (
      <TableInternal>
        <thead>
          <tr>
            <td>{t("Project.Code")}</td>
            <td>{t("Name")}</td>
            <td>{t("Approval.Type")}</td>
          </tr>
        </thead>
        <tbody>
          <tr key={`tr2-${row.task}`}>
            <td data-head={t("Project.Code")} className="noCheckbox">
              {row.id || "-"}
            </td>
            <td data-head={t("Name")}>{row.projectName || "-"}</td>
            <td data-head={t("Approval.Type")}>{taskDescription}</td>
          </tr>
        </tbody>

        <thead>
          <tr>
            <td>{t("Fluxo de Aprovações da Tarefa")}</td>
          </tr>
        </thead>
        <tbody>
          <tr key={`tr2-${row.task}`}>
            <td colSpan={3} data-head={t("Fluxo de Aprovações da Tarefa")}>
              {!!sortedApprovalRequests.length ? (
                <ApprovalHistory approvalRequests={sortedApprovalRequests} />
              ) : (
                "-"
              )}
            </td>
          </tr>
        </tbody>
      </TableInternal>
    );
  },
};
