import { AribaService } from "@/services";
import { t } from "i18next";
import React from "react";
import { ReturnProps } from "..";
import { IAriba } from "../../../interfaces";
import { TableInternal } from "../styles";
import { ApprovalHistory } from "./components/ApprovalHistory";

export const AS: ReturnProps<IAriba.ItemPropsAS> = {
  title: "Ariba.Sourcing.Request",
  origin: "ARIBA",
  type: "AS",
  permission: "AS",
  hasDetailModal: false,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "Code",
      selector: (row: IAriba.ItemPropsAS) => row.id || "-",
    },
    {
      name: "Contract.Manager",
      selector: (row: IAriba.ItemPropsAS) => row.contractManager || "-",
    },
    {
      name: "Name.Description.AS",
      selector: (row: IAriba.ItemPropsAS) => row.projectName || "-",
    },
    {
      name: "Demand.type",
      selector: (row: IAriba.ItemPropsAS) => row.demandType || "-",
    },
    {
      name: "Solicitation.Degree",
      selector: (row: IAriba.ItemPropsAS) => {
        const customField = row.workspaceDetails?.customFields?.find(
          (item: any) => item?.fieldName === "cus_GraudaSolicitcao"
        );
        return customField?.fieldValue || "-";
      },
    },
    {
      name: "AS.Provider.type",
      selector: (row: IAriba.ItemPropsAS) => row.supplierType || "-",
    },
    {
      name: "Amount.Requested.AS",
      selector: (row: IAriba.ItemPropsAS) =>
        row.workspaceDetails?.baselineSpend || "-",
    },
  ],
  documentDetailHtml: (rows: IAriba.ItemPropsAS | IAriba.ItemPropsAS[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IAriba.ItemPropsAS) : rows;
    const taskDescription = row?.task || "-";
    const sortedApprovalRequests = AribaService.sortApprovalRequests(
      row?.approvalRequests || []
    );

    return (
      <TableInternal>
        <React.Fragment key={`item-${row?.task}`}>
          <thead>
            <tr>
              <td>{t("Description")}</td>
              <td>{t("Approval.Type")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={`tr2-arriba-sourcing-${row?.task}`}>
              <td data-head={t("Description")} className="noCheckbox">
                {row?.workspaceDetails?.description || "-"}
              </td>
              <td data-head={t("Approval.Type")}>{taskDescription}</td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td>{t("Task.Approval.Flow")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={`tr2-arriba-sourcing-${row?.task}`}>
              <td colSpan={2} data-head={t("Task.Approval.Flow")}>
                {!!sortedApprovalRequests.length ? (
                  <ApprovalHistory approvalRequests={sortedApprovalRequests} />
                ) : (
                  "-"
                )}
              </td>
            </tr>
          </tbody>
        </React.Fragment>
      </TableInternal>
    );
  },
};
