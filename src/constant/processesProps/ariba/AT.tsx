import { AribaService } from "@/services";
import { AttachFile } from "@mui/icons-material";
import { t } from "i18next";
import { useState } from "react";
import { ReturnProps } from "..";
import { APIAriba } from "../../../api";
import Loading from "../../../components/Loading";
import { IAriba } from "../../../interfaces";
import { dateBDtoReadWithSeparator } from "../../../utils/Date";
import { TableInternal } from "../styles";
import { ApprovalHistory } from "./components/ApprovalHistory";

export const AT: ReturnProps<IAriba.ItemPropsAT> = {
  title: "Ariba.Contract",
  origin: "ARIBA",
  type: "AT",
  permission: "AT",
  hasDetailModal: false,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "Project.Code",
      selector: (row: IAriba.ItemPropsAT) => row.id || "-",
    },
    {
      name: "Title",
      selector: (row: IAriba.ItemPropsAT) => row.projectName || "-",
    },
    {
      name: "Requester",
      selector: (row: IAriba.ItemPropsAT) => row.ownerName || "-",
    },
    {
      name: "Task.Start.Date.AT",
      selector: (row: IAriba.ItemPropsAT) =>
        dateBDtoReadWithSeparator(row.beginDate) || "-",
    },
    {
      name: "Contract.Effective.Date.AT",
      selector: (row: IAriba.ItemPropsAT) =>
        dateBDtoReadWithSeparator(row.effectiveDate) || "-",
    },
    {
      name: "Value",
      selector: (row: IAriba.ItemPropsAT) => {
        return row.contractAmount
          ? new Intl.NumberFormat("pt-BR", {
              currency: "BRL",
              minimumFractionDigits: 2,
            }).format(Number(row.contractAmount))
          : "-";
      },
    },
    {
      name: "Currency",
      selector: (row: IAriba.ItemPropsAT) => row.contractCurrency || "-",
    },
  ],
  documentDetailHtml: (rows: IAriba.ItemPropsAT | IAriba.ItemPropsAT[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IAriba.ItemPropsAT) : rows;
    const [isDownloading, setIsDownloading] = useState(false);

    const taskDescription = row?.description;
    const sortedApprovalRequests = AribaService.sortApprovalRequests(
      row?.approvalRequests
    );

    const handleDownloadAttatchment = async (item: IAriba.ItemPropsAT) => {
      const params = {
        entity_id: item?.entityId,
        entity_type: item?.entityType,
        attachment_id: item?.attachmentId,
      };

      try {
        setIsDownloading(true);
        const { data, success } = await APIAriba.downloadAttachment(params);

        if (success && data) {
          // Handle the response based on what we receive
          let blob;
          if (data instanceof Blob) {
            // Use the blob directly if it's already a Blob
            blob = data;
          } else if (data instanceof ArrayBuffer) {
            // Convert ArrayBuffer to Blob
            blob = new Blob([data]);
          } else if (typeof data === "object" && data !== null) {
            // If we got JSON or another object instead of binary data
            const jsonStr = JSON.stringify(data);
            blob = new Blob([jsonStr], { type: "application/json" });
          } else {
            // For text or other formats
            blob = new Blob([data], {
              type: "application/octet-stream",
            });
          }

          // Check if blob has content
          if (blob.size > 0) {
            const urlBlob = window.URL.createObjectURL(blob);

            const link = document.createElement("a");
            link.href = urlBlob;
            link.download = item?.title || "download";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(urlBlob);
          } else {
            console.error("Downloaded file has no content");
          }
        }
      } catch (error) {
        console.error("Error downloading attachment:", error);
      } finally {
        setIsDownloading(false);
      }
    };
    return (
      <>
        {isDownloading && <Loading open={isDownloading} />}
        <TableInternal>
          <thead>
            <tr>
              <td>{t("Summarized.Description")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={`desc-${row?.task}`}>
              <td data-head={t("Summarized.Description")} colSpan={9}>
                {row?.cusCmDetalhesAditivo || "-"}
              </td>
            </tr>
          </tbody>

          <thead>
            <tr>
              <td>{t("Contracted.company")}</td>
              <td>{t("Hirer")}</td>
              <td>{t("Approval.Type")}</td>
              {taskDescription?.includes(
                "C5 - Visualização Risk Assessment"
              ) && <td>{t("Attachment")}</td>}
            </tr>
          </thead>
          <tbody>
            <tr key={`details-${row?.task}`}>
              <td data-head={t("Contracted.company")} className="noCheckbox">
                {row?.supplierName || "-"}
              </td>
              <td data-head={t("Hirer")}>{row?.contractor || "-"}</td>
              <td data-head={t("Approval.Type")}>{taskDescription}</td>
              {taskDescription?.includes(
                "C5 - Visualização Risk Assessment"
              ) && (
                <td data-head={t("Attachment")}>
                  {row?.attachmentId ? (
                    <AttachFile
                      fontSize="small"
                      style={{
                        color: "#004BA0",
                        cursor: "pointer",
                      }}
                      onClick={() => handleDownloadAttatchment(row)}
                    />
                  ) : (
                    "-"
                  )}
                </td>
              )}
            </tr>
          </tbody>

          <thead>
            <tr>
              <td>{t("Fluxo de Aprovações da Tarefa")}</td>
            </tr>
          </thead>
          <tbody>
            <tr key={`approval-${row?.task}`}>
              <td colSpan={3} data-head={t("Fluxo de Aprovações da Tarefa")}>
                {!!sortedApprovalRequests.length ? (
                  <ApprovalHistory approvalRequests={sortedApprovalRequests} />
                ) : (
                  "-"
                )}
              </td>
            </tr>
          </tbody>
        </TableInternal>
      </>
    );
  },
};
