/* eslint-disable react/jsx-one-expression-per-line */
import React from "react";
import { useTranslation } from "react-i18next";
import { IAriba } from "../../../../../interfaces";
import { BoxStatus, Container } from "./styles";

const handleStatusColor = (state: number) => {
  switch (state) {
    case 1: //Pending
      return "#E1E1E1";
    case 2: //Ready for Approval
      return "#2BA4F2";
    case 4: //Denied
      return "#ED7575";
    case 8: //Approved
      return "#99C717";
    default:
      return "#E1E1E1";
  }
};

interface ApprovalHistoryProps {
  approvalRequests: IAriba.ApprovalRequest[];
}

export const ApprovalHistory: React.FC<ApprovalHistoryProps> = ({
  approvalRequests,
}) => {
  const { t } = useTranslation();

  const StatusLine: React.FC = () => (
    <div
      className="statusLine"
      style={{ width: "64px", border: "1px solid rgba(0, 0, 0, 0.2)" }}
    />
  );

  return (
    <Container>
      {approvalRequests.map((step: IAriba.ApprovalRequest, index: number) => {
        return step.approvers.map(
          (approvers: IAriba.ApprovalRequest["approvers"][number]) => {
            return (
              <div
                key={approvers.group?.uniqueName || approvers.user?.uniqueName}
              >
                <BoxStatus
                  $colorStatus={handleStatusColor(step.state)}
                  $index={index}
                  $length={approvalRequests.length}
                >
                  <div>{t(step.approvalState)}</div>
                  <div>
                    {approvers?.group?.name || approvers.user?.name || "-"}
                    {step.approvalState === "Approved" &&
                      step.approvedBy &&
                      step.approvedBy.user && (
                        <div style={{ fontSize: "12px" }}>
                          ({step.approvedBy.user.name})
                        </div>
                      )}
                  </div>
                </BoxStatus>
                {index !== approvalRequests.length - 1 && <StatusLine />}
              </div>
            );
          }
        );
      })}
    </Container>
  );
};
