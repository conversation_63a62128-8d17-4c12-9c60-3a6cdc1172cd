import {
  <PERSON><PERSON>,
  ISubTotalPorDespesas,
  ISubTotalPorDias,
} from "@/interfaces/IAptus";
import { AptusService } from "@/services/aptus";
import { dateBDtoRead } from "@/utils/Date";
import {
  ExpandMoreOutlined,
  NotificationsOutlined,
  WarningAmberOutlined,
} from "@mui/icons-material";
import { t } from "i18next";
import { IAptus } from "../../../interfaces";
import {
  DivCollapse,
  DivTotalValue,
  Intbody,
  Intdcenter,
  Intdleft,
  Inthead,
  Inttitle,
  TableInternal,
} from "../styles";
import { ReturnProps } from "../types";

export const DR: ReturnProps<IAptus.IAptusBaseDocument> = {
  title: "Aptus.03.Route.Expense",
  origin: "APTUS",
  type: "DR",
  permission: "AD",
  approveItems: true,
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IAptus.IAptusBaseDocument) => row.Codigo || "-",
    },
    {
      name: "Favored",
      selector: (row: IAptus.IAptusBaseDocument) => row.Favorecido || "-",
    },
    {
      name: "Value",
      selector: (row: IAptus.IAptusBaseDocument) => row.Valor || "-",
    },
    {
      name: "Trip",
      cell: (row: IAptus.IAptusBaseDocument) => {
        if (row.DataInicioPeriodoViagem && row.DataFimPeriodoViagem) {
          return (
            <td data-head={t("Trip")}>
              {`${dateBDtoRead(row.DataInicioPeriodoViagem)} ${t(
                "Until"
              )} ${dateBDtoRead(row.DataFimPeriodoViagem)}`}
            </td>
          );
        }
        return "-";
      },
    },
    {
      name: "Lighthouse",
      cell: (row: IAptus.IAptusBaseDocument) =>
        row.Critica === "SIM" ? (
          <NotificationsOutlined
            sx={{ color: "#E32027" }}
            fontSize="medium"
            titleAccess={row.CriticaMotivo}
          />
        ) : (
          "-"
        ),
    },
    {
      name: "Anomaly",
      cell: (row: IAptus.IAptusBaseDocument) => {
        return row?.Itens ? AptusService.handleAnomalyColumn(row?.Itens) : "-";
      },
    },
  ],
  detailModalHeader: (
    rows: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(rows)
      ? (rows[0] as IAptus.IAptusBaseDocument)
      : rows;

    const arrDiasDespesas = AptusService.handleArrayDespesasDia(row);

    const callbackItemDropdown = (codigo: string) => {
      const collapseElement = document.getElementById(`collapse-${codigo}`);
      if (collapseElement) {
        collapseElement.classList.toggle("show");
      }
    };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {row.Codigo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Requester.ID")}:`}</div>
            {`${row.Itens[0].SolicitanteID} - ${row.Itens[0].Solicitante}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}:`}</div>
            {`${row.Itens[0].FavorecidoCPF} - ${row.Itens[0].Favorecido}`}
          </td>
          <td>
            <div className="tableTitle">{`${t(
              "Job.Position.Beneficiary"
            )}:`}</div>
            {row.Cargo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Area.Beneficiary")}:`}</div>
            {row.AreaRH}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {`${row.Itens[0].Empresa} - ${row.Itens[0].EmpresaNome}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Departure.Date")}:`}</div>
            {row.Itens[0].DataInicioPeriodoViagem
              ? dateBDtoRead(row.Itens[0].DataInicioPeriodoViagem)
              : "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Return.Date")}:`}</div>
            {row.Itens[0].DataFimPeriodoViagem
              ? dateBDtoRead(row.Itens[0].DataFimPeriodoViagem)
              : "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {row.Itens[0].CidadeOrigem}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination")}:`}</div>
            {row.Itens[0].CidadeDestino}
          </td>
          <td>
            <div className="tableTitle">{`${t("CC.Description")}:`}</div>
            {row.Itens[0].CentroCustoDescricao}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Has.advance.payment")}:`}</div>
            {row.Itens[0].PossuiAdiantamento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Down.payment.number")}:`}</div>
            {row.Itens[0].NumeroAdiantamento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Advance.amount")}:`}</div>
            {row.Itens[0].ValorAdiantamento}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <Inttitle>{`${t("Expenses")}:`}</Inttitle>
            <div style={{ paddingRight: "20px", paddingLeft: "20px" }}>
              {Array.isArray(arrDiasDespesas) &&
                arrDiasDespesas.map((dias: ISubTotalPorDias, index: number) => {
                  return (
                    <>
                      <Inttitle className="paddingLeftTable">
                        <span>{dateBDtoRead(dias.Data)}</span>
                        <span>{`SUBTOTAL ${dias.Valor}`}</span>
                      </Inttitle>
                      <TableInternal className="noMargin">
                        <Inthead>
                          <tr className="tableAptus">
                            <th className="paddingLeftTable">{`${t(
                              "Tipo de Despesa"
                            )}`}</th>
                            <th>{`${t("C.Accountancy")}`}</th>
                            <th>{`${t("Total")}`}</th>
                            <th>{`${t("Order")}`}</th>
                            <th>{`${t("CC")}`}</th>
                            <th>{`${t("Lighthouse")}`}</th>
                            <th>{`${t("Anomaly")}`}</th>
                          </tr>
                        </Inthead>
                        {dias.SubTotalPorDespesas.map(
                          (despesa: ISubTotalPorDespesas) => {
                            return (
                              <>
                                <Intbody
                                  id={`bodyItem-${row.Codigo}`}
                                  className="borderBottom"
                                >
                                  <tr className="tableAptus">
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("Tipo de Despesa")}
                                      className="despDescricao"
                                    >
                                      <div className="paddingLeftTable">
                                        {despesa.DespesaDescricao}
                                      </div>
                                    </Intdleft>
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("C.Accountancy")}
                                    >
                                      <div>
                                        {AptusService.handleCAccountancy(
                                          despesa.Itens
                                        )}
                                      </div>
                                    </Intdleft>
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("Total")}
                                    >
                                      <div>{despesa.Valor}</div>
                                    </Intdleft>
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("Order")}
                                    >
                                      <div>
                                        {AptusService.handleOrder(
                                          despesa.Itens
                                        )}
                                      </div>
                                    </Intdleft>
                                    <Intdleft colSpan={1} data-head={t("CC")}>
                                      <div>
                                        {AptusService.handleCC(despesa.Itens)}
                                      </div>
                                    </Intdleft>
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("Lighthouse")}
                                    >
                                      <div>
                                        {AptusService.handleLighthouseColumn(
                                          despesa.Itens
                                        )}
                                      </div>
                                    </Intdleft>
                                    <Intdleft
                                      colSpan={1}
                                      data-head={t("Anomaly")}
                                    >
                                      <div>
                                        {despesa.Itens
                                          ? AptusService.handleAnomalyColumn(
                                              despesa.Itens
                                            )
                                          : "-"}
                                      </div>
                                    </Intdleft>
                                    <Intdcenter
                                      colSpan={1}
                                      className="despDescricao"
                                    >
                                      <div data-translation={t("Expand")}>
                                        <ExpandMoreOutlined
                                          id={`iconExpand-${row.Codigo}`}
                                          fontSize="medium"
                                          onClick={(e) => {
                                            callbackItemDropdown(
                                              `${row.Codigo}-${index}`
                                            );
                                            e.currentTarget.classList.toggle(
                                              "show"
                                            );
                                          }}
                                        />
                                      </div>
                                    </Intdcenter>
                                  </tr>
                                  <tr
                                    className="tableAptus"
                                    id={`tr2-${row.Codigo}`}
                                  >
                                    <td colSpan={8}>
                                      <DivCollapse
                                        id={`collapse-${row.Codigo}-${index}`}
                                        className="paddingNone"
                                      >
                                        <TableInternal className="noMargin">
                                          <Inthead>
                                            <tr className="tableAptus">
                                              <th
                                                className="paddingLeftTable"
                                                colSpan={1}
                                              >{`${t("Value")}`}</th>
                                              <th colSpan={3}>{`${t(
                                                "Comment"
                                              )}`}</th>
                                              <th
                                                className="thCenter"
                                                colSpan={1}
                                              >{`${t("Participants")}`}</th>
                                              <th
                                                className="thCenter"
                                                colSpan={1}
                                              >{`${t("Lighthouse")}`}</th>
                                              <th
                                                className="thCenter"
                                                colSpan={1}
                                              >{`${t("Anomaly")}`}</th>
                                              <th
                                                className="thCenter"
                                                colSpan={1}
                                              >{`${t("Anexo")}`}</th>
                                            </tr>
                                          </Inthead>
                                          <Intbody>
                                            {despesa.Itens.map(
                                              (item: IItens) => {
                                                return (
                                                  <tr className="tableAptus">
                                                    <Intdleft
                                                      colSpan={1}
                                                      data-head={t("Value")}
                                                      className="noCheckbox paddingLeftTable"
                                                    >
                                                      <div>
                                                        {item.ValorTotal}
                                                      </div>
                                                    </Intdleft>
                                                    <Intdleft
                                                      colSpan={3}
                                                      data-head={t("Comment")}
                                                    >
                                                      <div>
                                                        {item.Comentarios}
                                                      </div>
                                                    </Intdleft>
                                                    {AptusService.handleParticipantsColumn(
                                                      parseInt(
                                                        item.QuantidadeParticipantes ||
                                                          "0"
                                                      ),
                                                      item.Participantes ?? "",
                                                      t
                                                    )}
                                                    <Intdcenter
                                                      colSpan={1}
                                                      data-head={t(
                                                        "Lighthouse"
                                                      )}
                                                    >
                                                      <div>
                                                        {item.Critica ===
                                                        "SIM" ? (
                                                          <NotificationsOutlined
                                                            fontSize="medium"
                                                            color="error"
                                                            titleAccess={
                                                              item.CriticaMotivo
                                                            }
                                                          />
                                                        ) : (
                                                          "-"
                                                        )}
                                                      </div>
                                                    </Intdcenter>
                                                    <Intdcenter
                                                      colSpan={1}
                                                      data-head={t("Anomaly")}
                                                    >
                                                      <div>
                                                        {item.Anomalia ? (
                                                          <WarningAmberOutlined
                                                            fontSize="medium"
                                                            color="error"
                                                            titleAccess={
                                                              item.Anomalia
                                                            }
                                                          />
                                                        ) : (
                                                          "-"
                                                        )}
                                                      </div>
                                                    </Intdcenter>
                                                    <Intdcenter
                                                      colSpan={1}
                                                      data-head={t(
                                                        "Attachment"
                                                      )}
                                                    >
                                                      <div>
                                                        {AptusService.handleDownloadAttachment(
                                                          item.Objetivo ?? ""
                                                        )}
                                                      </div>
                                                    </Intdcenter>
                                                  </tr>
                                                );
                                              }
                                            )}
                                          </Intbody>
                                        </TableInternal>
                                      </DivCollapse>
                                    </td>
                                  </tr>
                                </Intbody>
                              </>
                            );
                          }
                        )}
                      </TableInternal>
                    </>
                  );
                })}
              <DivTotalValue>
                <Inttitle className="paddingLeftTable">
                  {`${t("Total.Value")}: ${row.Valor}`}
                </Inttitle>
              </DivTotalValue>
            </div>
          </td>
        </tr>
      </tbody>
    );
  },
};
