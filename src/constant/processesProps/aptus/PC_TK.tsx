import {
  Notifications,
  NotificationsOutlined,
  Warning,
} from "@mui/icons-material";
import { t } from "i18next";
import { IAptus } from "../../../interfaces";
import { AptusService } from "../../../services/aptus";
import { dateBDtoRead } from "../../../utils/Date";
import {
  DivLineInTable,
  DivTitleInTable,
  DivValueInTable,
  Intbody,
  Intdcenter,
  Intdleft,
  Intfoot,
  Inthead,
  Intrbody,
  Inttitle,
  TableInternal,
} from "../styles";
import { ReturnProps } from "../types";

export const PC_TK: ReturnProps<IAptus.IAptusBaseDocument> = {
  title: "Aptus.06.Accountability",
  origin: "APTUS",
  type: "PC_TK", // Prestação de Contas Turkia
  permission: "AD",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IAptus.IAptusBaseDocument) => row.Codigo || "-",
    },
    {
      name: "Favored",
      selector: (row: IAptus.IAptusBaseDocument) => row.Favorecido || "-",
    },
    {
      name: "Value",
      selector: (row: IAptus.IAptusBaseDocument) => row.Valor || "-",
    },
    {
      name: "Trip",
      cell: (row: IAptus.IAptusBaseDocument) => {
        if (row.DataInicioPeriodoViagem && row.DataFimPeriodoViagem) {
          return (
            <td data-head={t("Trip")}>
              {`${dateBDtoRead(row.DataInicioPeriodoViagem)} ${t(
                "Until"
              )} ${dateBDtoRead(row.DataFimPeriodoViagem)}`}
            </td>
          );
        }
        return "-";
      },
    },
    {
      name: "Lighthouse",
      cell: (row: IAptus.IAptusBaseDocument) =>
        row.Critica === "SIM" ? (
          <NotificationsOutlined
            sx={{ color: "#E32027" }}
            fontSize="medium"
            titleAccess={row.CriticaMotivo}
          />
        ) : (
          "-"
        ),
    },
    {
      name: "Anomaly",
      cell: (row: IAptus.IAptusBaseDocument) => {
        return row?.Itens ? AptusService.handleAnomalyColumn(row?.Itens) : "-";
      },
    },
  ],
  detailModalHeader: (
    rows: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(rows)
      ? (rows[0] as IAptus.IAptusBaseDocument)
      : rows;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {row.Codigo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Requester.ID")}:`}</div>
            {`${row.Itens[0].SolicitanteID} - ${row.Itens[0].Solicitante}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}:`}</div>
            {`${row.Itens[0].FavorecidoCPF} - ${row.Itens[0].Favorecido}`}
          </td>
        </tr>

        <tr>
          <td>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {row.Itens[0].CidadeOrigem}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination")}:`}</div>
            {row.Itens[0].CidadeDestino}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Has.advance.payment")}:`}</div>
            {row.Itens[0].PossuiAdiantamento}
          </td>
          <td>
            <DivLineInTable>
              <div>
                <div>
                  <DivTitleInTable>{t("Down.payment.number")}</DivTitleInTable>
                  <DivValueInTable>
                    {row.Itens[0].NumeroAdiantamento}
                  </DivValueInTable>
                </div>
                <div>
                  <DivTitleInTable>{`${t("Advance.amount")}`}</DivTitleInTable>
                  <DivValueInTable>
                    {row.Itens[0].ValorAdiantamento}
                  </DivValueInTable>
                </div>
              </div>
            </DivLineInTable>
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <Inttitle>{`${t("Expenses")}:`}</Inttitle>
            <TableInternal>
              <Inthead>
                <tr>
                  <th>{`${t("Date")}`}</th>
                  <th>{`${t("C.Accountancy")}`}</th>
                  <th>{`${t("Expense.Type")}`}</th>
                  <th>{`${t("Total")}`}</th>
                  <th>{`${t("Order")}`}</th>
                  <th>{`${t("CC")}`}</th>
                  <th>{`${t("Lighthouse")}`}</th>
                  <th>{`${t("Attachment")}`}</th>
                </tr>
              </Inthead>
              <Intbody>
                {row.Itens.map((list: IAptus.IItens) => (
                  <Intrbody>
                    <Intdleft data-head={t("Date")} className="noCheckbox">
                      {dateBDtoRead(list.DataDespesa || "")}
                    </Intdleft>
                    <Intdleft data-head={t("C.Accountancy")}>
                      {list.ContaContabil}
                    </Intdleft>
                    <Intdleft data-head={t("Expense.Type")}>
                      {list.tipo_despesa}
                    </Intdleft>
                    <Intdleft data-head={t("Total")}>
                      {list.ValorTotal}
                    </Intdleft>
                    <Intdleft data-head={t("Order")}>
                      {list.OrdemInterna}
                    </Intdleft>
                    <Intdleft data-head={t("CC")}>{list.CentroCusto}</Intdleft>
                    <Intdcenter data-head={t("Lighthouse")}>
                      {list.Critica === "SIM" ? (
                        <Notifications
                          sx={{ color: "#E32027" }}
                          fontSize="medium"
                          titleAccess={row.CriticaMotivo}
                        />
                      ) : (
                        "-"
                      )}
                    </Intdcenter>
                    <Intdcenter data-head={t("Anomaly")}>
                      {list.Anomalia ? (
                        <Warning
                          fontSize="medium"
                          color="error"
                          titleAccess={list.Anomalia}
                        />
                      ) : (
                        "-"
                      )}
                    </Intdcenter>
                    <Intdcenter data-head={t("Attachment")}>
                      {AptusService.handleDownloadAttachment(
                        list.Objetivo || ""
                      )}
                    </Intdcenter>
                  </Intrbody>
                ))}
              </Intbody>
              <Intfoot>
                <tr>
                  <td colSpan={5}></td>
                  <td colSpan={3}>
                    <span>{`${t("Total.Value")}: ${row.Valor}`}</span>
                  </td>
                </tr>
              </Intfoot>
            </TableInternal>
          </td>
        </tr>
      </tbody>
    );
  },
};
