import {
  Notifications,
  NotificationsOutlined,
  Warning,
} from "@mui/icons-material";
import { t } from "i18next";
import { IAptus } from "../../../interfaces";
import { AptusService } from "../../../services/aptus";
import { dateBDtoRead } from "../../../utils/Date";
import {
  Intbody,
  Intdcenter,
  Intdleft,
  Intdright,
  Intfoot,
  Inthead,
  Intrbody,
  TableInternal,
} from "../styles";
import { ReturnProps } from "../types";

export const PD_TK: ReturnProps<IAptus.IAptusBaseDocument> = {
  title: "Aptus.10.Small.Expenses.Turkey",
  origin: "APTUS",
  type: "PD_TK",
  permission: "AD",
  approveItems: true,
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IAptus.IAptusBaseDocument) => row.Codigo || "-",
    },
    {
      name: "Favored",
      selector: (row: IAptus.IAptusBaseDocument) => row.Favorecido || "-",
    },
    {
      name: "Value",
      selector: (row: IAptus.IAptusBaseDocument) => row.Valor || "-",
    },
    {
      name: "Date",
      selector: (row: IAptus.IAptusBaseDocument) =>
        dateBDtoRead(row.DataSolicitacao),
    },
    {
      name: "Lighthouse",
      selector: (row: IAptus.IAptusBaseDocument) => row.Critica || "-",
      cell: (row: IAptus.IAptusBaseDocument) =>
        row.Critica === "SIM" ? (
          <NotificationsOutlined
            sx={{ color: "#E32027" }}
            fontSize="medium"
            titleAccess={row.CriticaMotivo}
          />
        ) : (
          "-"
        ),
    },
    {
      name: "Anomaly",
      cell: (row: IAptus.IAptusBaseDocument) => {
        return row?.Itens ? AptusService.handleAnomalyColumn(row?.Itens) : "-";
      },
    },
  ],
  detailModalHeader: (
    rows: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(rows)
      ? (rows[0] as IAptus.IAptusBaseDocument)
      : rows;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {row.Codigo}
          </td>
          <td>
            <div className="tableTitle">{`${t("Requester.ID")}:`}</div>
            {`${row.Itens[0].SolicitanteID} - ${row.Itens[0].Solicitante}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}:`}</div>
            {`${row.Itens[0].FavorecidoCPF} - ${row.Itens[0].Favorecido}`}
          </td>
        </tr>

        <tr>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {dateBDtoRead(row.Itens[row.Itens.length - 1].DataDespesa || "")}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {row.Valor}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <TableInternal>
              <Inthead>
                <tr>
                  <th>{`${t("Date")}`}</th>
                  <th>{`${t("C.Accountancy")}`}</th>
                  <th>{`${t("Expense.Type")}`}</th>
                  <th>{`${t("CC")}`}</th>
                  <th>{`${t("Order")}`}</th>
                  <th>{`${t("Value")}`}</th>
                  <th>{`${t("Lighthouse")}`}</th>
                  <th>{`${t("Anomaly")}`}</th>
                  <th>{`${t("Attachment")}`}</th>
                </tr>
              </Inthead>
              <Intbody>
                {row.Itens.map((list: IAptus.IItens) => (
                  <Intrbody>
                    <Intdright data-head={t("Date")} className="noCheckbox">
                      {dateBDtoRead(list.DataDespesa || "")}
                    </Intdright>
                    <Intdleft data-head={t("C.Accountancy")}>
                      {list.ContaContabil}
                    </Intdleft>
                    <Intdleft data-head={t("Expense.Type")}>
                      {list.tipo_despesa}
                    </Intdleft>
                    <Intdleft data-head={t("CC")}>{list.CentroCusto}</Intdleft>
                    <Intdleft data-head={t("Order")}>
                      {list.OrdemInterna}
                    </Intdleft>
                    <Intdright data-head={t("Value")}>
                      {list.ValorTotal}
                    </Intdright>
                    <Intdcenter data-head={t("Lighthouse")}>
                      {list.Critica === "SIM" ? (
                        <Notifications
                          fontSize="medium"
                          color="error"
                          titleAccess={list.CriticaMotivo}
                        />
                      ) : (
                        "-"
                      )}
                    </Intdcenter>
                    <Intdcenter data-head={t("Anomaly")}>
                      {list.Anomalia ? (
                        <Warning
                          fontSize="medium"
                          color="error"
                          titleAccess={list.Anomalia}
                        />
                      ) : (
                        "-"
                      )}
                    </Intdcenter>
                    <Intdcenter data-head={t("Attachment")}>
                      {AptusService.handleDownloadAttachment(
                        list.Objetivo || ""
                      )}
                    </Intdcenter>
                  </Intrbody>
                ))}
              </Intbody>
              <Intfoot>
                <tr>
                  <td colSpan={5}></td>
                  <td colSpan={3}>
                    <span>{`${t("Total.Value")}: ${row.Valor}`}</span>
                  </td>
                </tr>
              </Intfoot>
            </TableInternal>
          </td>
        </tr>
      </tbody>
    );
  },
};
