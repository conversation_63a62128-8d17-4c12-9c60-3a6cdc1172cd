import { AptusService } from "@/services/aptus";
import { Notifications, Warning } from "@mui/icons-material";
import { t } from "i18next";
import { IAptus } from "../../../interfaces";
import { dateBDtoRead } from "../../../utils/Date";
import { ReturnProps } from "../types";

export const AD_TK: ReturnProps<IAptus.IAptusBaseDocument> = {
  title: "Aptus.08.Advance.Turkey",
  origin: "APTUS",
  type: "AD_TK",
  permission: "AD",
  approveItems: true,
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IAptus.IAptusBaseDocument) => row.Codigo || "-",
    },
    {
      name: "Favored",
      selector: (row: IAptus.IAptusBaseDocument) => row.Favorecido || "-",
    },
    {
      name: "CC",
      selector: (row: IAptus.IAptusBaseDocument) => row.CentroCusto || "-",
    },
    {
      name: "Value",
      selector: (row: IAptus.IAptusBaseDocument) => row.Valor || "-",
    },
    {
      name: "Date",
      selector: (row: IAptus.IAptusBaseDocument) =>
        dateBDtoRead(row.DataSolicitacao),
    },
    {
      name: "Lighthouse",
      selector: (row: IAptus.IAptusBaseDocument) => row.Critica || "-",
      cell: (row: IAptus.IAptusBaseDocument) =>
        row.Critica === "SIM" ? (
          <Notifications
            sx={{ color: "#E32027" }}
            fontSize="medium"
            titleAccess={row.CriticaMotivo}
          />
        ) : (
          "-"
        ),
    },
    {
      name: "Anomaly",
      cell: (row: IAptus.IAptusBaseDocument) => {
        return row?.Itens ? AptusService.handleAnomalyColumn(row?.Itens) : "-";
      },
    },
  ],
  detailModalHeader: (
    rows: IAptus.IAptusBaseDocument | IAptus.IAptusBaseDocument[]
  ) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {row.Codigo}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Requester.ID")}:`}</div>
            {row.Favorecido}
          </td>
          <td>
            <div className="tableTitle">{`${t("Cpf")}:`}</div>
            {`${row.Itens[0].FavorecidoCPF} - ${row.Itens[0].Solicitante}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CC")}:`}</div>
            {row.CentroCusto}
          </td>
          <td>
            <div className="tableTitle">{`${t("Order")}:`}</div>
            {row.OrdemInterna}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {row.Valor}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {`${row.Itens[0].Empresa} - ${row.Itens[0].EmpresaNome}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {row.Itens[0].CidadeOrigem}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destination")}:`}</div>
            {row.Itens[0].CidadeDestino}
          </td>
          <td>
            <div className="tableTitle">{`${t("Departure.Date")}:`}</div>
            {dateBDtoRead(row.DataInicioPeriodoViagem)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Return.Date")}:`}</div>
            {dateBDtoRead(row.DataFimPeriodoViagem)}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Lighthouse")}:`}</div>
            {row.Itens[0].Critica === "SIM" ? (
              <Notifications
                fontSize="medium"
                color="error"
                titleAccess={row.Itens[0].CriticaMotivo}
              />
            ) : (
              "-"
            )}
          </td>
          <td>
            <div className="tableTitle">{`${t("Anomaly")}:`}</div>
            {row.Itens[0].Anomalia === "SIM" ? (
              <Warning
                fontSize="medium"
                color="error"
                titleAccess={row.Itens[0]?.Anomalia}
              />
            ) : (
              "-"
            )}
          </td>
          <td>
            <div className="tableTitle">{`${t("Objective")}:`}</div>
            {row.Itens[0].Objetivo}
          </td>
        </tr>
      </tbody>
    );
  },
};
