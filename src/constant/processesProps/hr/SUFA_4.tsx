import { HRService } from "@/services/hr";
import { t } from "i18next";
import { ReturnProps } from "..";
import { IHRPositionCreate, IHRPositionCreate } from "../../../interfaces/IHR";

export const SUFA_4: ReturnProps<IHRPositionCreate> = {
  title: "Position.Create.Success.Factors",
  origin: "HR",
  permission: "SUFA",
  type: "SUFA_4",
  headerColumns: [
    {
      name: "code",
      selector: (row) => row.Codigo2 || "",
    },
    {
      name: "Position",
      selector: (row) => row.Cargo || "",
    },
    {
      name: "Category",
      selector: (row) => row.Categoria || "",
    },
    {
      name: "Requester",
      selector: (row) => row.Solicitante || "",
    },
    {
      name: "Position",
      selector: (row) => row.QualCargo || "",
    },
    {
      name: "RH.area",
      selector: (row) => row.AreaRH || "",
    },
  ],
  detailModalHeader: async (rows: IHRPositionCreate | IHRPositionCreate[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IHRPositionCreate) : rows;

    const { data } = (await HRService.getDocumentDetail(
      "SUFA_3",
      row.Codigo
    )) as { data: IHRPositionCreate };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Code")}:`}</div>
            {row.Codigo2}
          </td>
          <td>
            <div className="tableTitle">{`${t("Position")}:`}</div>
            {data.Cargo}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Category")}:`}</div>

            {data.Categoria}
          </td>
          <td>
            <div className="tableTitle">{`${t("Started.by")}:`}</div>
            {data.IniciadoPorFirstName}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Status")}:`}</div>
            {data.Status}
          </td>
          <td>
            <div className="tableTitle">{`${t("Start.Date.II")}:`}</div>
            {data.DataInicial}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Employee.group")}:`}</div>
            {data.NivelFuncao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Employee.subgroup")}:`}</div>
            {data.ClasseColaborador}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Regular.Temporary")}:`}</div>
            {data.RegularTemporario}
          </td>
          <td>
            <div className="tableTitle">{`${t("Salary.range")}:`}</div>
            {data.RegularTemporario}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("To.be.hired")}:`}</div>
            {data.ASerContratado}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {data.Empresa}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Organization.unit")}:`}</div>
            {data.UnidadeOrganizacional}
          </td>
          <td>
            <div className="tableTitle">{`${t("Business.unit")}:`}</div>
            {data.UnidadeNegocio}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Division")}:`}</div>
            {data.Divisao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {data.CentroCusto}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Recruitable")}:`}</div>
            {data.Recruting}
          </td>
          <td>
            <div className="tableTitle">{`${t("RH.area")}:`}</div>
            {data.AreaRH}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Subarea")}:`}</div>
            {data.SubArea}
          </td>
          <td>
            <div className="tableTitle">{`${t("Work.schedule.code")}:`}</div>
            {data.WorkSchedule}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Approved.salary")}:`}</div>
            {data.ApprovedSalary}
          </td>
          <td>
            <div className="tableTitle">{`${t("Minimum.salary.range")}:`}</div>
            {data.RangeMin}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Midle.salary.range")}:`}</div>
            {data.RangeMed}
          </td>
          <td>
            <div className="tableTitle">{`${t("Maximum.salary.range")}:`}</div>
            {data.RangeMax}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Top.Level.Position")}:`}</div>
            {data.PosicaoNivelSuperior}
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Subsistence.allowance.JUST.FOR.MEA)")}:`}
            </div>
            {data.AllowanceList}
          </td>
        </tr>
      </tbody>
    );
  },
};
