import { IHRSalaryUpdate } from "@/interfaces/IHR";
import { HRService } from "@/services/hr";
import { t } from "i18next";
import { ReturnProps } from "..";

export const SUFA_3: ReturnProps<IHRSalaryUpdate> = {
  title: "Salary.Update.Success.Factors",
  origin: "HR",
  permission: "SUFA",
  type: "SUFA_3",
  headerColumns: [
    {
      name: "Code",
      selector: (row) => row.Codigo || "-",
    },
    {
      name: "Employee",
      selector: (row) => row.Empregado || "-",
    },
    {
      name: "Reason",
      selector: (row) =>
        `${row.EventReason} - ${t(`central_${row.EventReason}`)} || "-"`,
    },
    {
      name: "Requester",
      selector: (row) => row.Solicitante || "-",
    },
  ],
  detailModalHeader: async (rows: IHRSalaryUpdate | IHRSalaryUpdate[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IHRSalaryUpdate) : rows;

    const { data } = (await HRService.getDocumentDetail(
      "SUFA_3",
      row.Codigo
    )) as { data: IHRSalaryUpdate };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Reason")}:`}</div>
            {`${row.EventReason} - ${t(`central_${row.EventReason}`)}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Employee")}:`}</div>
            {data.Empregado}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Started.by")}:`}</div>
            {data.IniciadoPorFirstName}
          </td>
          <td>
            <div className="tableTitle">{`${t("Effected.in")}:`}</div>
            {data.EfetivoEm}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Country")}:`}</div>
            {data.Pais}
          </td>
          <td>
            <div className="tableTitle">{`${t("Currency")}:`}</div>
            {data.MoedaCorrente}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Wage.Type")}:`}</div>
            {data.ComponentesPagamento}
          </td>
          <td>
            <div className="tableTitle">{`${t("Valor")}:`}</div>
            {data.Valor}
          </td>
        </tr>
      </tbody>
    );
  },
};
