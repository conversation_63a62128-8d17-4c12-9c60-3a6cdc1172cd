import { IHRExceptionRequest } from "@/interfaces/IHR";
import { t } from "i18next";
import { ReturnProps } from "..";

export const EPI: ReturnProps<IHRExceptionRequest> = {
  title: "Exception.Request",
  origin: "HR",
  type: "EPI",
  permission: "EPI",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Request"),
      selector: (row) => row.IDREQUISICAO || "",
    },
    {
      name: t("Requester"),
      selector: (row) => row.SOLICITANTE || "",
    },
    {
      name: t("Justification"),
      selector: (row) => row.JUSTIFICATIVA || "",
    },
    {
      name: t("Data"),
      selector: (row) => row.DATACADASTRO || "",
    },
  ],
  detailModalHeader: (rows: IHRExceptionRequest | IHRExceptionRequest[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IHRExceptionRequest) : rows;

    return (
      <tbody>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Request")}:`}</div>
            {row.IDREQUISICAO || "-"}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {row.NOMECENTROCUSTO || "-"}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {row.SOLICITANTE || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {row.DATACADASTRO || "-"}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Justification")}:`}</div>
            {row.JUSTIFICATIVA || "-"}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Beneficiary")}:`}</div>
            {row.NOMEFUNCIONARIO || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Reference")}:`}</div>
            {row.REFERENCIA || "-"}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("PPE")}:`}</div>
            {row.NOMEEPI}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">CA:</div>
            {row.CA || "-"}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Quantity")}:`}</div>
            {row.QUANTIDADE || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Unit.Measure")}:`}</div>
            {row.UNIDADEMEDIDA || "-"}
          </td>
        </tr>
      </tbody>
    );
  },
};
