import { IHRDemissionRequest } from "@/interfaces/IHR";
import { t } from "i18next";
import { ReturnProps } from "../types";

export const SUFA_2: ReturnProps<IHRDemissionRequest> = {
  title: "Demission.Request.Success.Factors",
  origin: "HR",
  permission: "SUFA",
  type: "SUFA_2",
  headerColumns: [
    {
      name: "Reason",
      selector: (row) =>
        `${row.EventReason} - ${t(`central_${row.EventReason}`)} || ""`,
    },
    {
      name: "Employee",
      selector: (row) => row.Empregado || "",
    },
    {
      name: "Dismissal.date",
      selector: (row) => row.TerminationDate || "",
    },
    {
      name: "Dismissal.reason",
      selector: (row) => row.LeavingReason || "",
    },
    {
      name: "Requester",
      selector: (row) => row.Solicitante || "",
    },
  ],
  detailColumns: [
    {
      name: "Commented by",
      selector: (row) => row.CommentsBy || "",
    },
    {
      name: "Comment",
      selector: (row) => row.Comments || "",
    },
    {
      name: "Date",
      selector: (row) => row.Date || "",
    },
  ],
};
