import { HRService } from "@/services/hr";
import { t } from "i18next";
import { IHRTransference } from "../../../interfaces/IHR";
import { ReturnProps } from "../types";

export const SUFA_1: ReturnProps<IHRTransference> = {
  title: "Transference.Success.Factors",
  origin: "HR",
  permission: "SUFA",
  type: "SUFA_1",
  headerColumns: [
    {
      name: "Reason",
      selector: (row) =>
        `${row.EventReason} - ${t(`central_${row.EventReason}`)} || ""`,
    },
    {
      name: "Employee",
      selector: (row) => row.Empregado || "",
    },
    {
      name: "Position",
      selector: (row) => row.Posicao || "",
    },
    {
      name: "Requester",
      selector: (row) => row.Solicitante || "",
    },
  ],
  detailModalHeader: async (rows: IHRTransference | IHRTransference[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IHRTransference) : rows;

    const { data } = (await HRService.getDocumentDetail(
      "SUFA_1",
      row.Codigo
    )) as { data: IHRTransference };

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Reason")}:`}</div>
            {`${row.EventReason} - ${t(`central_${row.EventReason}`)}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Employee")}:`}</div>
            {data.Empregado}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Position")}:`}</div>
            {data.Posicao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Started.by")}:`}</div>
            {data.IniciadoPorFirstName}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Effected.in")}:`}</div>
            {data.EfetivoEm}
          </td>
          <td>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {data.CentroCusto}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Organizational.unit")}:`}</div>
            {data.UnidadeOrganizacional}
          </td>
          <td>
            <div className="tableTitle">{`${t("Country")}:`}</div>
            {data.Pais}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Manager")}:`}</div>
            {data.Supervisor}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Employee.group")}:`}</div>-
          </td>
          <td>
            <div className="tableTitle">{`${t("Employee.subgroup")}:`}</div>-
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Position")}:`}</div>
            {data.Posicao}
          </td>
          <td>
            <div className="tableTitle">{`${t("Work.time.plan")}:`}</div>
            {data.PlanoHorarioTrabalho}
          </td>
        </tr>
      </tbody>
    );
  },
};
