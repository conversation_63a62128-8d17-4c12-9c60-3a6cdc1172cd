import React from "react";
import { TableColumn } from "react-data-table-component";
import { AD, AD_TK, CC_APTUS, DR, IE, PC, PC_TK, PD, PD_TK } from "./aptus";
import { AP, AS, AT } from "./ariba";
import * as SeoDigitalProcesses from "./autoControle";
import { VI as AVIPAM_VI } from "./avipam";
import { GCC, SALES } from "./commercial";
import { DS } from "./docuSign";
import * as HRProcesses from "./hr";
import {
  C1 as INTRANET_C1,
  C2 as INTRANET_C2,
  CP as INTRANET_CP,
  CS as INTRANET_CS,
  NO as INTRANET_NO,
  NR as INTRANET_NR,
  PM as INTRANET_PM,
  SV_1 as INTRANET_SV_1,
  SV_2 as INTRANET_SV_2,
  SV_3 as INTRANET_SV_3,
  TR as INTRANET_TR,
} from "./intranet";
import { ONE as ONELOG_OL } from "./onelog";
import { TMS_1, TMS_2, TMS_3, TMS_4, TMS_5, TMS_6 } from "./oracle";
import {
  JR_MAN as PROCUREMENT_JR,
  JR_CIRC as PROCUREMENT_JR_CIRC,
  JR_CONF as PROCUREMENT_JR_CONF,
  JR_DEP as PROCUREMENT_JR_DEP,
  ME as PROCUREMENT_ME,
  MX as PROCUREMENT_MX,
} from "./procurement";
import {
  AA,
  AB,
  AE,
  AF,
  AG,
  AL,
  AM,
  AN,
  AO,
  AV,
  CA,
  CC_SAP,
  CE,
  CF,
  CI,
  CN,
  CO,
  DF,
  DP,
  DT,
  DV,
  EA,
  ER,
  FA,
  FC,
  FF,
  FG,
  FH,
  FJ,
  FL,
  FR,
  GA,
  H5,
  HC,
  IC,
  ID,
  IF,
  II,
  IL,
  IM,
  IP,
  IV,
  KM,
  LC,
  LP,
  LS,
  LT,
  MI,
  MR,
  NC,
  NF,
  OB,
  OD,
  OI,
  OL,
  OM,
  OR,
  OT,
  OX,
  PA,
  PE,
  PG,
  PO,
  PR,
  PV,
  QM,
  RA,
  RI,
  RT,
  SA,
  SM,
  SP,
  TR_SAP,
  VL,
  VX,
  XP,
  ZO,
} from "./sap";
import { SNOW } from "./serviceNow/SNOW";

export interface ReturnProps<T> {
  title: string;
  origin: string;
  type?: string;
  permission: string;
  data?: unknown;
  headerColumns: TableColumn<T>[];
  detailColumns?: TableColumn<T>[];
  documentDetailHtml?: (rows: T | T[]) => React.ReactNode;
  approveItems?: boolean;
  hasDetailRoute?: boolean;
  hasDetailModal?: boolean;
  additional1?: (row: T) => string;
  additional2?: (row: T) => string;
  detailModalHeader?: (row: T) => React.ReactNode;
  detailModalContent?: (rows: T) => React.ReactNode;
  portalLink?: (row: T) => string;
  hasOpenUrl?: boolean;
}

const sapProcesses: Record<string, ReturnProps<unknown>> = {
  AA: AA as unknown as ReturnProps<unknown>,
  AL: AL as unknown as ReturnProps<unknown>,
  CC_SAP: CC_SAP as unknown as ReturnProps<unknown>,
  CI: CI as unknown as ReturnProps<unknown>,
  TR_SAP: TR_SAP as unknown as ReturnProps<unknown>,
  II: II as unknown as ReturnProps<unknown>,
  IL: IL as unknown as ReturnProps<unknown>,
  AB: AB as unknown as ReturnProps<unknown>,
  AE: AE as unknown as ReturnProps<unknown>,
  AF: AF as unknown as ReturnProps<unknown>,
  AG: AG as unknown as ReturnProps<unknown>,
  AM: AM as unknown as ReturnProps<unknown>,
  AN: AN as unknown as ReturnProps<unknown>,
  AO: AO as unknown as ReturnProps<unknown>,
  AV: AV as unknown as ReturnProps<unknown>,
  CA: CA as unknown as ReturnProps<unknown>,
  CE: CE as unknown as ReturnProps<unknown>,
  CF: CF as unknown as ReturnProps<unknown>,
  CN: CN as unknown as ReturnProps<unknown>,
  CO: CO as unknown as ReturnProps<unknown>,
  DF: DF as unknown as ReturnProps<unknown>,
  DP: DP as unknown as ReturnProps<unknown>,
  DT: DT as unknown as ReturnProps<unknown>,
  DV: DV as unknown as ReturnProps<unknown>,
  EA: EA as unknown as ReturnProps<unknown>,
  ER: ER as unknown as ReturnProps<unknown>,
  FA: FA as unknown as ReturnProps<unknown>,
  FC: FC as unknown as ReturnProps<unknown>,
  FF: FF as unknown as ReturnProps<unknown>,
  FG: FG as unknown as ReturnProps<unknown>,
  FH: FH as unknown as ReturnProps<unknown>,
  FJ: FJ as unknown as ReturnProps<unknown>,
  FL: FL as unknown as ReturnProps<unknown>,
  FR: FR as unknown as ReturnProps<unknown>,
  GA: GA as unknown as ReturnProps<unknown>,
  H5: H5 as unknown as ReturnProps<unknown>,
  HC: HC as unknown as ReturnProps<unknown>,
  IC: IC as unknown as ReturnProps<unknown>,
  IF: IF as unknown as ReturnProps<unknown>,
  IP: IP as unknown as ReturnProps<unknown>,
  KM: KM as unknown as ReturnProps<unknown>,
  LP: LP as unknown as ReturnProps<unknown>,
  LT: LT as unknown as ReturnProps<unknown>,
  MI: MI as unknown as ReturnProps<unknown>,
  MR: MR as unknown as ReturnProps<unknown>,
  NC: NC as unknown as ReturnProps<unknown>,
  NF: NF as unknown as ReturnProps<unknown>,
  OB: OB as unknown as ReturnProps<unknown>,
  OD: OD as unknown as ReturnProps<unknown>,
  OI: OI as unknown as ReturnProps<unknown>,
  OL: OL as unknown as ReturnProps<unknown>,
  OM: OM as unknown as ReturnProps<unknown>,
  OR: OR as unknown as ReturnProps<unknown>,
  OT: OT as unknown as ReturnProps<unknown>,
  OX: OX as unknown as ReturnProps<unknown>,
  PE: PE as unknown as ReturnProps<unknown>,
  PG: PG as unknown as ReturnProps<unknown>,
  PO: PO as unknown as ReturnProps<unknown>,
  LC: LC as unknown as ReturnProps<unknown>,
  PR: PR as unknown as ReturnProps<unknown>,
  QM: QM as unknown as ReturnProps<unknown>,
  RT: RT as unknown as ReturnProps<unknown>,
  SA: SA as unknown as ReturnProps<unknown>,
  SM: SM as unknown as ReturnProps<unknown>,
  SP: SP as unknown as ReturnProps<unknown>,
  VL: VL as unknown as ReturnProps<unknown>,
  VX: VX as unknown as ReturnProps<unknown>,
  XP: XP as unknown as ReturnProps<unknown>,
  PV: PV as unknown as ReturnProps<unknown>,
  PA: PA as unknown as ReturnProps<unknown>,
  RA: RA as unknown as ReturnProps<unknown>,
  RI: RI as unknown as ReturnProps<unknown>,
  IM: IM as unknown as ReturnProps<unknown>,
  ID: ID as unknown as ReturnProps<unknown>,
  IV: IV as unknown as ReturnProps<unknown>,
  LS: LS as unknown as ReturnProps<unknown>,
  ZO: ZO as unknown as ReturnProps<unknown>,
};
const aribaProcesses: Record<string, ReturnProps<unknown>> = {
  AP: AP as unknown as ReturnProps<unknown>,
  AT: AT as unknown as ReturnProps<unknown>,
  AS: AS as unknown as ReturnProps<unknown>,
};

const commercialProcesses: Record<string, ReturnProps<unknown>> = {
  SALES: SALES as unknown as ReturnProps<unknown>,
  GCC: GCC as unknown as ReturnProps<unknown>,
};

const serviceNowProcesses: Record<string, ReturnProps<unknown>> = {
  SNOW: SNOW as unknown as ReturnProps<unknown>,
};

const oracleProcesses: Record<string, ReturnProps<unknown>> = {
  TMS_1: TMS_1 as unknown as ReturnProps<unknown>,
  TMS_2: TMS_2 as unknown as ReturnProps<unknown>,
  TMS_3: TMS_3 as unknown as ReturnProps<unknown>,
  TMS_4: TMS_4 as unknown as ReturnProps<unknown>,
  TMS_5: TMS_5 as unknown as ReturnProps<unknown>,
  TMS_6: TMS_6 as unknown as ReturnProps<unknown>,
};

const docuSignProcesses: Record<string, ReturnProps<unknown>> = {
  DS: DS as unknown as ReturnProps<unknown>,
};

const aptusProcesses: Record<string, ReturnProps<unknown>> = {
  AD: AD as unknown as ReturnProps<unknown>,
  CC_APTUS: CC_APTUS as unknown as ReturnProps<unknown>,
  DR: DR as unknown as ReturnProps<unknown>,
  PD: PD as unknown as ReturnProps<unknown>,
  PC: PC as unknown as ReturnProps<unknown>,
  IE: IE as unknown as ReturnProps<unknown>,
  AD_TK: AD_TK as unknown as ReturnProps<unknown>,
  PC_TK: PC_TK as unknown as ReturnProps<unknown>,
  PD_TK: PD_TK as unknown as ReturnProps<unknown>,
};

const autoControleProcesses: Record<string, ReturnProps<unknown>> = {
  MANU: SeoDigitalProcesses.MANU as ReturnProps<unknown>,
};

const hrProcesses: Record<string, ReturnProps<unknown>> = {
  SUFA_1: { ...HRProcesses.SUFA_1 } as unknown as ReturnProps<unknown>,
  SUFA_2: { ...HRProcesses.SUFA_2 } as unknown as ReturnProps<unknown>,
  SUFA_3: { ...HRProcesses.SUFA_3 } as unknown as ReturnProps<unknown>,
  SUFA_4: { ...HRProcesses.SUFA_4 } as unknown as ReturnProps<unknown>,
  LM: { ...HRProcesses.LM } as unknown as ReturnProps<unknown>,
  EPI: { ...HRProcesses.EPI } as unknown as ReturnProps<unknown>,
};

const avipamProcesses: Record<string, ReturnProps<unknown>> = {
  VI: { ...AVIPAM_VI } as unknown as ReturnProps<unknown>,
};

const procurementProcesses: Record<string, ReturnProps<unknown>> = {
  MX: { ...PROCUREMENT_MX } as unknown as ReturnProps<unknown>,
  ME: { ...PROCUREMENT_ME } as unknown as ReturnProps<unknown>,
  JR_MAN: { ...PROCUREMENT_JR } as unknown as ReturnProps<unknown>,
  JR_CONF: { ...PROCUREMENT_JR_CONF } as unknown as ReturnProps<unknown>,
  JR_DEP: { ...PROCUREMENT_JR_DEP } as unknown as ReturnProps<unknown>,
  JR_CIRC: { ...PROCUREMENT_JR_CIRC } as unknown as ReturnProps<unknown>,
};

const intranetProcesses: Record<string, ReturnProps<unknown>> = {
  CP: { ...INTRANET_CP } as unknown as ReturnProps<unknown>,
  CS: { ...INTRANET_CS } as unknown as ReturnProps<unknown>,
  C1: { ...INTRANET_C1 } as unknown as ReturnProps<unknown>,
  C2: { ...INTRANET_C2 } as unknown as ReturnProps<unknown>,
  PM: { ...INTRANET_PM } as unknown as ReturnProps<unknown>,
  TR_INTRANET: { ...INTRANET_TR } as unknown as ReturnProps<unknown>,
  NO: { ...INTRANET_NO } as unknown as ReturnProps<unknown>,
  NR: { ...INTRANET_NR } as unknown as ReturnProps<unknown>,
  SV_1: { ...INTRANET_SV_1 } as unknown as ReturnProps<unknown>,
  SV_2: { ...INTRANET_SV_2 } as unknown as ReturnProps<unknown>,
  SV_3: { ...INTRANET_SV_3 } as unknown as ReturnProps<unknown>,
};

const onelogProcesses: Record<string, ReturnProps<unknown>> = {
  ONE: { ...ONELOG_OL } as unknown as ReturnProps<unknown>,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const processesConfig: Record<string, ReturnProps<unknown>> = {
  ...sapProcesses,
  ...aribaProcesses,
  ...commercialProcesses,
  ...serviceNowProcesses,
  ...oracleProcesses,
  ...docuSignProcesses,
  ...aptusProcesses,
  ...autoControleProcesses,
  ...hrProcesses,
  ...avipamProcesses,
  ...procurementProcesses,
  ...intranetProcesses,
  ...onelogProcesses,
};

const processesProps = (codigo: string): ReturnProps<unknown>[] => {
  const config = processesConfig[codigo];
  return config ? [config] : [];
};

export { processesProps };
