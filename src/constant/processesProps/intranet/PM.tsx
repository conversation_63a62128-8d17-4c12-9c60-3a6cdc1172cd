import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { t } from "i18next";
import { ReturnProps } from "..";

export const PM: ReturnProps<IIntranet.ItemProps> = {
  title: "Master.Plan",
  origin: "INTRANET",
  type: "PM",
  permission: "PM",
  hasDetailModal: true,
  headerColumns: [
    {
      name: "Item",
      selector: (item: IIntranet.ItemProps) => item.CODIGO || "-",
    },
    {
      name: "CC",
      selector: (item: IIntranet.ItemProps) => item.CC || "-",
    },
    {
      name: "Date",
      selector: (item: IIntranet.ItemProps) => item.DATASOLICITACAO || "-",
    },
    {
      name: "Unit",
      selector: (item: IIntranet.ItemProps) => item.UNIDADE || "-",
    },
  ],
  documentDetailHtml: async (
    rows: IIntranet.ItemProps | IIntranet.ItemProps[]
  ) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    const { data: detailAdvance } = await api.get(
      `${process.env.REACT_APP_INTRANET}/MasterPlanDetail/${row.CODIGO}`
    );

    const tempData = detailAdvance.DATA[0];

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.CODIGO}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {tempData.DATASOLICITACAO}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Unit")}:`}</div>
            {tempData.UNIDADE}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Expiry.deadline")}:`}</div>
            {tempData.DT_EXPIRACAO_DE && tempData.DT_EXPIRACAO_ATE
              ? `${tempData.DT_EXPIRACAO_DE} ${t("until")} ${
                  tempData.DT_EXPIRACAO_ATE
                }`
              : "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Title")}:`}</div>
            {tempData.TITULO}
          </td>
          <td>
            <div className="tableTitle">{`${t("From")}:`}</div>
            {tempData.UNIDADE_DE}
          </td>
          <td>
            <div className="tableTitle">{`${t("To")}:`}</div>
            {tempData.UNIDADE_PARA}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Occupation")}:`}</div>
            {`${t("From")}: ${tempData.PRODUTIVIDADE_DE} ${t("To")}: ${
              tempData.PRODUTIVIDADE_PARA
            }`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Salary")}:`}</div>
            {`${t("From")}: ${tempData.PRODUZIDO_DE} ${t("To")}: ${
              tempData.PRODUZIDO_PARA
            }`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Direct.Workforce")}:`}</div>
            {`${t("From")}: ${tempData.MO_DE} ${t("To")}: ${tempData.MO_PARA}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Indirect.Workforce")}:`}</div>
            {`${t("From")}: ${tempData.MOI_DE} ${t("To")}: ${
              tempData.MOI_PARA
            }`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Product.LAF")}:`}</div>
            {`${tempData.LAF} ${tempData.LAF_UNIDADE}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Contribution.Margin")}:`}</div>
            {`${tempData.MARGEM} ${tempData.MARGEM_UNIDADE}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("HH.Ton")}:`}</div>
            {`${t("From")}: ${tempData.HHTON_DE} ${t("To")}: ${
              tempData.HHTON_PARA
            } ${t("Technical.Index")}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("UEPs.Employee")}:`}</div>
            {`${t("From")}: ${tempData.MOI_DE} ${t("To")}: ${
              tempData.MOI_PARA
            } ${t("Technical Index")}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Volume")}:`}</div>
            {`${t("From")}: ${tempData.VOLUME_LITROS_DE} ${t("To")}: ${
              tempData.VOLUME_LIROS_PARA
            } ${tempData.VOLUME_LITROS_TIPO}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Impact")}:`}</div>
            {`${t("From")}: ${tempData.IMPACTO_DE} ${t("To")}: ${
              tempData.IMPACTO_PARA
            } ${t("Amount.in.BRL")}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Bench.comparison")}:`}</div>
            {`${t("From")}: ${tempData.COMPARATIVO_BENCH_DE} ${t("To")}: ${
              tempData.COMPARATIVO_BENCH_PARA
            } ${t("Technical.Index")}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Benchmarking")}:`}</div>
            {tempData.BENCHMARK}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Decision.Basis")}:`}</div>
            {tempData.BASE_RESPOSTA ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Note.Cost.Department")}:`}</div>
            {tempData.OBS_GERENCIA_CUSTOS ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Current.Situation")}:`}</div>
            {tempData.SITUACAO_ATUAL ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Proposed.Situation")}:`}</div>
            {tempData.SITUACAO_PROPOSTA ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Reason")}:`}</div>
            {tempData.JUSTIFICATIVA ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Hiring.Schedule")}:`}</div>
            {tempData.CRONOGRAMA_CONTRATACAO ?? "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">
              {`${t("Ratio.between.Proposed.and.Current.Workforce")}:`}
            </div>
            {tempData.PROPORCAO_MO ?? "-"}
          </td>
        </tr>
      </tbody>
    );
  },
};
