import { IIntranet } from "@/interfaces";
import { t } from "i18next";
import { ReturnProps } from "..";

const handleTextWithTrFormatOrBr = (value: string) => {
  function sanitize(html: string) {
    const doc = document.createElement("div");
    doc.innerHTML = html;
    return doc.innerHTML;
  }

  if (value?.toLocaleLowerCase("tr").includes(value?.toLocaleLowerCase("tr"))) {
    return <div dangerouslySetInnerHTML={{ __html: sanitize(value) }}></div>;
  }
  return value;
};

export const C2: ReturnProps<IIntranet.ItemProps> = {
  title: "Contracts.Procurations",
  origin: "INTRANET",
  type: "C2",
  permission: "C2",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (row: IIntranet.ItemProps) => row.CODIGO || "-",
    },
    {
      name: t("Requester"),
      selector: (row: IIntranet.ItemProps) => row.FAVORECIDO || "-",
    },
    {
      name: t("Attorneys.In.Fact"),
      cell: (row: IIntranet.ItemProps) => {
        if (Array.isArray(row.OUTORGADO) && row.OUTORGADO.length > 1) {
          return `${row.OUTORGADO[0].OUTORGADO}; ...`;
        }
        if (Array.isArray(row.OUTORGADO) && row.OUTORGADO[0]) {
          return row.OUTORGADO[0].OUTORGADO;
        }
        if (typeof row.OUTORGADO === "string") {
          return row.OUTORGADO;
        }
        return "-";
      },
    },
    {
      name: t("Request.Date.C2"),
      selector: (row: IIntranet.ItemProps) => row.DATASOLICITACAO || "-",
    },
  ],
  detailModalHeader: (rows: IIntranet.ItemProps | IIntranet.ItemProps[]) => {
    const row = Array.isArray(rows) ? (rows[0] as IIntranet.ItemProps) : rows;

    const outorgados =
      Array.isArray(row.OUTORGADO) && row.OUTORGADO.length > 0
        ? row.OUTORGADO.map((item) => item?.OUTORGADO ?? "-").join("; ")
        : typeof row.OUTORGADO === "string"
        ? row.OUTORGADO
        : "-";

    const observacao = row.OBSERVACAO
      ? handleTextWithTrFormatOrBr(row.OBSERVACAO)
      : "-";
    const justificativa = row.JUSTIFICATIVA
      ? handleTextWithTrFormatOrBr(row.JUSTIFICATIVA)
      : "-";
    const centroDeCusto = row.CC ? row.CC : "-";

    return (
      <tbody>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Item")}:`}</div>
            {row.CODIGO}
          </td>
          <td colSpan={6}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {row.FAVORECIDO}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Request.Date.C2")}:`}</div>
            {row.DATASOLICITACAO}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
            {centroDeCusto}
          </td>
        </tr>
        <tr>
          <td colSpan={12}>
            <div className="tableTitle">{`${t("Attorneys.In.Fact")}:`}</div>
            {outorgados}
          </td>
        </tr>
        <tr>
          <td colSpan={12}>
            <div className="tableTitle">{`${t("Consideration")}:`}</div>
            {observacao}
          </td>
        </tr>
        <tr>
          <td colSpan={12}>
            <div className="tableTitle">{`${t("Reason.C2")}:`}</div>
            {justificativa}
          </td>
        </tr>
      </tbody>
    );
  },
};
