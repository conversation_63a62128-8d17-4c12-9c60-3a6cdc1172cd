import { api } from "@/api/api";
import i18n from "@/hooks/translation";
import { IIntranet } from "@/interfaces";
import { NormProps } from "@/interfaces/intranet";
import { Table } from "@mui/material";
import { t } from "i18next";
import { ReturnProps } from "..";

export const handleNenDataAprov = (item: any) => {
  const dateNen = new Date(item.NEN_DATA_APROV.replace(" - ", " "));
  return dateNen.toLocaleString(i18n.language.replace("_", "-"));
};

export const NO: ReturnProps<NormProps> = {
  title: "Norms",
  origin: "INTRANET",
  type: "NO",
  permission: "NO",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (row) => row.CODIGO || "",
      sortable: true,
    },
    {
      name: t("Number"),
      selector: (row) => row.NUMERO || "",
      sortable: true,
    },
    {
      name: t("Title"),
      selector: (row) => row.TITULO || "",
      sortable: true,
    },
    {
      name: t("Editor"),
      selector: (row) => row.REDATOR || "",
      sortable: true,
    },
    {
      name: t("Type"),
      selector: (row) => row.TIPO || "",
      sortable: true,
    },
    {
      name: t("Edition"),
      selector: (row) => row.EDICAO || "",
      sortable: true,
    },
  ],
  documentDetailHtml: async (rows: NormProps | NormProps[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const { data: detailNorm } = await api.get(
      `${process.env.REACT_APP_INTRANET}/NormDetail/${row.CODIGO}`
    );

    const { data: developersNorm } = await api.get<IIntranet.NormEvolvedProps>(
      `${process.env.REACT_APP_INTRANET}/NormEvolved/${row.CODIGO}/elaborador`
    );

    const { data: approversNorm } = await api.get<IIntranet.NormEvolvedProps>(
      `${process.env.REACT_APP_INTRANET}/NormEvolved/${row.CODIGO}/aprovador`
    );

    const { data: approversNorm2 } = await api.get<IIntranet.NormEvolvedProps>(
      `${process.env.REACT_APP_INTRANET}/NormEvolved/${row.CODIGO}/aprovador2`
    );

    const tempData = detailNorm.data[0];

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.NUMERO}
          </td>
          <td>
            <div className="tableTitle">{`${t("Revision")}:`}</div>
            {tempData.REVISAO === "0" ? "XX" : tempData.REVISAO}
          </td>
          <td>
            <div className="tableTitle">{`${t("Edition")}:`}</div>
            {tempData.EDICAO || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Unit")}:`}</div>
            {tempData.REGIONAL || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Type")}:`}</div>
            {tempData.TIPO || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Area")}:`}</div>
            {tempData.AREA || "-"}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Department.Process")}:`}</div>
            {tempData.PROCESSO || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Title")}:`}</div>
            {tempData.TITULO || "-"}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Subtitle.Process")}:`}</div>
            {tempData.SUBTITULO || "-"}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Editor")}:`}</div>
            {tempData.REDATOR || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Job.Position")}:`}</div>
            {tempData.CARGO || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Area")}:`}</div>
            {tempData.DEPTO_USUARIO || "-"}
          </td>
          <td>
            <div className="tableTitle">{`${t("Approved.editor")}:`}</div>
            {tempData.APROVACAO_REDATOR || "-"}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Summary")}:`}</div>
            {tempData.RESUMO || "-"}
          </td>
        </tr>
        {developersNorm.data.length > 0 && (
          <>
            <tr>
              <td colSpan={4}>
                <div className="modal-title">{t("Developers")}</div>
              </td>
            </tr>
            <tr>
              <td colSpan={4}>
                <div>
                  <Table>
                    <thead>
                      <tr>
                        <td>{t("Acronym.Name")}</td>
                        <td>{`${t("Job.Position")}`}</td>
                        <td>{`${t("Area")}`}</td>
                        <td>{`${t("Approval")}`}</td>
                      </tr>
                    </thead>
                    <tbody>
                      {developersNorm.data.map((item) => {
                        if (item.NEN_DATA_APROV) {
                          item.NEN_DATA_APROV = handleNenDataAprov(item);
                        }
                        return (
                          <tr key={item.USR_ID}>
                            <td data-head={t("Acronym.Name")}>
                              {`${item.USR_SIGLA} - ${item.USR_NOME}`}
                            </td>
                            <td data-head={t("Job.Position")}>
                              {item.NEN_CARGO ?? item.USR_CARGO ?? "-"}
                            </td>
                            <td data-head={t("Area")}>{item.NEN_AREA}</td>
                            <td data-head={t("Approval")}>
                              {item.NEN_DATA_APROV ?? t("Without.Approval")}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              </td>
            </tr>
          </>
        )}

        {approversNorm && approversNorm.data.length > 0 && (
          <>
            <tr>
              <td colSpan={4}>
                <div className="modal-title">{t("Approvers")}</div>
              </td>
            </tr>
            <tr>
              <td colSpan={4}>
                <div>
                  <Table>
                    <thead>
                      <tr>
                        <td>{t("Acronym.Name")}</td>
                        <td>{`${t("Job.Position")}`}</td>
                        <td>{`${t("Area")}`}</td>
                        <td>{`${t("Approval")}`}</td>
                      </tr>
                    </thead>
                    <tbody>
                      {approversNorm.data.map((item) => {
                        if (item.NEN_DATA_APROV) {
                          item.NEN_DATA_APROV = handleNenDataAprov(item);
                        }
                        return (
                          <tr key={item.USR_ID}>
                            <td data-head={t("Acronym.Name")}>
                              {`${item.USR_SIGLA} - ${item.USR_NOME}`}
                            </td>
                            <td data-head={t("Job.Position")}>
                              {item.NEN_CARGO ?? item.USR_CARGO ?? "-"}
                            </td>
                            <td data-head={t("Area")}>{item.NEN_AREA}</td>
                            <td data-head={t("Approval")}>
                              {item.NEN_DATA_APROV ?? t("Without.Approval")}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              </td>
            </tr>
          </>
        )}

        {approversNorm2 && approversNorm2.data.length > 0 && (
          <>
            <tr>
              <td colSpan={4}>
                <div className="modal-title">{t("Approvers.II")}</div>
              </td>
            </tr>
            <tr>
              <td colSpan={4}>
                <div>
                  <Table>
                    <thead>
                      <tr>
                        <td>{t("Acronym.Name")}</td>
                        <td>{`${t("Job.Position")}`}</td>
                        <td>{`${t("Area")}`}</td>
                        <td>{`${t("Approval")}`}</td>
                      </tr>
                    </thead>
                    <tbody>
                      {approversNorm2.data.map((item) => {
                        if (item.NEN_DATA_APROV) {
                          item.NEN_DATA_APROV = handleNenDataAprov(item);
                        }
                        return (
                          <tr key={item.USR_ID}>
                            <td data-head={t("Acronym.Name")}>
                              {`${item.USR_SIGLA} - ${item.USR_NOME}`}
                            </td>
                            <td data-head={t("Job.Position")}>
                              {item.NEN_CARGO ?? item.USR_CARGO ?? "-"}
                            </td>
                            <td data-head={t("Area")}>{item.NEN_AREA}</td>
                            <td data-head={t("Approval")}>
                              {item.NEN_DATA_APROV ?? t("Without.Approval")}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              </td>
            </tr>
          </>
        )}
      </tbody>
    );
  },
};
