import { IIntranet } from "@/interfaces";
import { t } from "i18next";
import { ReturnProps } from "..";

export const CS: ReturnProps<IIntranet.ItemProps> = {
  title: "Credit.Card.Request",
  origin: "INTRANET",
  type: "CS",
  permission: "CS",
  headerColumns: [
    {
      name: t("Item"),
      selector: (item: IIntranet.ItemProps) => item.CODIGO,
    },
    {
      name: t("Requester"),
      selector: (item: IIntranet.ItemProps) => item.FAVORECIDO,
    },
    {
      name: t("Amount"),
      selector: (item: IIntranet.ItemProps) =>
        (Number(item.LIMITE) / 100).toFixed(2).replace(".", ","),
    },
    {
      name: t("Date"),
      selector: (item: IIntranet.ItemProps) => item.DATASOLICITACAO,
    },
  ],
};
