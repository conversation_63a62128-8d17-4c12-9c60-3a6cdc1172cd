import { IIntranet } from "@/interfaces";
import { t } from "i18next";
import { ReturnProps } from "..";
import { putDecimalPoint } from "../../../utils/Currency";

export const C1: ReturnProps<IIntranet.ItemProps> = {
  title: "Contracts",
  origin: "INTRANET",
  type: "C1",
  permission: "C1",
  headerColumns: [
    {
      name: t("Item"),
      selector: (row: IIntranet.ItemProps) => row.CODIGO || "",
      sortable: true,
    },
    {
      name: t("Requester"),
      selector: (row: IIntranet.ItemProps) => row.FAVORECIDO || "",
      sortable: true,
    },
    {
      name: t("CC"),
      selector: (row: IIntranet.ItemProps) => row.CC || "",
      sortable: true,
    },
    {
      name: t("Amount"),
      selector: (row: IIntranet.ItemProps) => putDecimalPoint(row.VALOR),
      sortable: true,
    },
    {
      name: t("Start"),
      selector: (row: IIntranet.ItemProps) => row.DATAINICIO || "",
      sortable: true,
    },
  ],
};
