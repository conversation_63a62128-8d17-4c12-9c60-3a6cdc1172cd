import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { dateUStoBR } from "@/utils/Date";
import { t } from "i18next";
import { ReturnProps } from "..";
import { putDecimalPoint } from "../../../utils/Currency";

export const SV_3: ReturnProps<IIntranet.SvProps> = {
  title: "Small.Expenses",
  origin: "INTRANET",
  type: "SV_3",
  permission: "SV",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (item: IIntranet.SvProps) => item.CODIGO ?? "-",
    },
    {
      name: t("Beneficiary"),
      selector: (item: IIntranet.SvProps) => item.FAVORECIDO ?? "-",
    },
    {
      name: t("Request.Date"),
      selector: (item: IIntranet.SvProps) =>
        item.DATASOLICITACAO ? dateUStoBR(item.DATASOLICITACAO) : "-",
    },
    {
      name: t("Currency"),
      selector: (item: IIntranet.SvProps) => item.SIGLA_MOEDA ?? "-",
    },
    {
      name: t("Value"),
      selector: (item: IIntranet.SvProps) =>
        item.VALOR ? putDecimalPoint(item.VALOR) : "-",
    },
  ],

  documentDetailHtml: async (rows: IIntranet.SvProps | IIntranet.SvProps[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const { data: dataExpenseItems } = await api.get(
      `${process.env.REACT_APP_INTRANET}/SavSmallExpenseItems/${row.CODIGO}`
    );
    const { data: dataDetails } = await api.get(
      `${process.env.REACT_APP_INTRANET}/SavSmallExpenseDetail/${row.CODIGO}`
    );
    3;

    const tempData = dataDetails.data[0];
    const VL_CPMF = `0${tempData.VL_CPMF}`;

    return (
      <tbody>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.DES_CODIGO}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Type")}:`}</div>
            {tempData.DES_TIPO}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("CPF.CNPJ.Name")}:`}</div>
            {`${tempData.DES_CPFCNPJ} -${tempData.DES_NOME}`}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {`${tempData.DES_USR_SIGLA_CAD} -${tempData.DES_USR_NOME_CAD}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Document.Number")}:`}</div>
            {`${tempData.DES_NFNUM}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {`${dateUStoBR(tempData.DES_NFDATA)}`}
          </td>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Expiry.Date")}:`}</div>
            {`${dateUStoBR(tempData.DES_DATA_VENC)}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Amount")}:`}</div>
            {`${tempData.DES_MOEDA} ${putDecimalPoint(
              tempData.DES_IMPORTANCIA
            )}`}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Amount.CPMF.Taxes")}`}</div>
            {`${tempData.DES_MOEDA} ${putDecimalPoint(
              tempData.DES_IMPORTANCIA ?? VL_CPMF ?? "0"
            )}`}
          </td>
        </tr>
        <tr>
          <td colSpan={5}>
            <div className="modal-title">{t("Expenses")}</div>
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Nature.Expenditure")}`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Cost.Center")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("VAT")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("VAT.Value")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">
              {`${t("Sub.total ")}: ${tempData.DES_MOEDA}`}
            </div>
          </td>
        </tr>
        {dataExpenseItems.data.map((item: IIntranet.Sv3ExpenseItemProps) => {
          return (
            <tr>
              <td>
                {item.DEST_OUT === 1
                  ? `${item.DEST_DESCRICAO} - ${item.DESI_OUTDESCRI}`
                  : item.DEST_DESCRICAO}
              </td>
              <td>{item.DESI_CCUSTO}</td>
              <td>{item.VATNOME}</td>
              <td>{putDecimalPoint(item.VATVALOR.toString())}</td>
              <td>{putDecimalPoint(item.DESI_IMPORTANCIA.toString())}</td>
            </tr>
          );
        })}
      </tbody>
    );
  },
};
