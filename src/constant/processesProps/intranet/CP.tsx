import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { t } from "i18next";
import { ReturnProps } from "..";
import { putDecimalPoint } from "../../../utils/Currency";

export const CP: ReturnProps<IIntranet.CpItemProps> = {
  title: "Credit.Card.Installments",
  origin: "INTRANET",
  type: "CP",
  permission: "CP",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (item: IIntranet.CpItemProps) => item.CODIGO || "-",
    },
    {
      name: t("Holder"),
      selector: (item: IIntranet.CpItemProps) => item.FAVORECIDO || "-",
    },
    {
      name: t("Request.Date"),
      selector: (item: IIntranet.CpItemProps) => item.DATASOLICITACAO || "-",
    },
    {
      name: t("Month.of.Invoice"),
      selector: (item: IIntranet.CpItemProps) =>
        item.DATA_FATURA
          ? new Date(item.DATA_FATURA).toLocaleDateString("pt-BR")
          : "-",
    },
    {
      name: t("Total.Amount"),
      selector: (item: IIntranet.CpItemProps) =>
        `${item.MOEDA || ""} ${
          item.VALORTOTAL ? putDecimalPoint(item.VALORTOTAL) : "-"
        }`,
    },
    {
      name: t("Summarized.Purpose"),
      selector: (item: IIntranet.CpItemProps) => item.OBJETIVO || "-",
    },
  ],

  documentDetailHtml: async (
    rows: IIntranet.CpItemProps | IIntranet.CpItemProps[]
  ) => {
    const row = Array.isArray(rows) ? (rows[0] as IIntranet.CpItemProps) : rows;

    const { data: dataExpense } = await api.get(
      `${process.env.REACT_APP_INTRANET}/CreditCardExpenseItems/${row.CODIGO}`
    );
    const { data: dataAccountability } = await api.get(
      `${process.env.REACT_APP_INTRANET}/CreditCardAccountability/${row.CODIGO}`
    );

    let total = 0;

    const tempData = dataAccountability.data[0];

    return (
      <tbody>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.CODIGO}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Date")}:`}</div>
            {new Date(tempData.DATA_PRESTACAO).toLocaleDateString("pt-BR")}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {`${tempData.SIGLA_SOLICITANTE} -${tempData.NOME_SOLICITANTE}`}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("CPF")}:`}</div>
            {tempData.CPF_SOLICITANTE}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Holder")}:`}</div>
            {`${tempData.SIGLA_TITULAR} -${tempData.NOME_TITULAR}`}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("CPF")}:`}</div>
            {tempData.CPF_TITULAR}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {tempData.EMPRESA}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Card.Number")}:`}</div>
            {tempData.CARTAO_NUMERO}
          </td>
        </tr>
        <tr>
          <td colSpan={3}>
            <div className="tableTitle">{`${t("Manager")}:`}</div>
            {`${tempData.SIGLA_GESTOR} -${tempData.NOME_GESTOR}`}
          </td>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Month.of.Invoice")}`}</div>
            {new Date(tempData.DATA_FATURA).toLocaleDateString("pt-BR")}
          </td>
        </tr>
        <tr>
          <td colSpan={7}>
            <div className="tableTitle">{`${t("Summarized.Purpose")}`}</div>
            {tempData.OBJETIVO}
          </td>
        </tr>
        <tr>
          <td colSpan={7}>
            <div className="modal-title">{t("Expenses")}</div>
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Date")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Ledger.Account")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Type.of.Expense")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("CC")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Order")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Amount")}:`}</div>
          </td>
          <td>
            <div className="tableTitle">{`${t("Expense.Description")}`}</div>
          </td>
        </tr>
        {dataExpense.data.map((item: IIntranet.CpExpenseProps) => {
          total += Number(item.VALOR);
          return (
            <tr>
              <td>{new Date(item.DATA).toLocaleDateString("pt-BR")}</td>
              <td>{item.CONTA_CONTABIL}</td>
              <td>{item.TIPO_DESPESA}</td>
              <td>{item.CENTRO_CUSTO}</td>
              <td>{item.ORDEM}</td>
              <td>{item.VALOR}</td>
              <td>{item.DESCRICAO}</td>
            </tr>
          );
        })}
        <tr>
          <td colSpan={6}>
            <div className="tableTitle">
              {`${t("Total.Amount")}: R$ ${total}`}
            </div>
          </td>
          <td></td>
        </tr>
      </tbody>
    );
  },
};
