import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { dateUStoBR } from "@/utils/Date";
import { ExpandMoreOutlined } from "@mui/icons-material";
import { t } from "i18next";
import React from "react";
import { ReturnProps } from "..";
import { putDecimalPoint } from "../../../utils/Currency";
import {
  ButtonsTd,
  DivCollapse,
  Table,
  TableInternal,
  TrCollapse,
} from "../styles";

const callbackItemDropdown = (codigo: string) => {
  const collapseElement = document.getElementById(`collapse-${codigo}`);
  if (collapseElement) {
    collapseElement.classList.toggle("show");
  }
};

export const SV_2: ReturnProps<IIntranet.SvProps> = {
  title: "Accountabilities",
  origin: "INTRANET",
  type: "SV_2",
  permission: "SV",
  hasDetailModal: true,

  headerColumns: [
    {
      name: t("Item"),
      selector: (item: IIntranet.SvProps) => item.CODIGO,
    },
    {
      name: t("Beneficiary"),
      selector: (item: IIntranet.SvProps) => item.FAVORECIDO,
    },
    {
      name: t("Value"),
      selector: (item: IIntranet.SvProps) =>
        `${item.SIGLA_MOEDA}${putDecimalPoint(item.VALOR)}`,
    },
    {
      name: t("Trip"),
      selector: (item: IIntranet.SvProps) =>
        `${dateUStoBR(item.DATA_INICIO)} ${t("Until")} ${dateUStoBR(
          item.DATA_FIM
        )}`,
    },
  ],

  documentDetailHtml: async (rows: IIntranet.SvProps | IIntranet.SvProps[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;

    const { data: detailAdvance } = await api.get(
      `${process.env.REACT_APP_INTRANET}/Sav/${row.CODIGO}/false`
    );

    const { data: expenseItems } = await api.get<IIntranet.Sv2ExpenseItemProps>(
      `${process.env.REACT_APP_INTRANET}/SavItems/${row.CODIGO}`
    );

    const documents = [] as string[];
    expenseItems.data.forEach((element) => {
      if (!documents.includes(element.PTI_DATAMOV)) {
        documents.push(element.PTI_DATAMOV);
      }
    });

    const tempData = detailAdvance[0];
    const { data: adiantamento } = await api.get(
      `${process.env.REACT_APP_INTRANET}/Sav/${tempData.VIA_COD_ADTO}/false`
    );

    return (
      <tbody>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.CODIGO}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Requester")}:`}</div>
            {tempData.SOLICITANTE}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}`}</div>
            {`${tempData.FAVORECIDO_CPF}-${tempData.FAVORECIDO}`}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Advance")}:`}</div>
            {adiantamento.length > 0 &&
              `${dateUStoBR(adiantamento[0].VIA_DATA)} - ${
                adiantamento[0].CODIGO
              } - ${adiantamento[0].FAVORECIDO}`}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">
              {`${t("R&D Project Expense Law 11.196/05 (Law of Science)")}:`}
            </div>
            {tempData.VIA_DISPENDIO.toString() === "1" ? t("yes") : t("no")}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {tempData.EMPRESA}
          </td>
          <td>
            <div className="tableTitle">{`${t("Departure.Date")}:`}</div>
            {dateUStoBR(tempData.DATAINICIO)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Return.Date")}:`}</div>
            {dateUStoBR(tempData.DATAFIM)}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {tempData.ORIGEM}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Destin")}:`}</div>
            {tempData.DESTINO}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Transport")}:`}</div>
            {tempData.VIC_TIPO_VEICULO.toString() === "1" && t("OwnVehicle")}
            {tempData.VIC_TIPO_VEICULO.toString() === "2" &&
              t("Company.Vehicle")}
            {tempData.VIC_TIPO_VEICULO.toString() === "3" && t("Others")}

            {tempData.VIC_VEICULO_OUTROS &&
            tempData.VIC_VEICULO_OUTROS.toString() !== ""
              ? ` ${t("Plate")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {tempData.VIC_VEICULO_OUTROS &&
            tempData.VIC_VEICULO_OUTROS.toString() !== "" &&
            tempData.VIC_TIPO_VEICULO.toString() === "3"
              ? ` ${t("Whatis")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {(tempData.VIC_TIPO_VEICULO.toString() === "1" ||
              tempData.VIC_TIPO_VEICULO.toString() === "2") &&
            tempData.VIC_VEICULO_OUTROS &&
            tempData.VIC_VEICULO_OUTROS.toString() !== "" &&
            tempData.VIC_TIPO_VEICULO.toString() === "3"
              ? ` ${t("What")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {tempData.VIC_VEICULO_ONIBUS.toString() === "1" && t("Bus")}
            {tempData.VIC_VEICULO_AVIAO.toString() === "1" && t("Airplane")}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Purpose")}:`}</div>
            {tempData.OBJETIVO}
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div className="modal-title">{t("Expenses")}</div>
          </td>
        </tr>
        <tr>
          <td colSpan={4}>
            <div>
              <Table>
                <thead>
                  <tr>
                    <td>{t("Date")}</td>
                    <td>{`${t("Subtotal")} R$`}</td>
                    <td></td>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((doc, index) => {
                    const code = expenseItems.data?.filter(
                      (resp) => resp.PTI_DATAMOV === doc
                    );
                    let subtotal = 0;
                    code.forEach((sub) => {
                      subtotal += sub.PTI_VALOR;
                    });
                    return (
                      <React.Fragment key={`frag-${index}`}>
                        <tr>
                          <td data-head={t("Date")} className="noCheckbox">
                            {dateUStoBR(doc)}
                          </td>
                          <td data-head={t("Subtotal")}>
                            {putDecimalPoint(subtotal.toString())}
                          </td>
                          <ButtonsTd>
                            <div>
                              <div data-translation={t("Expand")}>
                                <ExpandMoreOutlined
                                  id={`iconExpand-${row.CODIGO}`}
                                  fontSize="medium"
                                  onClick={(e) => {
                                    callbackItemDropdown(`collapse-${index}`);
                                    e.currentTarget.classList.toggle("show");
                                  }}
                                />
                              </div>
                            </div>
                          </ButtonsTd>
                        </tr>
                        <TrCollapse>
                          <td colSpan={3}>
                            <DivCollapse id={`collapse-${index}`}>
                              <TableInternal>
                                <thead>
                                  <tr>
                                    <td>{t("Meal")}</td>
                                    <td>{t("Taxi")}</td>
                                    <td>{t("KM")}</td>
                                    <td>{t("Ticket")}</td>
                                    <td>{t("Others")}</td>
                                    <td>{t("VAT")}</td>
                                    <td>{t("VAT.Value")}</td>
                                    <td>{t("Order")}</td>
                                    <td>{t("CC")}</td>
                                    <td>{t("Sub.total")}</td>
                                    <td>{t("Observation")}</td>
                                  </tr>
                                </thead>
                                <tbody>
                                  {code.map((subcode) => {
                                    return (
                                      <tr>
                                        <td
                                          data-head={t("Meal")}
                                          className="noCheckbox"
                                        >
                                          {putDecimalPoint(
                                            subcode.REFEICAO ?? "0"
                                          )}
                                        </td>
                                        <td data-head={t("Taxi")}>
                                          {putDecimalPoint(subcode.TAXI ?? "0")}
                                        </td>
                                        <td data-head={t("KM")}>
                                          {putDecimalPoint(subcode.KM ?? "0")}
                                        </td>
                                        <td data-head={t("Ticket")}>
                                          {putDecimalPoint(
                                            subcode.PASSAGEM ?? "0"
                                          )}
                                        </td>
                                        <td data-head={t("Others")}>
                                          {putDecimalPoint(
                                            subcode.OUTROS ?? "0"
                                          )}
                                        </td>
                                        <td data-head={t("VAT")}>
                                          {subcode.VATNOME}
                                        </td>
                                        <td data-head={t("VAT.Value")}>
                                          {putDecimalPoint(
                                            subcode.VATVALOR.toString()
                                          )}
                                        </td>
                                        <td data-head={t("Order")}>
                                          {subcode.PTI_ORDEM}
                                        </td>
                                        <td data-head={t("CC")}>
                                          {subcode.PTI_CCUSTO}
                                        </td>
                                        <td data-head={t("Sub.total")}>
                                          {putDecimalPoint(
                                            (
                                              subcode.PTI_VALOR +
                                              subcode.VATVALOR
                                            ).toString()
                                          )}
                                        </td>
                                        <td data-head={t("Observation")}>
                                          {subcode.PTI_OBS}
                                        </td>
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </TableInternal>
                            </DivCollapse>
                          </td>
                        </TrCollapse>
                      </React.Fragment>
                    );
                  })}
                </tbody>
              </Table>
            </div>
          </td>
        </tr>
      </tbody>
    );
  },
};
