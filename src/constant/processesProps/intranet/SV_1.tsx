import { api } from "@/api/api";
import { IIntranet } from "@/interfaces";
import { putDecimalPoint } from "@/utils/Currency";
import { dateUStoBR } from "@/utils/Date";
import { t } from "i18next";
import { ReturnProps } from "..";

export const SV_1: ReturnProps<IIntranet.SvProps> = {
  title: "Advance",
  origin: "INTRANET",
  type: "SV_1",
  permission: "SV",
  hasDetailModal: true,
  headerColumns: [
    {
      name: t("Item"),
      selector: (item: IIntranet.SvProps) => item.CODIGO || "-",
    },
    {
      name: t("Holder"),
      selector: (item: IIntranet.SvProps) => item.FAVORECIDO || "-",
    },
    {
      name: t("Request.Date"),
      selector: (item: IIntranet.SvProps) => item.CC || "-",
    },
    {
      name: t("Month.of.Invoice"),
      selector: (item: IIntranet.SvProps) => item.ORDEM || "-",
    },
    {
      name: t("Amount"),
      selector: (item: IIntranet.SvProps) =>
        `${item.SIGLA_MOEDA ?? ""} ${
          item.VALOR ? putDecimalPoint(item.VALOR) : "-"
        }`,
    },
    {
      name: t("Trip"),
      selector: (item: IIntranet.SvProps) =>
        item.DATA_INICIO && item.DATA_FIM
          ? `${item.DATA_INICIO} ${t("Until")} ${item.DATA_FIM}`
          : "-",
    },
  ],

  documentDetailHtml: async (rows: IIntranet.SvProps | IIntranet.SvProps[]) => {
    const row = Array.isArray(rows) ? rows[0] : rows;
    const { data: detailAdvance } = await api.get(
      `${process.env.REACT_APP_INTRANET}/Sav/${row.CODIGO}/false`
    );

    const tempData = detailAdvance[0];

    return (
      <tbody>
        <tr>
          <td colSpan={4}>
            <div className="tableTitle">{`${t("Number")}:`}</div>
            {tempData.CODIGO}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Holder")}:`}</div>
            {tempData.SOLICITANTE}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("CPF.Beneficiary")}:`}</div>
            {`${tempData.FAVORECIDO_CPF} -${tempData.FAVORECIDO}`}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("CC")}:`}</div>
            {tempData.CC}
          </td>
          <td>
            <div className="tableTitle">{`${t("Order")}:`}</div>
            {tempData.ORDEM}
          </td>
          <td>
            <div className="tableTitle">{`${t("Value")}:`}</div>
            {`${tempData.SIGLA_MOEDA} ${putDecimalPoint(tempData.VALOR)}`}
          </td>
          <td>
            <div className="tableTitle">{`${t("Company")}:`}</div>
            {tempData.EMPRESA}
          </td>
        </tr>
        <tr>
          <td>
            <div className="tableTitle">{`${t("Origin")}:`}</div>
            {tempData.ORIGEM}
          </td>
          <td>
            <div className="tableTitle">{`${t("Destin")}:`}</div>
            {tempData.DESTINO}
          </td>
          <td>
            <div className="tableTitle">{`${t("Departure.Date")}:`}</div>
            {dateUStoBR(tempData.VIC_PREV_SAIDA)}
          </td>
          <td>
            <div className="tableTitle">{`${t("Return.Date")}:`}</div>
            {dateUStoBR(tempData.VIC_PREV_REGRESSO)}
          </td>
        </tr>
        <tr>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Transport")}:`}</div>
            {tempData.VIC_TIPO_VEICULO === "1" && t("OwnVehicle")}
            {tempData.VIC_TIPO_VEICULO === "2" && t("Company.Vehicle")}
            {tempData.VIC_TIPO_VEICULO === "3" && t("Others")}

            {tempData.VIC_VEICULO_OUTROS && tempData.VIC_VEICULO_OUTROS !== ""
              ? ` ${t("Plate")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {tempData.VIC_VEICULO_OUTROS &&
            tempData.VIC_VEICULO_OUTROS !== "" &&
            tempData.VIC_TIPO_VEICULO === "3"
              ? ` ${t("What")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {(tempData.VIC_TIPO_VEICULO === "1" ||
              tempData.VIC_TIPO_VEICULO === "2") &&
            tempData.VIC_VEICULO_OUTROS &&
            tempData.VIC_VEICULO_OUTROS !== "" &&
            tempData.VIC_TIPO_VEICULO === "3"
              ? ` ${t("What")}:${tempData.VIC_VEICULO_OUTROS}`
              : ""}
            {tempData.VIC_VEICULO_ONIBUS === "1" ? t("Bus") : ""}
            {tempData.VIC_VEICULO_AVIAO === "1" ? t("Airplane") : ""}
          </td>
          <td colSpan={2}>
            <div className="tableTitle">{`${t("Objective")}:`}</div>
            {tempData.OBJETIVO}
          </td>
        </tr>
      </tbody>
    );
  },
};
