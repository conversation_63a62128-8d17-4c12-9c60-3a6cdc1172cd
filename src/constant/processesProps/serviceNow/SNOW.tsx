import { ReturnProps } from "..";
import { IServiceNow } from "../../../interfaces";

export const SNOW: ReturnProps<IServiceNow.ItemProps> = {
  title: "ServiceNow.Requests",
  origin: "SERVICENOW",
  type: "SNOW",
  permission: "SNOW",
  hasDetailModal: true,
  hasDetailRoute: false,
  headerColumns: [
    {
      name: "Item",
      selector: (row: IServiceNow.ItemProps) => row.Codigo || "-",
    },
    {
      name: "Requester",
      selector: (row: IServiceNow.ItemProps) => row.Descricao || "-",
    },
    {
      name: "Favored",
      selector: (row: IServiceNow.ItemProps) => row.Favorecido || "-",
    },
    {
      name: "Position",
      selector: (row: IServiceNow.ItemProps) => row.Cargo || "-",
    },
    {
      name: "Description",
      selector: (row: IServiceNow.ItemProps) => row.Solicitante || "-",
    },
    {
      name: "Area",
      selector: (row: IServiceNow.ItemProps) => row.Solicitante || "-",
    },
    {
      name: "Profile",
      selector: (row: IServiceNow.ItemProps) => row.Solicitante || "-",
    },
  ],
};
