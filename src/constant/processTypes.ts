export interface ProcessType {
  label: string;
}

export const processTypes: Array<ProcessType> = [
  { label: 'AA - APROVAÇÃO DE ATAS' },
  { label: 'AB - REL. PERFORMANCE - APROVAÇÕES' },
  { label: 'AC - CONTROLE DE ACESSOS' },
  { label: 'AD - ADIANTAMENTOS E ACERTOS APTUS' },
  { label: 'AE - AG<PERSON><PERSON>MENTO DESCARGA CLIENTES' },
  { label: 'AF - ADICIONAL FRETE PRIMARIA' },
  { label: 'AG - APROVAR FRETE COMPLEM. AGRO' },
  { label: 'AH - APROVAR FRETE COMPLEM. DISTRIB' },
  { label: 'AI - AUTOGESTÃO-PGTO ASSIT. MEDICA' },
  { label: 'AJ - APROVAÇÃO-PEND DE NEGOCIAÇÃO' },
  { label: 'AK - PA<PERSON>MENTO DE OVOS SOX' },
  { label: 'AL - ARIBA CONTRACT' },
  { label: 'AM - ARIBA' },
  { label: 'AN - ARIBA - PROCESSO DE SOURCING' },
  { label: 'AO - ARIB<PERSON> CONTRACT' },
  { label: 'AP - CONCESSÃO GARANT. BENS MÓV/IMO' },
  { label: 'AQ - CONTRATOS' },
  { label: 'AR - CONTRATOS-PROCURAÇÕES' },
  { label: 'AS - CONTRATOS-TERCEIRIZAÇÃO' },
  { label: 'AT - CONTR. ATIV. PROMOCIONAL' },
  { label: 'AU - COFFEE BREAK' },
  { label: 'AV - CONTRATO DE COMPRA' },
  { label: 'AW - COMPLEMENTO - ESPECIALISTA' },
  { label: 'AX - CIÊNCIA DE DIVERGÊNCIA DE PESO' },
  { label: 'AY - CLAIM INTERNACIONAL' },
  { label: 'AZ - CIRCULARIZAÇÃO JUR CONTENCIOSO' },
  { label: 'BA - COMPLEMENTO - NORMA' },
  { label: 'BB - COMPLEMENTO - ORÇAMENTO' },
  { label: 'BC - CARTÕES DE CRÉDITO-PRESTAÇÃO' },
  { label: 'BD - CARTÕES DE CRÉDITO-SOLICITAÇÃO' },
  { label: 'BE - FRETE DISTRIBUIÇÃO' },
  { label: 'BF - APROVAR DIVERGENCIA DE PESO' },
  { label: 'BG - DOCU SIGN' },
  { label: 'BH - SOLICITAÇÃO DE DETERIORADOS' },
  { label: 'BI - APROVAÇÃO DIVERGÊNCIA' },
  { label: 'BJ - ANTECIPAÇÃO DE ENTREGA' },
  { label: 'BK - ESTRUTURA RH - EXCEÇÕES' },
  { label: 'BL - EVENTOS' },
  { label: 'BM - ALTERAÇÃO PEDIDOS GRAOS Z5T4N' },
  { label: 'BN - CONCESSÃO GARANT. REAIS/JUDEJU' },
  { label: 'BO - PEDIDOS GRAOS MOD.DEP. + TRF' },
  { label: 'BP - FICHA DE DELEGAÇÃO' },
  { label: 'BQ - FRETES GRAOS' },
  { label: 'BR - ORDEM DE VENDAS BLOQ FIFO 33' },
  { label: 'BS - FILIAIS' },
  { label: 'BT - ORDEM DE VENDAS BLOQ-REGRA 30' },
  { label: 'BU - APROVAÇÃO FOLHA DE PAGAMENTO' },
  { label: 'BV - SOLICITAÇÃO DE FRETE' },
  { label: 'BW - GESTÃO DE FROTAS-SOLICITAÇÃO' },
  { label: 'BX - GESTÃO DE FROTAS-CRÍTICA' },
  { label: 'BY - GESTÃO DE FROTAS-PÓS CRÍTICA' },
  { label: 'BZ - GESTÃO DE FROTAS-2 VIA DOCTO.' },
  { label: 'CA - GESTÃO DE FROTAS-2 VIA CARTÃO' },
  { label: 'CB - GESTÃO DE FROTAS-CARRO PROVIS.' },
  { label: 'CC - GESTÃO DE FROTAS-TROCA DE TIT.' },
  { label: 'CD - GESTÃO DE FROTAS-SOLICIT. LM' },
  { label: 'CE - GESTÃO DE FROTAS-TR.DE TIT. LM' },
  { label: 'CF - GESTÃO TI' },
  { label: 'CG - REQUISIÇÃO DE PESSOAL' },
  { label: 'CH - ALTERAÇÃO SALARIAL' },
  { label: 'CI - TRANSFERÊNCIA DE PESSOAL' },
  { label: 'CJ - DESLIGAMENTO' },
  { label: 'CK - FÉRIAS' },
  { label: 'CL - HIERARQUIA CENTRO DE CUSTO' },
  { label: 'CM - HARDWARE E SOFTWARE' },
  { label: 'CN - INVENTÁRIO DE CD/WM' },
  { label: 'CO - INCENTIVO EDUCACIONAL' },
  { label: 'CP - INVENTÁRIO DE FÁBRICA' },
  { label: 'CQ - INVENTÁRIO DE IMOBILIZADO' },
  { label: 'CR - LAUDO IMOBILIZADO' },
  { label: 'CS - IMPORTAÇÃO - LIBERAÇÃO PAGTO' },
  { label: 'CT - ACORDO JUR. TRAB./CÍVEL MASSA' },
  { label: 'CU - ACORDO JUR. CÍVEL IMPAC/AMBIEN' },
  { label: 'CV - ATUALIZAÇÃO DEPÓSITOS JURÍDICO' },
  { label: 'CW - SISTEMA JURÍDICO CONTENCIOSO' },
  { label: 'CX - CONTESTAÇÃO KM PARTICULAR' },
  { label: 'CY - LANÇAMENTO CONTABIL MANUAL' },
  { label: 'CZ - LEARNING MANAGEMENT-LMS' },
  { label: 'DA - LNT' },
  { label: 'DB - LANÇAMENTO DE PERDAS' },
  { label: 'DC - MOVIMENTAÇÕES DE LOTES' },
  { label: 'DD - HOMOLOGAÇÃO EXCEÇÃO' },
  { label: 'DE - MOVIMENTAÇÃO DE IMOBILIZADO' },
  { label: 'DF - CADASTRO MRP' },
  { label: 'DG - HOMOLOGAÇÃO NORMAL' },
  { label: 'DH - PNC DIGITAL - GRÃOS' },
  { label: 'DI - APROVAÇÃO DE NF GNF' },
  { label: 'DJ - NORMAS' },
  { label: 'DK - NORMAS-REVOGAÇÃO' },
  { label: 'DL - ORDEM DE VENDAS BLOQ.POR PREÇO' },
  { label: 'DM - OPORTUNIDADES DA CASA' },
  { label: 'DN - NEW CUSTOMER CREATION REQUEST' },
  { label: 'DO - TERMO ENCERRAMENTO OI' },
  { label: 'DP - ME - LIBERAR BLOQUEIO CLIENTE' },
  { label: 'DQ - ME - LIBERAR BLOQUEIO OV' },
  { label: 'DR - ORDENS DE INVESTIMENTO Z070048' },
  { label: 'DS - ORDENS DE CUSTO' },
  { label: 'DT - AUDITORIA DE TICKETS SOX' },
  { label: 'DU - PROV. POSTURA E ABATE MATRIZES' },
  { label: 'DV - PRICING PROMOTION ENGINE' },
  { label: 'DW - SOLICITAÇÃO PEDIDO COMPRA GRÃO' },
  { label: 'DX - PLANO MESTRE' },
  { label: 'DY - PEDIDO DE COMPRA' },
  { label: 'DZ - REQUISIÇÃO DE COMPRA' },
  { label: 'EA - PRORROGAÇÃO/ANT. DE VENCIMENTO' },
  { label: 'EB - QUEBRA DE MALHA' },
  { label: 'EC - RETURN ORDER APPROVALS TR' },
  { label: 'ED - RETIFICAÇÃO DE FAIXA RESTRITA' },
  { label: 'EE - SOLICITAÇÃO DE ADIANTAMENTO' },
  { label: 'EF - SIC-SIST.INTEG.DE CONTRATAÇÕES' },
  { label: 'EG - SOLICITAÇÃO DE DESPESA' },
  { label: 'EH - CONCESSÃO SEGURO-GARANTIA' },
  { label: 'EI - SOLICITAÇÃO DE MATERIAL DMS' },
  { label: 'EJ - SOL. CADASTRO/ALTER. DE PLACAS' },
  { label: 'EK - ADNTO/PREST DE CTAS/PEQ DESP' },
  { label: 'EL - PAGAMENTO DE TRIBUTOS JURÍDICO' },
  { label: 'EM - TELEFONIA' },
  { label: 'EN - TRIBUTOS' },
  { label: 'EO - TROCA TITULARIDADE' },
  { label: 'EP - VIAGENS (AGÊNCIA)' },
  { label: 'EQ - ANOMALIA DE LACRES' },
  { label: 'ER - PROCESSO DE ACORDOS VISTEX' },
  { label: 'FA - VehicleRequest' },
  { label: 'FB - OwnershipTrans' },
  { label: 'FC - Copastur' },
  { label: 'FD - PendingEnvelop' },
  { label: 'FE - ApprovalCostor' },
  { label: 'FF - BatchRectifica' },
  { label: 'FG - ChangesInGrain' },
  { label: 'FH - ApprovalOfMinu' },
  { label: 'FI - SelfManagement' },
  { label: 'FJ - Bonus' },
  { label: 'FK - ClaimInternati' },
  { label: 'FL - Contracts' },
  { label: 'FM - Deteriorated' },
  { label: 'FN - WeightDivergen' },
  { label: 'FO - HumanResources' },
  { label: 'FP - Vacation' },
  { label: 'FQ - AdditionalFrei' },
  { label: 'FR - Payroll' },
  { label: 'FS - CostCenterHier' },
  { label: 'FT - FixedAssets' },
  { label: 'FU - FreightDistrib' },
  { label: 'FV - ImportPaidRele' },
  { label: 'FW - DmsMaterialReq' },
  { label: 'FX - MovementOfImmo' },
  { label: 'FY - BlockedSalesor' },
  { label: 'FZ - PurchaseOrder' },
  { label: 'GA - GrainPurchasin' },
  { label: 'GB - SecuritiesExte' },
  { label: 'GC - FreightRequest' },
  { label: 'GD - ProvisionEggs' },
  { label: 'GE - StatusFlowVist' },
  { label: 'GF - AccountingEntr' },
  { label: 'GG - GrainOrder' },
  { label: 'GH - GrainFreightor' },
  { label: 'GI - Requests' },
  { label: 'GJ - BlackBeltRecti' },
  { label: 'GK - BlockedOrdersR' },
  { label: 'GL - BlockedClients' },
  { label: 'GM - EndClosingInve' },
  { label: 'GN - InstallationPP' },
  { label: 'GO - AdvanceRequest' },
  { label: 'GP - MRPRegister' },
  { label: 'GQ - CustomerUnload' },
  { label: 'GR - Kilometer' },
  { label: 'GS - ExceptionEPIR' },
  { label: 'GT - Advance' },
  { label: 'GU - CorporateCar' },
  { label: 'GV - RouteExpense' },
  { label: 'GW - Fixed Fund' },
  { label: 'GX - SmallExpense' },
  { label: 'GY - Accountabili' },
  { label: 'GZ - EducationalI' },
  { label: 'HA - Advance/Turk' },
  { label: 'HB - Transference' },
  { label: 'HC - DemissionR' },
  { label: 'HD - SalaryUpda' },
  { label: 'HE - PositionCr' },
  { label: 'HF - PendingList' },
  { label: 'HG - PenddingAppr' },
  { label: 'HH - CreditCardRequ' },
  { label: 'HI - CreditCardInst' },
  { label: 'HJ - ContractsProcu' },
  { label: 'HK - MasterPlans' },
  { label: 'HL - Tributes' },
  { label: 'HM - SavAdvances' },
  { label: 'HN - SavAccountab' },
  { label: 'HO - SavSmallExpe' },
  { label: 'HP - Norms' },
  { label: 'HQ - RevogationNorm' },
  { label: 'HR - Sales' },
  { label: 'HS - OverDebits' },
  { label: 'HT - CriticalDocume' },
  { label: 'HU - ManageLegal' },
  { label: 'HV - Confronting' },
  { label: 'HW - UpdateMonthl' },
  { label: 'HX - GrainDigitalPN' },
  { label: 'HY - InvestmentOrde' },
  { label: 'HZ - ComplementaryF' },
  { label: 'IA - LMS' },
  { label: 'IB - MeshBreakage' },
  { label: 'IC - EggPayment' },
  { label: 'ID - BravoDocume' },
  { label: 'IE - FixedAssetsRep' },
  { label: 'IF - InventoryAppro' },
  { label: 'IG - InventoryDiffe' },
  { label: 'IH - Invoice' },
  { label: 'II - TicketSox' },
  { label: 'IJ - PlateChange' },
  { label: 'IK - LostingOfLosse' },
  { label: 'IL - ComplementsApp' },
  { label: 'IM - AdditionalPrim' },
  { label: 'IN - Circularizat' },
  { label: 'IO - DocumentGAN' },
  { label: 'IP - ComponentAppro' },
  { label: 'IQ - ApprovalDiverg' },
  { label: 'LC - LC' },
  { label: 'MR - MR' },
]