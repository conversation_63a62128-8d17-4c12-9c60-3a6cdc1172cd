import { AllProcessesTypes } from "@/components/AccordionTable";
import { create } from "zustand";

interface SelectedRowsState {
  selectedRows: Record<string, AllProcessesTypes[]>;
  addSelectedRows: (tableId: string, rows: AllProcessesTypes[]) => void;
  clearSelectedRows: () => void;
  cleared: boolean;
}

export const useSelectedRowsStore = create<SelectedRowsState>((set, get) => ({
  selectedRows: {},
  addSelectedRows: (tableId, rows) =>
    set((state) => {
      if (!rows.length) {
        const newSelectedRows = { ...state.selectedRows };
        delete newSelectedRows[tableId];
        return { selectedRows: newSelectedRows };
      }
      const updatedRows = {
        ...state.selectedRows,
        [tableId]: [...rows],
      };
      return {
        selectedRows: updatedRows,
      };
    }),
  clearSelectedRows: () => set({ selectedRows: {}, cleared: !get().cleared }),
  cleared: false,
}));
