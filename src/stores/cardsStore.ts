import { APIAriba, APICommercial, APIProcurement } from "@/api";
import { ApprovalOrigin } from "@/api/approval/approval.service";
import {
  AribaService,
  AvipamService,
  IntranetService,
  OneLogService,
  SapService,
  ServiceNowService,
} from "@/services";
import { DocuSignService } from "@/services/docuSign";
import { HRService } from "@/services/hr";
import { OracleService } from "@/services/oracle";
import { SeoDigitalService } from "@/services/seoDigital";
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { AllProcessesTypes } from "../components/AccordionTable";
import { processesProps } from "../constant";
import { ReturnProps } from "../constant/processesProps";
import {
  IAccordionTableProps,
  IIntranet,
  IOracle,
  IResponse,
  IUser,
} from "../interfaces";
import { AptusService } from "../services/aptus";
import { mock } from "../utils/mock";
import { handleLanguageSAP } from "../utils/ValidationLanguage";

export type ExtraParamsType = {
  language: string;
  cacheLiveTime: number;
  ecp: string;
  cacheActive: string;
  ano?: number;
  aprovador?: string;
  timeKeepCache?: string;
  [key: string]: any;
};

const activeMock = false && !!import.meta.env.DEV;
export const aribaPermissions = ["AT", "AP", "AR", "AS"];

interface CardsState {
  // Estado
  cards: IAccordionTableProps<any>[];
  loadingDataTable: boolean;
  reloadWithoutCache: boolean;
  aribaApiCalled: boolean;
  currentUser: IUser | undefined;

  // Ações
  setCards: (cards: IAccordionTableProps<any>[]) => void;
  setLoadingDataTable: (loading: boolean) => void;
  setReloadWithoutCache: (reload: boolean) => void;
  setCurrentUser: (user: IUser | undefined) => void;
  resetAribaApiCalled: () => void;

  // Funções principais
  processPermissions: (inputUser?: IUser) => any[];
  userHasAribaPermissions: (inputUser?: IUser) => boolean;
  handleExtraParams: (type: string) => any;
  execute: (
    processConfig: ReturnProps<AllProcessesTypes> | undefined,
    cache?: boolean,
    isAriba?: boolean
  ) => Promise<AllProcessesTypes>;
  handleCards: (user: IUser, cache?: boolean) => void;
  reloadCardsWithoutCache: (user: IUser) => void;
  updateDataAndCounter: (
    processId: string,
    newTotal: number,
    headerData: AllProcessesTypes[],
    detailData?: AllProcessesTypes[]
  ) => void;

  // Helpers internos
  extractHeaderDetailData: (
    responseHeader: AllProcessesTypes,
    responseDetail: AllProcessesTypes[],
    processConfig: ReturnProps<AllProcessesTypes>
  ) => { headerData: AllProcessesTypes[]; detailData: AllProcessesTypes[] };
  handleAribaResponse: (aribaData: any) => void;
  handleCardResponse: (
    response: AllProcessesTypes,
    processConfig?: ReturnProps<AllProcessesTypes>
  ) => Promise<void>;
}

export const useCardsStore = create<CardsState>()(
  subscribeWithSelector((set, get) => ({
    // Estado inicial
    cards: [] as IAccordionTableProps<any>[],
    loadingDataTable: true,
    reloadWithoutCache: false,
    aribaApiCalled: false,
    currentUser: undefined,

    // Ações básicas
    setCards: (cards) => set({ cards }),
    setLoadingDataTable: (loadingDataTable) => set({ loadingDataTable }),
    setReloadWithoutCache: (reloadWithoutCache) => set({ reloadWithoutCache }),
    setCurrentUser: (currentUser) => set({ currentUser }),
    resetAribaApiCalled: () => set({ aribaApiCalled: false }),

    // Função para atualizar o total de um card específico
    updateDataAndCounter(
      processId: string,
      newTotal: number,
      headerData: AllProcessesTypes[],
      detailData?: AllProcessesTypes[]
    ) {
      const { cards } = get();

      if (newTotal === 0) {
        // Remove cards with zero items
        const filteredCards = cards.filter(
          (card) => !(card.process === processId || card.type === processId)
        );
        set({ cards: filteredCards });
      } else {
        // Update counter for non-zero items
        const updatedCards = cards.map((card) => {
          // Identifica o card pelo process ou type (para caso especial AD)
          if (card.process === processId || card.type === processId) {
            return {
              ...card,
              headerData,
              detailData: detailData || [],
              total: newTotal,
            };
          }
          return card;
        });
        set({ cards: updatedCards });
      }
    },

    // Processamento de permissões
    processPermissions: (inputUser?: IUser) => {
      const { currentUser } = get();
      const targetUser = inputUser || currentUser;
      const userPermissions = targetUser?.permissionsApproval;

      if (!userPermissions?.length) {
        return [];
      }

      const updatedPermissionsToCards = userPermissions.flatMap(
        (permissionApproval: { process: string }) => {
          let newPermissions: any[] = [];

          if (targetUser?.processApproval?.length) {
            targetUser.processApproval.forEach((processApproval) => {
              if (processApproval.process === permissionApproval.process) {
                const newPermission = {
                  ...permissionApproval,
                  typeProcess:
                    processApproval.typeProcess === "CC"
                      ? "CC_APTUS"
                      : processApproval.typeProcess,
                };

                if (
                  !newPermissions.some(
                    (item) => item.typeProcess === processApproval.typeProcess
                  )
                ) {
                  newPermissions.push(newPermission);
                }
              }
            });
          } else {
            newPermissions.push(permissionApproval);

            // tipagem das permissoes que possuem varios processos
            const sameProcessTypeMapping: Record<string, string[]> = {
              CC: ["CC_SAP"],
              SUFA: ["SUFA_1", "SUFA_2", "SUFA_3", "SUFA_4"],
              JR: ["JR_MAN", "JR_CONF", "JR_CIRC", "JR_DEP"],
              TMS: ["TMS_1", "TMS_2", "TMS_3", "TMS_4", "TMS_5", "TMS_6"],
              SV: ["SV_1", "SV_2", "SV_3"],
              TR: ["TR_SAP", "TR_INTRANET"],
            };
            // Check if the permission process is in our mapping
            const processTypes =
              sameProcessTypeMapping[permissionApproval.process];

            if (processTypes) {
              // Filter out the original process permission
              newPermissions = newPermissions.filter(
                (item) => item.process !== permissionApproval.process
              );

              // Add the sub-process permissions
              newPermissions.push(
                ...processTypes.map((typeProcess) => ({
                  ...permissionApproval,
                  typeProcess,
                }))
              );
            }
          }

          return newPermissions;
        }
      );

      return targetUser?.processApproval?.length
        ? [...userPermissions, ...updatedPermissionsToCards]
        : updatedPermissionsToCards;
    },

    // Verificação de permissões Ariba
    userHasAribaPermissions: (inputUser?: IUser) => {
      const { currentUser } = get();
      const targetUser = inputUser || currentUser;
      return (
        targetUser?.permissionsApproval?.some((permission) =>
          aribaPermissions.includes(permission.process)
        ) || false
      );
    },
    handleExtraParams: (type: string) => {
      const { currentUser } = get();
      switch (type?.toLowerCase()) {
        case "confrontational":
          return {
            aprovador: currentUser?.initials,
          };
        default:
          return {};
      }
    },

    // Execução de APIs
    execute: async (
      processConfig: ReturnProps<AllProcessesTypes> | undefined,
      cache: boolean = true,
      isAriba: boolean = false
    ): Promise<AllProcessesTypes> => {
      const { currentUser } = get();
      const { origin = "", permission = "", type = "" } = processConfig ?? {};

      try {
        const language = handleLanguageSAP(currentUser?.language);
        let resp: IResponse.Default<AllProcessesTypes> = {
          data: [],
          success: false,
        };

        try {
          if (activeMock) {
            resp = {
              data: mock[type as keyof typeof mock],
              success: true,
            };
          } else {
            if (origin === "SAP") {
              const sapParams = {
                permission: permission || "",
                extraParams: {
                  ecp: ["H5", "AM"].includes(permission ?? "")
                    ? "true"
                    : "false",
                  cacheActive: `${cache}`,
                  language,
                  cacheLiveTime: 15,
                },
              };
              resp = await SapService.fetchAndPreparePendingApprovals(
                sapParams
              );
            } else if (isAriba) {
              resp = await APIAriba.getPendingApprovals(cache);
            } else if (origin === "COMMERCIAL") {
              resp = await APICommercial.pendingApprovals(type || "", cache);
            } else if (origin === "SERVICENOW") {
              resp = await ServiceNowService.fetchAndPreparePendingApprovals(
                cache
              );
            } else if (origin === "ORACLE") {
              resp = await OracleService.fetchAndPreparePendingApprovals(
                type as IOracle.OracleTypesProcess,
                cache
              );
            } else if (origin === "DOCUSIGN") {
              resp = await DocuSignService.getPendingApprovals(cache);
            } else if (origin === "APTUS") {
              resp = await AptusService.fetchAndPreparePendingApprovals(
                type || "",
                cache
              );
            } else if (origin === "SEODIGITAL") {
              resp = await SeoDigitalService.getPendingApprovals(cache);
            } else if (origin === "HR") {
              resp = await HRService.getPendingApprovals(type || "", cache);
            } else if (origin === "PROCUREMENT") {
              resp = await APIProcurement.getPendingApprovals(
                type || "",
                cache
              );
            } else if (origin === "AVIPAM") {
              resp = await AvipamService.fetchAndPreparePendingApprovals(cache);
            } else if (origin === "ONELOG") {
              resp = await OneLogService.fetchAndPreparePendingApprovals(cache);
            } else if (origin === "INTRANET") {
              resp = await IntranetService.fetchAndPreparePendingApprovals(
                (type as IIntranet.ProcessKey) || "",
                cache
              );
            }
          }

          if (Array.isArray(resp.data)) {
            resp.data.forEach((item) => {
              item.origin = origin;
              item.type = type;
              item.process = permission;
            });
          }
          return resp;
        } catch (error) {
          console.error(`Error processing ${processConfig?.title}:`, error);
          return { data: [], success: false };
        }
      } catch (e) {
        return { data: [], success: false };
      }
    },

    // Helper para extrair dados
    extractHeaderDetailData: (
      responseHeader: AllProcessesTypes,
      responseDetail: AllProcessesTypes[],
      processConfig: ReturnProps<AllProcessesTypes>
    ) => {
      if (!responseHeader) return { headerData: [], detailData: [] };

      const headerData: AllProcessesTypes[] =
        responseHeader?.RETORNO || responseHeader?.data || responseHeader || [];

      const detailData: AllProcessesTypes[] =
        (processConfig?.detailColumns || processConfig?.documentDetailHtml) &&
        responseDetail?.length
          ? responseDetail
          : [];

      return { headerData, detailData };
    },

    // Handler para resposta Ariba
    handleAribaResponse: (aribaData: any) => {
      const aribaProcessMapping = {
        AS: "sourcing",
        AP: "project",
        AT: "contract",
        AR: "requisition",
      };

      for (const aribaType of aribaPermissions) {
        const dataSource =
          aribaProcessMapping[aribaType as keyof typeof aribaProcessMapping];
        if (!dataSource || !aribaData[dataSource]?.tasks?.length) continue;

        const tasksData = aribaData[dataSource].tasks.map((task: any) => ({
          ...task.detail,
          description: task.description,
          type: aribaType,
          origin: "ARIBA" as ApprovalOrigin,
        }));

        const permissionAribaHeaderData =
          AribaService.prepareTableData(tasksData);

        if (permissionAribaHeaderData.length > 0) {
          const specificProcessConfig = processesProps(aribaType)[0];

          const { cards, setCards } = get();

          // Check if we already have this card type
          if (!cards.some((card) => card.process === aribaType)) {
            const aribaCard: IAccordionTableProps<any> = {
              headerData: permissionAribaHeaderData,
              process: aribaType,
              title: specificProcessConfig?.title || "",
              type: specificProcessConfig?.type || aribaType,
              total: permissionAribaHeaderData.length,
            };

            // Create a new array with all existing cards plus the new one, then sort
            const newCards = [...cards, aribaCard].sort((a, b) =>
              a.title.localeCompare(b.title)
            );

            // Set the cards directly
            setCards(newCards);
          }
        }
      }
    },
    // Handler para resposta de cards
    handleCardResponse: async (
      response: AllProcessesTypes,
      processConfig?: ReturnProps<AllProcessesTypes>
    ): Promise<void> => {
      if (!response || !processConfig) return;

      const { extractHeaderDetailData, handleAribaResponse, setCards, cards } =
        get();

      const { headerData, detailData } = extractHeaderDetailData(
        response.data,
        response.detailData ?? response.data,
        processConfig
      );

      if (processConfig.origin === "ARIBA" && headerData) {
        return handleAribaResponse(headerData);
      }

      if (!headerData?.length && !detailData?.length) return;

      const alreadyHasProcess = cards.some((card) => {
        return card.type === processConfig.type;
      });

      if (!alreadyHasProcess) {
        const newCard = {
          headerData: headerData || [],
          detailData: detailData || [],
          process: processConfig.permission || "",
          title: processConfig.title || "",
          type: processConfig.type || "",
          origin: processConfig.origin || "",
          total: headerData.length,
        };

        const newCards = [...cards, newCard].sort((a, b) =>
          a.title.localeCompare(b.title)
        );

        setCards(newCards);
      }
    },

    // Função principal para buscar cards
    handleCards: async (user: IUser, cache: boolean = true) => {
      const {
        setLoadingDataTable,
        processPermissions,
        userHasAribaPermissions,
        execute,
        handleCardResponse,
      } = get();

      setLoadingDataTable(true);

      try {
        const finalPermissionsToCards = user ? processPermissions(user) : [];

        if (!finalPermissionsToCards?.length) {
          setLoadingDataTable(false);
          return;
        }

        const allPromises: Promise<void>[] = [];

        for (const permission of finalPermissionsToCards) {
          if (!permission.active) continue;

          if (
            ["AP", "AR", "AS", "AT"].includes(permission.process) &&
            userHasAribaPermissions(user) &&
            !get().aribaApiCalled
          ) {
            set({ aribaApiCalled: true });
            const aribaConfig = processesProps(permission.process)[0];
            const promise = execute(aribaConfig, cache, true)
              .then((response) => handleCardResponse(response, aribaConfig))
              .catch(console.error);
            allPromises.push(promise);
            continue;
          }

          if (permission.typeProcess) {
            const processApprovalConfig = processesProps(
              permission.typeProcess
            )[0];
            const promise = execute(processApprovalConfig, cache)
              .then((response) =>
                handleCardResponse(response, processApprovalConfig)
              )
              .catch(console.error);
            allPromises.push(promise);
          } else if (permission.process === "AD" && !permission.typeProcess) {
            const aptusTurkey = ["PC_TK", "AD_TK", "PD_TK"];
            for (const tk of aptusTurkey) {
              const tkConfig = processesProps(tk)[0];
              const promise = execute(tkConfig, cache)
                .then((response) => handleCardResponse(response, tkConfig))
                .catch(console.error);
              allPromises.push(promise);
            }
            continue;
          } else if (
            !["AP", "AR", "AS", "AT", "AD"].includes(permission.process) &&
            !permission.typeProcess
          ) {
            const processConfig = processesProps(permission.process)[0];
            const promise = execute(processConfig, cache)
              .then((response) => handleCardResponse(response, processConfig))
              .catch(console.error);
            allPromises.push(promise);
          }
        }

        Promise.all(allPromises)
          .catch(console.error)
          .finally(() => setLoadingDataTable(false));
      } catch (e) {
        console.error("Error in handleCards:", e);
        setLoadingDataTable(false);
      }
    },

    // Reload sem cache
    reloadCardsWithoutCache: (user: IUser) => {
      const {
        setCards,
        setReloadWithoutCache,
        setLoadingDataTable,
        userHasAribaPermissions,
        handleCards,
      } = get();

      try {
        setCards([]);
        setReloadWithoutCache(true);

        if (userHasAribaPermissions(user)) {
          set({ aribaApiCalled: false });
        }

        setLoadingDataTable(true);

        setTimeout(() => {
          handleCards(user, false);
        }, 0);
      } catch (e) {
        console.error("Error in reloadCardsWithoutCache:", e);
      } finally {
        setReloadWithoutCache(false);
      }
    },
  }))
);

export const useCards = (user?: IUser) => {
  const store = useCardsStore();

  return {
    cards: store.cards,
    currentUser: store.currentUser,
    setCards: store.setCards,
    execute: store.execute,
    handleCards: store.handleCards,
    handleExtraParams: store.handleExtraParams,
    loadingDataTable: store.loadingDataTable,
    reloadWithoutCache: store.reloadWithoutCache,
    setReloadWithoutCache: store.setReloadWithoutCache,
    reloadCardsWithoutCache: store.reloadCardsWithoutCache,
    updateDataAndCounter: store.updateDataAndCounter,
    resetAribaApiCalled: store.resetAribaApiCalled,
    setCurrentUser: store.setCurrentUser,
  };
};
