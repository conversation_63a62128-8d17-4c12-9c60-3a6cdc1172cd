import { create } from "zustand";

interface OnelogState {
  reason?: string;
  subReason?: string;
  setReason: (reason?: string) => void;
  setSubReason: (subReason?: string) => void;
}

const useOnelogStore = create<OnelogState>((set) => ({
  reason: undefined,
  subReason: undefined,
  setReason: (reason) => set({ reason }),
  setSubReason: (subReason) => set({ subReason }),
}));

export default useOnelogStore;
