import { FC, useCallback, useMemo, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { IAriba } from "../../interfaces";

import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { Box } from "@mui/material";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ASSubcategoryAccordion } from "./components/ASSubcategoryAccordion";
import { ApprovalOrigin } from "@/api/approval/approval.service";

export const DataTableAriba: FC<{
  headerData: IAriba.AribaItemType[];
  process: string;
}> = ({ headerData, process }) => {
  const { headerColumns, documentDetailHtml, origin } =
    processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();
  const { selectedRows } = useSelectedRowsStore();

  const { approveReproveDocument, isLoading } =
    useApprovalDocument<IAriba.ApprovalReprovalParams>();

  const [expandedRows, setExpandedRows] = useState<IAriba.AribaItemType[]>([]);

  const [disapprovalModalState, setDisapprovalModalState] = useState<{
    open: boolean;
    row: IAriba.AribaItemType | null;
  }>({
    open: false,
    row: null,
  });

  // Handle approval/rejection for Ariba documents
  const handleApprovalReprovalDocument = useCallback(
    async (row: IAriba.AribaItemType, status: "A" | "D", reason?: string) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [process, origin]
  );

  const handleOpenDisapprovalModal = (row: IAriba.AribaItemType) => {
    setDisapprovalModalState({
      open: true,
      row,
    });
  };

  const handleCloseDisapprovalModal = () => {
    setDisapprovalModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmDisapprovalModal = async (reason: string) => {
    if (disapprovalModalState.row) {
      await handleApprovalReprovalDocument(
        disapprovalModalState.row,
        "D",
        reason
      );
      handleCloseDisapprovalModal();
    }
  };

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (columns: TableColumn<IAriba.AribaItemType>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: IAriba.AribaItemType) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<IAriba.AribaItemType>
                    row={row}
                    onApprove={() => handleApprovalReprovalDocument(row, "A")}
                    onDisapprove={() => handleOpenDisapprovalModal(row)}
                    showOpenModalButton={false}
                    showOpenUrlButton={Boolean(row.fullURL)}
                    openUrl={row.fullURL}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleApprovalReprovalDocument,
      handleOpenDisapprovalModal,
      user.onlyReading,
    ]
  );

  addActionColumn(headerColumns as TableColumn<IAriba.AribaItemType>[]);

  // Componente para modal e loading
  const renderModalAndLoading = useCallback(
    () => (
      <>
        <ModalApprovalReason
          open={disapprovalModalState.open}
          onClose={handleCloseDisapprovalModal}
          title={t("Disapprove")}
          subtitle={t("Disapproval.Reason")}
          onConfirm={handleConfirmDisapprovalModal}
          confirmButtonText={t("Disapprove")}
          confirmButtonColor="error"
          isRequired={true}
        />
        {isLoading && <Loading open={true} />}
      </>
    ),
    [
      disapprovalModalState.open,
      handleCloseDisapprovalModal,
      handleConfirmDisapprovalModal,
      isLoading,
      t,
    ]
  );

  const handleRowExpansion = useCallback(
    (row: IAriba.AribaItemType) => {
      if (documentDetailHtml) {
        try {
          const content = documentDetailHtml(row);
          return (
            <div
              style={{
                padding: "16px",
                position: "relative",
              }}
              data-row-id={row.id}
            >
              {content}
            </div>
          );
        } catch (error) {
          console.error("Error rendering expandable content:", error);
        }
      }
      return null;
    },
    [documentDetailHtml]
  );

  // Props comuns para todas as instâncias do MainDataTable
  const commonTableProps = useMemo(
    () => ({
      rowKey: (row: IAriba.AribaItemType) => row.id,
      columns: headerColumns as TableColumn<IAriba.AribaItemType>[],
      expandableRows: Boolean(documentDetailHtml),
      onRowExpandToggled: (expanded: boolean, row: IAriba.AribaItemType) => {
        if (expanded) {
          setExpandedRows((prev) => [...prev, row]);
        } else {
          setExpandedRows((prev) => prev.filter((item) => item.id !== row.id));
        }
      },
      expandableRowExpanded: (row: IAriba.AribaItemType) =>
        expandedRows.some((item) => item.id === row.id),
      expandableRowsComponent: ({ data }: { data: IAriba.AribaItemType }) => {
        if (
          documentDetailHtml &&
          expandedRows.some((item) => item.id === data.id)
        ) {
          return handleRowExpansion(data);
        }
        return null;
      },
    }),
    [
      documentDetailHtml,
      expandedRows,
      handleRowExpansion,
      addActionColumn,
      headerColumns,
      selectedRows,
    ]
  );

  // Função para segregar subcategorias do AS conforme método antigo
  // Este método é exclusivo para o processo AS
  const getASSubcategories = (responseData: IAriba.ItemPropsAS[]) => {
    if (!responseData) return [];

    return [
      {
        key: "emergency",
        title: t("Emergency.Pn.AS"),
        color: "#E21F26",
        data: responseData.filter((item) =>
          item?.workspaceDetails?.customFields
            .find((f) => f?.fieldName === "cus_GraudaSolicitcao")
            ?.fieldValue?.includes("Regularização")
        ),
      },
      {
        key: "restrictedSupplier",
        title: t("Restricted.Supplier.AS"),
        color: "#F58220",
        data: responseData.filter(
          (item) =>
            !item?.workspaceDetails?.customFields
              .find((f) => f?.fieldName === "cus_GraudaSolicitcao")
              ?.fieldValue?.includes("Regularização") &&
            (item?.workspaceDetails?.customFields
              .find((f) => f?.fieldName === "cus_TipodeFornecedor")
              ?.fieldValue?.includes("Restrito") ||
              item?.workspaceDetails?.customFields
                .find((f) => f?.fieldName === "cus_TipodeFornecedor")
                ?.fieldValue?.includes("Escolhido"))
        ),
      },
      {
        key: "normal",
        title: t("Normal.Urgent.AS"),
        color: "#0066B2",
        data: responseData.filter(
          (item) =>
            !item?.workspaceDetails?.customFields
              .find((f) => f?.fieldName === "cus_GraudaSolicitcao")
              ?.fieldValue?.includes("Regularização") &&
            !(
              item?.workspaceDetails?.customFields
                .find((f) => f?.fieldName === "cus_TipodeFornecedor")
                ?.fieldValue?.includes("Restrito") ||
              item?.workspaceDetails?.customFields
                .find((f) => f?.fieldName === "cus_TipodeFornecedor")
                ?.fieldValue?.includes("Escolhido")
            )
        ),
      },
    ];
  };

  // Memoizar subcategorias do AS
  const subcategoriesAS = useMemo(
    () =>
      process === "AS"
        ? getASSubcategories(headerData as unknown as IAriba.ItemPropsAS[])
        : [],
    [process, headerData]
  );

  if (process === "AS") {
    return (
      <Box sx={{ "*:not(:last-child)": { marginBottom: "0.5rem" } }}>
        {subcategoriesAS.map((subcategory) => (
          <ASSubcategoryAccordion
            key={subcategory.key}
            subcategory={subcategory}
            commonTableProps={commonTableProps}
          />
        ))}
        {renderModalAndLoading()}
      </Box>
    );
  }

  // Render padrão para outros processos
  return (
    <div className="ariba-data-table-container">
      <MainDataTable
        key={`ariba-table-${process}`}
        {...commonTableProps}
        data={headerData}
      />
      {renderModalAndLoading()}
    </div>
  );
};
