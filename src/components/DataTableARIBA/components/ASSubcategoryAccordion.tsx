import { MainDataTable } from "@/components/DataTable/MainDataTable";
import { IAriba } from "@/interfaces";
import { FC } from "react";
import {
  StyledAccordion,
  StyledAccordionDetails,
  StyledAccordionSummary,
} from "../styles";

// Interface para as subcategorias do AS
interface ASSubcategory {
  key: string;
  title: string;
  color: string;
  data: IAriba.ItemPropsAS[];
}

// Componente para o Accordion de subcategoria do AS
export const ASSubcategoryAccordion: FC<{
  subcategory: ASSubcategory;
  commonTableProps: any;
}> = ({ subcategory, commonTableProps }) => {
  if (subcategory.data.length === 0) return null;

  return (
    <StyledAccordion backgroundColor={subcategory.color} defaultExpanded>
      <StyledAccordionSummary>{subcategory.title}</StyledAccordionSummary>
      <StyledAccordionDetails>
        <MainDataTable
          key={`subcat-table-${subcategory.key}`}
          {...commonTableProps}
          data={subcategory.data}
        />
      </StyledAccordionDetails>
    </StyledAccordion>
  );
};
