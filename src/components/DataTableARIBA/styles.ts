import { Accordion, AccordionDetails, AccordionSummary } from "@mui/material";
import { styled } from "@mui/material/styles";

interface StyledAccordionProps {
  backgroundColor?: string;
}

export const StyledAccordion = styled(Accordion)<StyledAccordionProps>(
  ({ theme, backgroundColor }) => ({
    background: backgroundColor,
    padding: "0.5rem",
    boxShadow: "none",
    "&:before": { display: "none" },
    "*:not(:last-child)": {
      marginBottom: "0.5rem",
    },
  })
);

export const StyledAccordionSummary = styled(AccordionSummary)({
  color: "#fff",
  fontWeight: "bold",
  fontSize: 16,
  minHeight: 48,
  "& .MuiAccordionSummary-content": {
    margin: 0,
  },
});

export const StyledAccordionDetails = styled(AccordionDetails)({
  backgroundColor: "#fff",
  borderRadius: "10px",
  margin: "5px",
});
