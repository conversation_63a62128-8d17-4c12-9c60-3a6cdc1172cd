import { Checkbox, CheckboxProps } from "@mui/material";
import { FC } from "react";

interface HierarchicalCheckboxProps extends Omit<CheckboxProps, 'indeterminate' | 'onChange'> {
  checked: boolean;
  indeterminate: boolean;
  onChange: (checked: boolean) => void;
}

/**
 * Checkbox base com três estados para seleção hierárquica
 * 
 * Estados possíveis:
 * - ✓ checked: Totalmente selecionado
 * - - indeterminate: Parcialmente selecionado  
 * - ☐ unchecked: Não selecionado
 * 
 * ⚠️ COMPONENTE BASE - Pode ser usado por qualquer DataTable hierárquica
 */
export const HierarchicalCheckbox: FC<HierarchicalCheckboxProps> = ({
  checked,
  indeterminate,
  onChange,
  ...props
}) => {
  return (
    <Checkbox
      checked={checked}
      indeterminate={indeterminate}
      onChange={(event) => onChange(event.target.checked)}
      color="secondary"
      size="small"
      sx={{
        color: '#812990',
        '&.Mui-checked': {
          color: '#812990',
        },
        '&.MuiCheckbox-indeterminate': {
          color: '#812990',
        },
        '& .MuiSvgIcon-root': {
          fontSize: 20,
        },
      }}
      {...props}
    />
  );
}; 