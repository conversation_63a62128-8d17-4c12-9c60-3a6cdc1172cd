import { createContext, useContext } from "react";
import { createStore } from "zustand";

export interface HierarchicalNode<T = unknown> {
  id: string;
  parentId?: string;
  children?: string[];
  data: T;
}

interface SelectionState {
  checked: boolean;
  indeterminate: boolean;
}

interface HierarchicalSelectionState {
  nodes: Record<string, HierarchicalNode>;
  selections: Record<string, SelectionState>;
  changeCounter: number;

  setNodes: (nodes: HierarchicalNode[]) => void;
  toggleSelection: (nodeId: string) => void;
  clearSelections: () => void;

  getSelectedNodes: () => string[];
  getSelectedData: () => unknown[];
  isNodeSelected: (nodeId: string) => boolean;
  isNodeIndeterminate: (nodeId: string) => boolean;
}

type HierarchicalSelectionStore = ReturnType<
  typeof createHierarchicalSelectionStore
>;

export const HierarchicalSelectionContext =
  createContext<HierarchicalSelectionStore | null>(null);

export const useHierarchicalSelectionStore = () => {
  const store = useContext(HierarchicalSelectionContext);
  if (!store) {
    throw new Error(
      "useHierarchicalSelectionStore deve ser usado dentro de HierarchicalSelectionProvider"
    );
  }
  return store;
};

export const createHierarchicalSelectionStore = () =>
  createStore<HierarchicalSelectionState>((set, get) => ({
    nodes: {},
    selections: {},
    changeCounter: 0,

    setNodes: (nodes) => {
      const nodesMap: Record<string, HierarchicalNode> = {};

      nodes.forEach((node) => {
        nodesMap[node.id] = { ...node, children: [] };
      });

      nodes.forEach((node) => {
        if (node.parentId && nodesMap[node.parentId]) {
          nodesMap[node.parentId].children!.push(node.id);
        }
      });

      set({
        nodes: nodesMap,
        selections: {},
        changeCounter: get().changeCounter + 1,
      });
    },

    toggleSelection: (nodeId) => {
      const { nodes, selections } = get();
      const node = nodes[nodeId];
      if (!node) return;

      const currentSelection = selections[nodeId] || {
        checked: false,
        indeterminate: false,
      };
      const newChecked = !currentSelection.checked;

      const newSelections = { ...selections };
      newSelections[nodeId] = { checked: newChecked, indeterminate: false };

      if (node.children && node.children.length > 0) {
        node.children.forEach((childId) => {
          newSelections[childId] = {
            checked: newChecked,
            indeterminate: false,
          };
        });
      }

      const updateParentSelection = (parentId: string) => {
        const parent = nodes[parentId];
        if (parent && parent.children) {
          const allChildrenChecked = parent.children.every(
            (childId) => newSelections[childId]?.checked
          );
          const someChildrenChecked = parent.children.some(
            (childId) => newSelections[childId]?.checked
          );

          newSelections[parentId] = {
            checked: allChildrenChecked,
            indeterminate: someChildrenChecked && !allChildrenChecked,
          };

          if (parent.parentId) {
            updateParentSelection(parent.parentId);
          }
        }
      };

      if (node.parentId) {
        updateParentSelection(node.parentId);
      }

      set({
        selections: newSelections,
        changeCounter: get().changeCounter + 1,
      });
    },

    clearSelections: () => {
      set({ selections: {}, changeCounter: get().changeCounter + 1 });
    },

    getSelectedNodes: () => {
      const { selections } = get();
      return Object.entries(selections)
        .filter(([, state]) => state.checked)
        .map(([id]) => id);
    },

    getSelectedData: () => {
      const { nodes, selections } = get();
      return Object.entries(selections)
        .filter(([, state]) => state.checked)
        .map(([id]) => nodes[id]?.data)
        .filter(Boolean);
    },

    isNodeSelected: (nodeId) => {
      const { selections } = get();
      return selections[nodeId]?.checked || false;
    },

    isNodeIndeterminate: (nodeId) => {
      const { selections } = get();
      return selections[nodeId]?.indeterminate || false;
    },
  }));
