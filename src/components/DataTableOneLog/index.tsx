import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { IProcurementApprovalReprovalParams } from "@/interfaces/procurement";
import useOnelogStore from "@/stores/onelogStore";
import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { FC, startTransition, useCallback, useMemo, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { APIOnelog } from "../../api";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { IOnelog } from "../../interfaces";
import { OneLogService } from "../../services";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import { SecondaryDataTable } from "../DataTable/SecondaryDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableOneLog: FC<{
  headerData: IOnelog.ClientProps[] | any;
  detailData?: IOnelog.ClientProps[] | any;
  process: string;
}> = ({ headerData, detailData, process }) => {
  const {
    headerColumns,
    detailColumns,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    approveItems,
    origin,
  } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const { reason, setReason, subReason, setSubReason } = useOnelogStore();

  const { selectedRows } = useSelectedRowsStore();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: IOnelog.ItemDetailProps;
  }>({ open: false, data: {} as IOnelog.ItemDetailProps });
  const [expandedRows, setExpandedRows] = useState<IOnelog.ClientProps[]>([]);

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: IOnelog.AprovacoesProps | null;
    isApproveFromDetailModal: boolean;
  }>({
    open: false,
    type: null,
    row: null,
    isApproveFromDetailModal: false,
  });

  const { isLoading: isLoadingApprovalReprovalFromModal, mutate } = useMutation(
    async ({
      status,
      id,
      reasonReproval,
    }: {
      status: "A" | "R";
      id: string;
      reasonReproval: string;
    }) => {
      const data = await APIOnelog.approvalReprovalDetailDocument(
        status,
        id,
        reason ?? "",
        subReason ?? "",
        user.employeeID,
        reasonReproval
      );

      return data;
    }
  );

  const handleOpenModal = (data: IOnelog.ItemDetailProps) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: {} as IOnelog.ItemDetailProps });
  };

  const handleModalApprove = async (data: IOnelog.AprovacoesProps[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleApprovalReprovalDocument(row, "A", true);
    handleCloseModal();
  };

  const handleModalReject = (data: IOnelog.AprovacoesProps[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenApprovalModal(row, "disapprove", true);
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: IOnelog.AprovacoesProps,
    type: "approve" | "disapprove",
    isApproveFromDetailModal: boolean = false
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
      isApproveFromDetailModal,
    });
  };

  const handleCloseReprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
      isApproveFromDetailModal: false,
    });
  };

  const handleConfirmReprovalModal = async (reason: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      await handleApprovalReprovalDocument(
        approvalModalState.row,
        "R",
        approvalModalState.isApproveFromDetailModal,
        reason
      );
      handleCloseReprovalModal();
    }
  };

  const { approveReproveDocument, isLoading } =
    useApprovalDocument<IProcurementApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: IOnelog.AprovacoesProps,
      status: "A" | "R",
      isApproveFromDetailModal: boolean = false,
      reason: string = ""
    ) => {
      console.log(isApproveFromDetailModal, "isApproveFromDetailModal");
      if (isApproveFromDetailModal) {
        mutate({
          status,
          id: row.id,
          reasonReproval: reason,
        });
      }

      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status: status,
        reason,
      });
    },
    [process, origin, t]
  );

  const renderExpandableRowsComponent = useCallback(
    ({ data }: { data: IOnelog.ClientProps }) => {
      const row = data as IOnelog.ClientProps;
      if (!detailData?.length || !detailColumns) return null;

      const documentDetails = OneLogService.filterDocumentDetails(
        detailData,
        row.nome
      );

      const memoizedColumns = useMemo(
        () => detailColumns as TableColumn<IOnelog.AprovacoesProps>[],
        [detailColumns]
      );

      const handleSelectedRowsChange = useCallback(
        ({ selectedRows }: { selectedRows: IOnelog.AprovacoesProps[] }) => {
          setSelectedDetailRows(selectedRows);
        },
        []
      );

      const checkSelectedRow = useCallback(
        (detailRow: IOnelog.AprovacoesProps) => {
          return (
            Object.values(selectedRows || {})
              .flat()
              .some((item) => {
                return item.aprovacoes.some(
                  (aprovacao: IOnelog.AprovacoesProps) =>
                    aprovacao.id === detailRow.id
                );
              }) ??
            Object.values(selectedRows || {})
              .flat()
              .some((item) => {
                return item.id === detailRow.id;
              })
          );
        },
        [selectedRows]
      );

      return (
        <SecondaryDataTable
          columns={memoizedColumns}
          data={documentDetails}
          selectableRows={true}
          selectableRowSelected={checkSelectedRow}
          onSelectedRowsChange={handleSelectedRowsChange}
        />
      );
    },
    [detailData, detailColumns, selectedRows]
  );

  const handleDataToModal = useCallback((row: IOnelog.AprovacoesProps) => {
    const documentNumber = row.id;

    startTransition(() => {
      OneLogService.getDocumentDetails(documentNumber)
        .then((data) => {
          if (data) {
            handleOpenModal(data);
            setReason(data?.motivo || data?.motivos[0].descricao);
            setSubReason(data?.subMotivo);
          }
        })
        .catch((error) => console.error(error));
    });
  }, []);

  const addActionColumn = useCallback(
    (columns: TableColumn<IOnelog.AprovacoesProps>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: IOnelog.AprovacoesProps) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<IOnelog.AprovacoesProps>
                    row={row}
                    onApprove={async () => {
                      handleApprovalReprovalDocument(row, "A");
                    }}
                    onDisapprove={() => {
                      handleOpenApprovalModal(row, "disapprove");
                    }}
                    showOpenModalButton={hasDetailModal}
                    onOpenModal={handleDataToModal}
                    showOpenUrlButton={false}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      user.onlyReading,
      process,
    ]
  );

  if (approveItems && detailColumns) {
    addActionColumn(detailColumns as TableColumn<IOnelog.AprovacoesProps>[]);
  }

  return (
    <div>
      <MainDataTable
        expandedRows={expandedRows}
        rowKey={(row: IOnelog.ClientProps) => row.nome}
        isItemApprove={true}
        columns={headerColumns as TableColumn<IOnelog.ClientProps>[]}
        data={headerData}
        expandableRows={!!detailData?.length}
        onRowExpandToggled={(expanded: boolean, row: IOnelog.ClientProps) => {
          if (expanded) {
            setExpandedRows((prev) => [...prev, row]);
          } else {
            setExpandedRows((prev) =>
              prev.filter((expandedRow) => expandedRow.nome !== row.nome)
            );
          }
        }}
        expandableRowsComponent={renderExpandableRowsComponent}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseReprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmReprovalModal}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor={
          approvalModalState.type === "approve" ? "success" : "error"
        }
        isRequired={approvalModalState.type === "disapprove"}
      />

      <Loading open={isLoading || isLoadingApprovalReprovalFromModal} />
    </div>
  );
};
