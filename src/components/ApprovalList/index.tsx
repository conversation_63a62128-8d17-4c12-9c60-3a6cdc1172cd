import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
} from "@mui/material";
import { useTranslation } from "react-i18next";


export const ApprovalList = ({ items = [] }) => {
  const { t } = useTranslation();

  if (items.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: "center" }}>
        <Typography variant="body2" color="textSecondary">
          {t("No approvals found")}
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer component={Paper} elevation={2}>
      <Table sx={{ minWidth: 650 }} aria-label="approvals table">
        <TableHead>
          <TableRow>
            <TableCell>{t("EmployeeId")}</TableCell>
            <TableCell>{t("AccountId")}</TableCell>
            <TableCell>{t("Process")}</TableCell>
            <TableCell>{t("Document")}</TableCell>
            <TableCell>{t("Action")}</TableCell>
            <TableCell>{t("Origin")}</TableCell>
            <TableCell>{t("Ip")}</TableCell>
            <TableCell>{t("Created")}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id} hover>
              <TableCell>{item.employeeId}</TableCell>
              <TableCell>{item.accountId}</TableCell>
              <TableCell>{item.process}</TableCell>
              <TableCell>{item.document}</TableCell>
              <TableCell>{item.action}</TableCell>
              <TableCell>{item.origin}</TableCell>
              <TableCell>{item.ip}</TableCell>
              <TableCell>
                {item.created
                  ? new Date(item.created).toLocaleString("pt-BR")
                  : ""}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
