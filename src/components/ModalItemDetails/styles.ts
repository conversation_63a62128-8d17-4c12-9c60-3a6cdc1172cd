/** @jsxImportSource @emotion/react */
import styled from "@emotion/styled";

// Tabela responsiva: 2 colunas no mobile
export const ResponsiveTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  td {
    vertical-align: top;
    padding: 8px 4px;
    border: none;
  }

  td > span {
    display: block;
    margin-bottom: 2px;
    font-size: 13px;
    color: #888;
  }

  .detailModalContent {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    width: 100%;
  }

  @media (max-width: 748px) {
    display: block;
    tbody {
      display: block;
      width: 100%;
    }
    tr {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      margin-bottom: 8px;
    }
    td {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      flex: 1 1 50%;
      min-width: 50%;
      box-sizing: border-box;
      padding: 8px 4px;
      border: none;
    }
    .detailModalContent {
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      width: 100%;
    }
  }
`;
