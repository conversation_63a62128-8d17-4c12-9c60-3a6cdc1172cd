import styled from "@emotion/styled";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent as Dialog<PERSON>ontentMui,
  DialogTitle,
} from "@mui/material";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { AllProcessesTypes } from "../AccordionTable";
import { ResponsiveTable } from "./styles";

interface ModalItemDetailsProps {
  open: boolean;
  onClose: () => void;
  modalTitle: string;
  origin: string;
  data: AllProcessesTypes;
  row?: AllProcessesTypes;
  detailModalHeader?: (row: AllProcessesTypes) => React.ReactNode | null;
  detailModalContent?: (row: AllProcessesTypes) => React.ReactNode | null;
  onApprove?: (data: AllProcessesTypes) => void;
  onReject?: (data: AllProcessesTypes) => void;
}

export const ModalItemDetails: React.FC<ModalItemDetailsProps> = ({
  open,
  onClose,
  data,
  row,
  modalTitle,
  origin,
  detailModalHeader,
  detailModalContent,
  onApprove,
  onReject,
}) => {
  const { t } = useTranslation();

  const handleApprove = () => {
    if (onApprove) {
      if (row) {
        onApprove(row);
        return;
      }
      onApprove(data);
    }
  };

  const handleReject = () => {
    if (onReject) {
      if (row) {
        onReject(row);
        return;
      }
      onReject(data);
    }
  };

  const headerContent = useMemo(() => {
    if (detailModalHeader && data) {
      const headerResult = detailModalHeader(data);
      if (headerResult instanceof Promise) {
        headerResult.then(() => {
          return <ResponsiveTable>{headerResult}</ResponsiveTable>;
        });
      } else {
        return <ResponsiveTable>{headerResult}</ResponsiveTable>;
      }
    }

    if (origin === "SERVICENOW") {
      const detailSnow = data.replace(
        "<div class='page-header'><span class='titulo h3'>ServiceNow</span></div>",
        ""
      );
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: detailSnow,
          }}
        />
      );
    }
  }, [detailModalHeader, data, origin]);

  const bodyContent = useMemo(() => {
    if (origin !== "DOCUSIGN" && detailModalContent && data) {
      const contentResult = detailModalContent(data);

      if (contentResult instanceof Promise) {
        contentResult.then(() => {
          return contentResult;
        });
      } else {
        return contentResult;
      }
    }

    if (origin === "DOCUSIGN" && detailModalContent && data) {
      const contentResult = detailModalContent(data);

      return <div>{contentResult}</div>;
    }
  }, [detailModalContent, data]);

  const DialogContent = styled(DialogContentMui)`
    font-family: Roboto, sans-serif;

    // "& .tabletitle": {
    //   display: block;
    //   width: 100%;
    //   font-size: 14px;
    //   color: rgba(0, 0, 0, 0.54);
    //   margin-bottom: 4px;
    //   line-height: 160%;
    // }
  `;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <DialogTitle id="modal-title">{t(modalTitle)}</DialogTitle>
      <DialogContent id="modal-description" dividers>
        <>
          {headerContent && (
            <Card>
              <CardContent
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.02)",
                  borderRadius: "10px",
                  overflowX: "auto",
                }}
              >
                {headerContent}
              </CardContent>
            </Card>
          )}
          {bodyContent && origin !== "DOCUSIGN" && (
            <CardContent sx={{ paddingX: "0px !important" }}>
              <ResponsiveTable>{bodyContent}</ResponsiveTable>
            </CardContent>
          )}
          {origin === "DOCUSIGN" && (
            <CardContent sx={{ paddingX: "0px !important" }}>
              {bodyContent}
            </CardContent>
          )}
        </>
      </DialogContent>
      <DialogActions
        sx={{
          display: "flex",
          justifyContent: "space-between",
          padding: "1rem",
        }}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={onClose}
          sx={{ borderRadius: "30px" }}
          autoFocus
        >
          Fechar
        </Button>
        <Box display="flex" gap={1}>
          <Button
            variant="contained"
            color="error"
            sx={{ borderRadius: "30px" }}
            onClick={handleReject}
            disabled={!onReject}
            autoFocus
          >
            Reprovar
          </Button>
          <Button
            variant="contained"
            color="success"
            sx={{ borderRadius: "30px" }}
            onClick={handleApprove}
            disabled={!onApprove}
            autoFocus
          >
            Aprovar
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};
