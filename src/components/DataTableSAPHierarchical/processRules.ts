import { processesProps } from "@/constant";

export interface ProcessRule {
  id: string;
  name: string;
  description: string;

  // Configurações de ações
  parentActions: {
    showApproveButton: boolean;
    showDisapproveButton: boolean;
    showOpenModalButton: boolean;
    showOpenUrlButton: boolean;
    showNCAction?: boolean;
    showDPOptions?: boolean;
  };

  childActions: {
    showApproveButton: boolean;
    showDisapproveButton: boolean;
    showOpenModalButton: boolean;
    showOpenUrlButton: boolean;
    showNCAction?: boolean;
    showDPOptions?: boolean;
  };

  childDetail: {
    hasDetailColumns: boolean;
    hasDetailsHtml: boolean;
  };

  // // Configurações especiais
  // specialBehavior?: {
  //   type:
  //     | "modal-only"
  //     | "approval-only"
  //     | "nc-process"
  //     | "dp-process"
  //     | "standard";
  //   description: string;
  // };

  // Configurações deDataTableSAPHierarchicalRefactored seleção
  selection: {
    enableSelection: boolean;
    enableSelectionChildren: boolean;
    enableSelectAll: boolean;
    selectableRowsNoSelectAll: boolean;
  };

  // Configurações de modal
  modal: {
    hasDetailModal: boolean;
    hasDetailRoute: boolean;
    processesWithItemsInModal: boolean;
  };

  // Campos adicionais específicos
  additionalFields?: {
    documentNumberField?: string;
    exerciseField?: string;
    customFields?: Record<string, string>;
  };
}

export const PROCESS_RULES = (
  process: string
): Record<string, ProcessRule> => ({
  [String(processesProps(process)[0]?.type)]: {
    id: String(processesProps(process)[0]?.type),
    name: String(processesProps(process)[0]?.title),
    description: "",

    parentActions: {
      showApproveButton: !processesProps(process)[0]?.approveItems,
      showDisapproveButton: !processesProps(process)[0]?.approveItems,
      showOpenModalButton: !!processesProps(process)[0]?.hasDetailModal,
      showOpenUrlButton: !!processesProps(process)[0]?.hasOpenUrl,
    },

    childActions: {
      showApproveButton: !!processesProps(process)[0]?.approveItems,
      showDisapproveButton: !!processesProps(process)[0]?.approveItems,
      showOpenModalButton: ["ONE"].includes(process) ?? "false",
      showOpenUrlButton: false,
    },

    childDetail: {
      hasDetailColumns: !!processesProps(process)[0]?.detailColumns,
      hasDetailsHtml: !!processesProps(process)[0]?.documentDetailHtml,
    },

    selection: {
      enableSelection: true,
      enableSelectionChildren: !!processesProps(process)[0]?.approveItems,
      enableSelectAll: true,

      // TODO selectableRowsNoSelectAll
      selectableRowsNoSelectAll:
        !!SPECIAL_PROCESSES.PROCESSES_NO_ROW_SELECTION_ALL.includes(process),
    },

    modal: {
      hasDetailModal: !!processesProps(process)[0]?.hasDetailModal,
      hasDetailRoute: !!processesProps(process)[0]?.hasDetailRoute,
      processesWithItemsInModal:
        !!SPECIAL_PROCESSES.PROCESSES_WITH_ITEMS_IN_MODAL.includes(process),
    },
  },
});

// Processos especiais que precisam de tratamento adicional
export const SPECIAL_PROCESSES = {
  // Processos que mostram itens em modal
  PROCESSES_WITH_ITEMS_IN_MODAL: ["DV", "PR", "MI", "AL", "NC", "LC"],

  // Processos de inventário com campo adicional
  INVENTARY_PROCESSES_WITH_ADDITIONAL1: ["RA", "IV", "ID", "IM", "RI"],

  // Processos que não permitem seleção de linhas
  PROCESSES_NO_ROW_SELECTION_ALL: ["DS", "FJ", "DP"],

  // Processos com campos especiais para documento
  PROCESSES_WITH_SPECIAL_DOCUMENT_FIELDS: {
    VX: "ACNUM",
    OX: "TICKET",
    AL: "Transporte",
  },
};

// Função helper para obter regra de um processo
export const getProcessRule = (process: string): ProcessRule => {
  return PROCESS_RULES(process)[process]; // CI como fallback padrão
};

// Função helper para verificar se processo tem comportamento especial
// export const hasSpecialBehavior = (
//   process: string,
//   behaviorType: string
// ): boolean => {
//   const rule = getProcessRule(process);
//   return rule.specialBehavior?.type === behaviorType;
// };
