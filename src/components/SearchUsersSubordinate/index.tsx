import { useAuth } from "@/hooks";
import { SearchUsersProps } from "@/interfaces/searchUsers";
import { useCardsStore } from "@/stores/cardsStore";
import { Close } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import { MouseEvent, useState } from "react";
import { useTranslation } from "react-i18next";
import { NavigationButton, SearchUsers } from "../../components";

export const SearchUsersSubordinate = ({ className }: SearchUsersProps) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const { user, getTokenInitial } = useAuth();
  const { setCards } = useCardsStore();

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClearSubordinate = () => {
    setCards([]);
    getTokenInitial();
  };

  return (
    <>
      <NavigationButton
        variant="contained"
        router={false}
        onClick={handleClick}
        className={className}
        active={!!user.userReading} // Ativo quando há um subordinado sendo filtrado
        aria-controls={open ? "select-subordinate" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        aria-label="Abrir menu de documentos dos subordinados"
        endIcon={
          user.userReading ? (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleClearSubordinate();
              }}
              sx={{
                color: "inherit",
                ml: 1,
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <Close fontSize="small" />
            </IconButton>
          ) : undefined
        }
      >
        {user.userReading
          ? `${t("Filter")}: ${user.userReading}`
          : t("Subordinate.Documents")}
      </NavigationButton>
      <SearchUsers
        action="subordinate"
        anchorEl={anchorEl}
        setAnchorEl={setAnchorEl}
        open={open}
      />
    </>
  );
};
