import { Backdrop, Box, CircularProgress, Typography } from "@mui/material";
import React from "react";
import { createPortal } from "react-dom";

interface LoadingProps {
  open: boolean;
  message?: string;
}

const Loading: React.FC<LoadingProps> = ({ open, message }) => {
  if (!open) return null;

  return createPortal(
    <Backdrop
      open={open}
      sx={{
        zIndex: 9999,
        // color: "#fff",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(0, 0, 0, 0.7)",
      }}
    >
      <CircularProgress color="primary" size={40} />
      <Box mt={2}>
        <Typography variant="h6" color="white">
          {message}
        </Typography>
      </Box>
    </Backdrop>,
    document.body
  );
};

export default Loading;
