import { IPermissionsApproval, ISearchUser } from "@/interfaces";
import {
  Autocomplete,
  CircularProgress,
  FormControl,
  TextField,
} from "@mui/material";
import { t } from "i18next";
import React from "react";

export interface Option {
  email: string;
  employeeId: string;
  id: number;
  initials: string;
  name: string;
  opid: string;
  permissionsApproval: IPermissionsApproval[];
}

interface SelectProps {
  value: ISearchUser.SearchUsersAccounts[];
  options: Option[];
  onChange: (value: Option[]) => void;
  onInputChange: (value: string) => void;
  label?: string;
  disabled?: boolean;
  isLoadingOptions?: boolean;
  loadMore?: boolean;
  setLoadMore?: (value: boolean) => void;
  loadMoreResults?: () => void;
  resetValues?: () => void;
}

export const Select: React.FC<SelectProps> = ({
  value,
  options,
  onChange,
  disabled = false,
  onInputChange,
  label,
  isLoadingOptions,
  loadMore,
  setLoadMore,
  loadMoreResults,
  resetValues,
}) => {
  const [hasSearched, setHasSearched] = React.useState<boolean>(false);
  const [inputLength, setInputLength] = React.useState<number>(0);

  // Reset hasSearched when options change from empty to filled or vice versa
  React.useEffect(() => {
    if (options.length > 0) {
      setHasSearched(true);
    }
  }, [options]);

  // console.log(value, value);

  return (
    <FormControl fullWidth>
      <Autocomplete
        options={options}
        disabled={disabled}
        loading={isLoadingOptions}
        getOptionLabel={(option) => `${option.initials} - ${option.name}` || ""}
        isOptionEqualToValue={(option, value) => option.id === value?.id}
        value={!!value?.length ? value[0] : null}
        open={
          inputLength >= 3 ||
          isLoadingOptions ||
          (options.length > 0 && hasSearched)
        }
        loadingText={
          isLoadingOptions ? t("Loading", "Carregando...") : undefined
        }
        onChange={(_, newValue) => {
          onChange(newValue ? [newValue] : []);
        }}
        onInputChange={(_, newValue, reason) => {
          if (reason === "input") {
            onInputChange(newValue);
            setInputLength(newValue.length);

            if (newValue.length >= 3) {
              setHasSearched(true);
            } else if (newValue.length === 0) {
              setHasSearched(false);
            }
          }
        }}
        filterOptions={(options, state) => {
          if (!state.inputValue || state.inputValue.length < 3) {
            return [];
          }
          if (isLoadingOptions) {
            return options;
          }

          return options;
        }}
        autoComplete
        noOptionsText={
          hasSearched &&
          inputLength >= 3 &&
          !isLoadingOptions &&
          options.length === 0
            ? t("No.options.available", "Nenhuma opção disponível")
            : ""
        }
        onClose={() => {
          // Não limpe o input ao fechar, apenas resete buscas
          setHasSearched(false);
          setInputLength(!!value?.length ? value[0].name.length : 0);
          resetValues && resetValues();
        }}
        slotProps={{
          listbox: {
            onScroll: (event) => {
              if (loadMore && !isLoadingOptions && loadMoreResults) {
                const listboxNode = event.currentTarget;
                const isNearBottom =
                  listboxNode.scrollTop + listboxNode.offsetHeight >=
                  listboxNode.scrollHeight - 10;

                if (isNearBottom) {
                  loadMoreResults();
                  setLoadMore && setLoadMore(false);
                }
              }
            },
          },
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            // Mostra o texto do selecionado se houver valor selecionado
            value={
              !!value?.length ? `${value[0].initials} - ${value[0].name}` : ""
            }
            variant="outlined"
            placeholder={t("Search.by.ID.Acronym.Name")}
            slotProps={{
              input: {
                ...params.InputProps,
                endAdornment: (
                  <React.Fragment>
                    {isLoadingOptions ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </React.Fragment>
                ),
              },
            }}
          />
        )}
      />
    </FormControl>
  );
};
