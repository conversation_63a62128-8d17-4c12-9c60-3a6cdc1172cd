import React, { useCallback, useState } from "react";
import Joyride, { ACTIONS, EVENTS, STATUS, Step } from "react-joyride";
import { APIAccountConfig } from "../../api";
import { useAuth } from "../../hooks";
import TooltipJoyrider, { BeaconJoyrider } from "../TooltipJoyrider";

interface JoyrideTutorialProps {
  stepsDesktop: Step[];
  stepsMobile: Step[];
  runJoyrider: boolean;
  setRunJoyrider: (value: boolean) => void;
}

export const JoyrideTutorial: React.FC<JoyrideTutorialProps> = ({
  stepsDesktop,
  stepsMobile,
  runJoyrider,
  setRunJoyrider,
}) => {
  const [stepIndexJoyrider, setStepIndexJoyrider] = useState(0);
  const { tutorialDone } = useAuth();

  const handleJoyrideCallback = useCallback(
    async (data: { action: any; index: any; status: any; type: any }) => {
      const { action, index, status, type } = data;

      if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
        setRunJoyrider(false);
        setStepIndexJoyrider(0);
        const result = await APIAccountConfig.doneTutorial();
        if (result && result.success) {
          tutorialDone();
        }
      }
      if ([EVENTS.TARGET_NOT_FOUND].includes(type)) {
        setRunJoyrider(false);
        setStepIndexJoyrider(0);
      }
      if ([EVENTS.STEP_BEFORE].includes(type)) {
        if (index === 0 && window.innerWidth <= 768) {
          // Custom logic for mobile
        }
      }
      if ([EVENTS.STEP_AFTER].includes(type)) {
        if (action === ACTIONS.CLOSE) {
          setRunJoyrider(false);
          setStepIndexJoyrider(0);
        } else {
          setStepIndexJoyrider(index + (action === ACTIONS.PREV ? -1 : 1));
        }
      }
    },
    [tutorialDone]
  );

  return (
    <div data-testid="joyride-dialog">
      <Joyride
        steps={window.innerWidth > 768 ? stepsDesktop : stepsMobile}
        run={runJoyrider}
        tooltipComponent={TooltipJoyrider}
        beaconComponent={BeaconJoyrider}
        stepIndex={stepIndexJoyrider}
        callback={handleJoyrideCallback}
        scrollToFirstStep
        showSkipButton
        continuous
        spotlightPadding={window.innerWidth > 768 ? 10 : 4}
        styles={{
          options: {
            zIndex: 999,
          },
        }}
      />
    </div>
  );
};

export default JoyrideTutorial;
