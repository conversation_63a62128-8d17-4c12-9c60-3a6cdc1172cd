/* eslint-disable max-len */
import React, { useCallback, useState } from "react";

import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";
import { ListErrorProps } from "../Submenu";

interface ErrorListProps {
  items: ListErrorProps[];
}

const ErrorList: React.FC<ErrorListProps> = ({ items }) => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);
  const [dataModal, setDataModal] = useState(<></>);

  const dataTitleModal = "LOG";
  const dataFooterModal = <></>;

  const handleOpenThisError = useCallback((error: ListErrorProps) => {
    const content = (
      <Table>
        <Tbody>
          <Tr>
            <Td>Trace</Td>
            <Td>
              <code>{error.stackTrace}</code>
            </Td>
          </Tr>
          <Tr>
            <Td>{t("Create.Date")}</Td>
            <Td>
              {" "}
              {error.created
                ? new Date(error.created).toLocaleDateString("pt-BR")
                : ""}
            </Td>
          </Tr>
          <Tr>
            <Td>Query string</Td>
            <Td>{error.queryString}</Td>
          </Tr>
          <Tr>
            <Td>Inner message</Td>
            <Td>{error.message}</Td>
          </Tr>
          <Tr>
            <Td>Process</Td>
            <Td>{error.process}</Td>
          </Tr>
          <Tr>
            <Td>Error code</Td>
            <Td>{error.statusCode}</Td>
          </Tr>
        </Tbody>
      </Table>
    );
    setDataModal(content);
    setShowModal(true);
  }, []);

  return (
    <Container>
      {items.length > 0 && (
        <Table>
          <Thead>
            <Tr>
              <Td>Employee ID</Td>
              <Td>{t("Name")}</Td>
              <Td>Status code</Td>
              <Td>{t("Method")}</Td>
              <Td>Request path</Td>
              <Td>Message</Td>
              <Td></Td>
            </Tr>
          </Thead>

          <Tbody>
            {items.map((item) => (
              <Tr key={item.errorCode}>
                <Td data-header="Employee ID: " className="employee">
                  {item.queryString
                    .slice(item.queryString.search("employeeid"))
                    .replace("employeeid=", "")
                    .includes("&")
                    ? item.queryString
                        .slice(item.queryString.search("employeeid"))
                        .replace("employeeid=", "")
                        .split("&")
                    : item.queryString
                        .slice(item.queryString.search("employeeid"))
                        .replace("employeeid=", "")}
                </Td>
                <Td data-header={`${t("Name")}: `} className="name">
                  {item.name}
                </Td>
                <Td data-header="Status code: " className="code">
                  {item.statusCode}
                </Td>
                <Td data-header={`${t("Method")}: `} className="method">
                  {item.method}
                </Td>
                <Td data-header="Request path: " className="path">
                  {item.requestPath}
                </Td>
                <Td data-header="Message: " className="message">
                  {item.message}
                </Td>
                <Td>
                  <FiSearch
                    size={14}
                    className="detail"
                    onClick={() => handleOpenThisError(item)}
                  />
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}

      {/*   {showModal && (
        <Modal
          data={dataModal}
          title={dataTitleModal}
          footer={dataFooterModal}
          callbackClose={() => setShowModal(false)}
        />
      )} */}
    </Container>
  );
};

export default ErrorList;
