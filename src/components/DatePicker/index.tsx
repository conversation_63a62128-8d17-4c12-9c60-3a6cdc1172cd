import {
  DatePicker as DatePicker<PERSON><PERSON>,
  DatePickerProps,
} from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { FunctionComponent } from "react";

interface DatePickerComponentProps extends DatePickerProps<Dayjs> {
  value: Dayjs;
  onChange: (newValue: Dayjs | null) => void;
  id: string;
  label: string;
}

export const DatePicker: FunctionComponent<DatePickerComponentProps> = ({
  value,
  label,
  id,
  onChange,
  ...props
}) => {
  return (
    <div>
      <DatePickerMUI
        data-testid={id}
        label={label}
        value={value}
        onChange={onChange}
        shouldDisableDate={(date) => date.isBefore(dayjs(), "day")}
        {...props}
      />
    </div>
  );
};
