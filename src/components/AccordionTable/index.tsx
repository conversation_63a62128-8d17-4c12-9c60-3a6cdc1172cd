import { AvipamItem } from "@/interfaces/avipam";
import { HRDocument } from "@/interfaces/IHR";
import { IProcurementItemJR } from "@/interfaces/procurement";
import { ExpandMore } from "@mui/icons-material";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  Box,
  Card as MuiCard,
  Typography,
} from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import {
  IAccordionTableProps,
  IAptus,
  IAriba,
  ICommercial,
  IIntranet,
  IOnelog,
  IOracle,
  ISap,
  ISeoDigital,
  IServiceNow,
} from "../../interfaces";
import theme from "../../styles/theme";
import { DataTableAptus } from "../DataTableAptus";
import { DataTableAriba } from "../DataTableARIBA";
import { DataTableAvipam } from "../DataTableAvipam";
import { DataTableCommercial } from "../DataTableCommercial";
import { DataTableDocuSign } from "../DataTableDocusign";
import { DataTableHR } from "../DataTableHR";
import { DataTableIntranet } from "../DataTableIntranet";
import { DataTableOneLogHierarchical } from "../DataTableOneLogHierarchical";
import { DataTableOracle } from "../DataTableOracle";
import { DataTableProcurement } from "../DataTableProcurement";
import { DataTableSAPHierarchical } from "../DataTableSAPHierarchical";
import { DataTableSeoDigital } from "../DataTableSeoDigital";
import { DataTableServiceNow } from "../DataTableServiceNow";

export type AllProcessesTypes =
  | ISap.ItemProps
  | IAriba.AribaItemType
  | ICommercial.ICommercialProps
  | IServiceNow.ItemProps
  | IOracle.OracleItemType
  | IOracle.OracleItemType
  | IAptus.IAptusBaseDocument
  | HRDocument
  | IProcurementItemJR
  | IOnelog.ClientProps
  | IIntranet.ItemProps
  | any;

export const AccordionTable: React.FC<
  IAccordionTableProps<AllProcessesTypes>
> = ({ process, title, headerData, detailData, type, total, ...props }) => {
  const { t } = useTranslation();

  const processData = type ? processesProps(type) : [];
  const { origin } =
    processData.length > 0 ? processData[0] : { origin: undefined };

  return (
    <div>
      <Accordion style={{ padding: "0.5rem", marginBottom: "0.5rem" }}>
        <AccordionSummary
          expandIcon={<ExpandMore />}
          aria-controls={`panel${props.key}-content`}
          id={`panel${props.key}-header`}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography variant="h6" component="h3">
                {t(title)}
              </Typography>
            </Box>
            <Box sx={{ marginRight: "24px" }}>
              <Avatar
                sx={{
                  width: 30,
                  height: 30,
                  backgroundColor: theme.palette.secondary.main,
                  color: theme.palette.primary.main,
                  fontSize: "12px",
                  fontWeight: 700,
                }}
              >
                {total}
              </Avatar>
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <MuiCard sx={{ maxWidth: "100%" }} {...props}>
            {origin === "SAP" && (
              <DataTableSAPHierarchical
                headerData={headerData}
                detailData={detailData}
                process={type}
              />
            )}
            {origin === "ONELOG" && (
              <DataTableOneLogHierarchical
                headerData={headerData}
                detailData={detailData}
                process={process}
              />
            )}
            {origin === "ARIBA" && (
              <DataTableAriba
                headerData={headerData as IAriba.AribaItemType[]}
                process={process}
              />
            )}
            {origin === "COMMERCIAL" && (
              <DataTableCommercial
                headerData={headerData as ICommercial.ICommercialProps[]}
                process={process}
              />
            )}
            {origin === "SERVICENOW" && (
              <DataTableServiceNow
                headerData={headerData as IServiceNow.ItemProps[]}
                process={process}
              />
            )}
            {origin === "ORACLE" && (
              <DataTableOracle
                headerData={headerData as IOracle.OracleItemType[]}
                type={type}
              />
            )}
            {origin === "APTUS" && (
              <DataTableAptus
                headerData={headerData as IAptus.IAptusBaseDocument[]}
                process={process}
                type={type}
              />
            )}
            {origin === "HR" && (
              <DataTableHR
                headerData={headerData as HRDocument[]}
                process={process}
                type={type}
              />
            )}
            {origin === "PROCUREMENT" && (
              <DataTableProcurement
                headerData={headerData as IProcurementItemJR[]}
                detailData={detailData as IProcurementItemJR[]}
                type={type}
              />
            )}
            {origin === "AVIPAM" && (
              <DataTableAvipam
                headerData={headerData as AvipamItem[]}
                process={process}
              />
            )}
            {origin === "DOCUSIGN" && (
              <DataTableDocuSign
                headerData={headerData}
                detailData={detailData}
                process={process}
              />
            )}
            {origin === "INTRANET" && (
              <DataTableIntranet
                headerData={headerData}
                detailData={detailData}
                type={type}
              />
            )}
            {origin === "SEODIGITAL" && (
              <DataTableSeoDigital
                headerData={headerData as ISeoDigital.ISeoDigitalBase[]}
                process={process}
              />
            )}
          </MuiCard>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};
