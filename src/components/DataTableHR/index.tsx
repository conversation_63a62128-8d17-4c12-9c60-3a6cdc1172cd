import { FC, useCallback, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";

import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { HRDocument } from "@/interfaces/IHR";
import { ActionButtons } from "../ActionButtons";
import { MainDataTable } from "../DataTable/MainDataTable";
import Loading from "../Loading";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableHR: FC<{
  headerData: HRDocument[];
  process: string;
  type: string;
}> = ({ headerData, process, type }) => {
  const { headerColumns, origin, title, detailModalHeader } =
    processesProps(type)[0] || {};

  const { user } = useAuth();
  const { t } = useTranslation();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: HRDocument | HRDocument[];
  }>({ open: false, data: [] as HRDocument[] });

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
    row: HRDocument | null;
  }>({
    open: false,
    type: null,
    row: null,
  });

  const { approveReproveDocument, isLoading } = useApprovalDocument<any>();

  const handleApprovalReprovalDocument = useCallback(
    async (row: HRDocument, status: "A" | "R" | boolean, comment?: string) => {
      approveReproveDocument({
        rowData: row,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason: comment,
      });
    },
    [process, origin]
  );

  const handleOpenModal = (data: HRDocument | HRDocument[]) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as HRDocument[] });
  };

  const handleModalApprove = async (data: HRDocument | HRDocument[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    const status = type === "EPI" ? true : "A";
    await handleApprovalReprovalDocument(row, status);
    handleCloseModal();
  };

  const handleModalReject = (data: HRDocument | HRDocument[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenApprovalModal(row, "disapprove");
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: HRDocument,
    type: "approve" | "disapprove"
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
    });
  };

  const handleCloseApprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (reason: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status =
        approvalModalState.type === "approve"
          ? process === "EPI"
            ? true
            : "A"
          : process === "EPI"
          ? false
          : "R";

      console.log(status);

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        reason
      );
      handleCloseApprovalModal();
    }
  };

  // Add action column for approval/rejection
  const addActionColumn = useCallback(
    (columns: TableColumn<HRDocument>[]) => {
      const actionsColumnExists = columns?.some(
        (column) => column?.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: HRDocument) => {
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<HRDocument>
                    row={row}
                    onApprove={() => {
                      if (process === "LM") {
                        handleOpenApprovalModal(row, "approve");
                      } else {
                        const status = process === "EPI" ? true : "A";
                        handleApprovalReprovalDocument(row, status);
                      }
                    }}
                    onDisapprove={() =>
                      handleOpenApprovalModal(row, "disapprove")
                    }
                    showOpenUrlButton={false}
                    showOpenModalButton={!!detailModalHeader}
                    onOpenModal={() => handleOpenModal(row)}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleOpenApprovalModal,
      handleApprovalReprovalDocument,
      user.onlyReading,
      handleOpenModal,
      detailModalHeader,
      process,
    ]
  );

  addActionColumn(headerColumns as TableColumn<HRDocument>[]);

  return (
    <div>
      <MainDataTable
        rowKey={(row: HRDocument) => row.Codigo}
        columns={headerColumns as TableColumn<HRDocument>[]}
        data={headerData}
      />
      {modalState.open && modalState.data && detailModalHeader && (
        <ModalItemDetails
          open={modalState.open}
          onClose={handleCloseModal}
          modalTitle={title}
          data={modalState.data}
          detailModalHeader={detailModalHeader}
          origin={origin}
          onApprove={handleModalApprove}
          onReject={handleModalReject}
        />
      )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={
          approvalModalState.type === "approve" ? t("Approve") : t("Disapprove")
        }
        subtitle={
          approvalModalState.type === "approve"
            ? t("Approval.Reason")
            : t("Disapproval.Reason")
        }
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={
          approvalModalState.type === "approve" ? t("Approve") : t("Disapprove")
        }
        confirmButtonColor={
          approvalModalState.type === "approve" ? "success" : "error"
        }
        isRequired={approvalModalState.type === "disapprove"}
      />

      {isLoading && <Loading open={true} />}
    </div>
  );
};
