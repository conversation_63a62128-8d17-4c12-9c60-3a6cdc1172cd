import { Cancel, CheckCircle, OpenInNew, Search } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalApprovalReason } from "../ModalApprovalReason";

interface ActionButtonsWithReasonProps<T> {
  row: T;
  onApprove?: (row: T, reason: string) => void;
  onDisapprove?: (row: T, reason: string) => void;
  onOpenModal?: (row: T) => void;
  showApproveButton?: boolean;
  showDisapproveButton?: boolean;
  showOpenModalButton?: boolean;
  showOpenUrlButton?: boolean;
  openUrl?: string;
  requireReasonForApproval?: boolean;
  requireReasonForDisapproval?: boolean;
}

export const ActionButtonsWithReason = <T extends unknown>({
  row,
  onApprove,
  onDisapprove,
  onOpenModal,
  showApproveButton = true,
  showDisapproveButton = true,
  showOpenModalButton = true,
  showOpenUrlButton = true,
  openUrl,
  requireReasonForApproval = false,
  requireReasonForDisapproval = true,
}: ActionButtonsWithReasonProps<T>) => {
  const { t } = useTranslation();
  const [modalState, setModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | null;
  }>({
    open: false,
    type: null,
  });

  const handleApprove = () => {
    if (requireReasonForApproval) {
      setModalState({ open: true, type: "approve" });
    } else if (onApprove) {
      onApprove(row, "");
    }
  };

  const handleDisapprove = () => {
    if (requireReasonForDisapproval) {
      setModalState({ open: true, type: "disapprove" });
    } else if (onDisapprove) {
      onDisapprove(row, "");
    }
  };

  const handleOpenModal = () => {
    if (onOpenModal) {
      onOpenModal(row);
    }
  };

  const handleOpenURL = () => {
    if (openUrl) {
      window.open(openUrl, "_blank");
    }
  };

  const handleModalClose = () => {
    setModalState({ open: false, type: null });
  };

  const handleModalConfirm = (reason: string) => {
    if (modalState.type === "approve" && onApprove) {
      onApprove(row, reason);
    } else if (modalState.type === "disapprove" && onDisapprove) {
      onDisapprove(row, reason);
    }
    handleModalClose();
  };

  const getModalProps = () => {
    if (modalState.type === "approve") {
      return {
        title: t("Aprovar Documento"),
        subtitle: t("Informe o motivo da aprovação deste documento."),
        confirmButtonText: t("Aprovar"),
        confirmButtonColor: "success" as const,
        isRequired: requireReasonForApproval,
      };
    } else {
      return {
        title: t("Reprovar Documento"),
        subtitle: t("Informe o motivo da reprovação deste documento."),
        confirmButtonText: t("Reprovar"),
        confirmButtonColor: "error" as const,
        isRequired: requireReasonForDisapproval,
      };
    }
  };

  return (
    <>
      {showApproveButton && (
        <IconButton onClick={handleApprove} title={t("Approve")}>
          <CheckCircle color="success" />
        </IconButton>
      )}

      {showDisapproveButton && (
        <IconButton onClick={handleDisapprove} title={t("Disapprove")}>
          <Cancel color="error" />
        </IconButton>
      )}

      {showOpenModalButton && (
        <IconButton onClick={handleOpenModal} title={t("View details")}>
          <Search />
        </IconButton>
      )}

      {showOpenUrlButton && openUrl && (
        <IconButton onClick={handleOpenURL} title={t("Open")}>
          <OpenInNew />
        </IconButton>
      )}

      <ModalApprovalReason
        open={modalState.open}
        onClose={handleModalClose}
        onConfirm={handleModalConfirm}
        {...getModalProps()}
      />
    </>
  );
};
