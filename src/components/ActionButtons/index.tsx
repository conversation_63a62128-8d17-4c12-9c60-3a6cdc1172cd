import {
  Cancel,
  CheckCircle,
  OpenInNew,
  Search,
  Undo,
} from "@mui/icons-material";
import { IconButton } from "@mui/material";
import { useTranslation } from "react-i18next";

interface ActionButtonsProps<T> {
  row: T;
  onApprove?: (row: T) => void;
  onDisapprove?: (row: T) => void;
  onOpenModal?: (row: T) => void;
  showApproveButton?: boolean;
  showDisapproveButton?: boolean;
  showOpenModalButton?: boolean;
  showOpenUrlButton?: boolean;
  openUrl?: string;
  isSapNcProcess?: boolean;
  onNCAction?: (row: T) => void;
}

export const ActionButtons = <T extends unknown>({
  row,
  onApprove,
  onDisapprove,
  onOpenModal,
  showApproveButton = true,
  showDisapproveButton = true,
  showOpenModalButton = true,
  showOpenUrlButton = true,
  openUrl,
  isSapNcProcess = false,
  onNCAction,
}: ActionButtonsProps<T>) => {
  const { t } = useTranslation();

  const handleNCAction = () => {
    if (onNCAction) {
      onNCAction(row);
    }
  };
  const handleApprove = () => {
    if (onApprove) {
      onApprove(row);
    }
  };

  const handleDisapprove = () => {
    if (onDisapprove) {
      onDisapprove(row);
    }
  };

  const handleOpenModal = () => {
    if (onOpenModal) {
      onOpenModal(row);
    }
  };

  const handleOpenURL = () => {
    if (openUrl) {
      window.open(openUrl, "_blank");
    }
  };

  return (
    <div
      style={{
        display: "flex",
        gap: "2px",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {showApproveButton && (
        <IconButton
          onClick={handleApprove}
          title={t("Approve")}
          size="small"
          sx={{ padding: "4px" }}
        >
          <CheckCircle color="success" fontSize="small" />
        </IconButton>
      )}

      {showDisapproveButton && (
        <IconButton
          onClick={handleDisapprove}
          title={t("Disapprove")}
          size="small"
          sx={{ padding: "4px" }}
        >
          <Cancel color="error" fontSize="small" />
        </IconButton>
      )}

      {showOpenModalButton && (
        <IconButton
          onClick={handleOpenModal}
          title={t("View details")}
          size="small"
          sx={{ padding: "4px" }}
        >
          <Search fontSize="small" />
        </IconButton>
      )}

      {showOpenUrlButton && openUrl && (
        <IconButton
          onClick={handleOpenURL}
          title={t("Open")}
          size="small"
          sx={{ padding: "4px" }}
        >
          <OpenInNew fontSize="small" />
        </IconButton>
      )}

      {/* Botão exclusivo para isSapNcProcesso NC do SAP */}
      {isSapNcProcess && (
        <IconButton
          onClick={handleNCAction}
          title={t("Return")}
          size="small"
          color="secondary"
          sx={{ padding: "4px" }}
        >
          <Undo fontSize="small" />
        </IconButton>
      )}
    </div>
  );
};
