import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  Box,
  Button,
  TextField,
  Paper,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Stack,
  IconButton,
  CircularProgress,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { ptBR } from "date-fns/locale";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import Axios, { CancelTokenSource } from "axios";
import { useAuth } from "../../hooks/auth";
import { SearchUsers } from "../../components";
import { api } from "../../api/api";
import { IPermissionsApproval } from "../../interfaces";

export interface ListErrorProps {
  id: number;
  statusCode: number;
  method: string;
  requestPath: string;
  message: string;
  stackTrace: string;
  created: string;
  queryString: string;
  innerMessage: string | null;
  accountId: number;
  process: string;
  errorCode: string;
  name: string;
}

interface SubProps {
  callbackChangeTab: React.Dispatch<React.SetStateAction<string>>;
  callbackSetErrorsList: React.Dispatch<React.SetStateAction<ListErrorProps[]>>;
  callbackSetApprovalList: React.Dispatch<
    React.SetStateAction<ApprovalProps[]>
  >;
}
interface FilterProps {
  accounts: {
    id: number;
    name: string;
    initials: string;
    employeeId: string;
    permissionsApproval: IPermissionsApproval[];
  }[];
  page: number;
  qttyPages: number;
  qttyRegisters: number;
}

export interface ApprovalProps {
  id: number;
  accountId: number;
  process: string;
  document: string;
  action: string;
  origin: string;
  ip: string;
  created: string;
  employeeId: string;
}

const NavigationAdmin: React.FC<SubProps> = ({
  callbackChangeTab,
  callbackSetErrorsList,
  callbackSetApprovalList,
}) => {
  const {
    user,
    getTokenForThisUser,
    getTokenInitial,
    setEmployeeRepresentedId,
  } = useAuth();

  const { t } = useTranslation();
  const [activeButton, setActiveButton] = useState("1");
  const inputRef = useRef<HTMLInputElement>(null);
  const [approvalFilters, setApprovalFilters] = useState({
    process: "",
    document: "",
    start: new Date(),
    end: new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      new Date().getDate() + 7
    ),
  });
  const [employeeId, setEmployeeId] = useState(user.employeeID);
  const [errosLogs, setErrorLogs] = useState({
    path: "",
    process: "",
    code: "",
    start: new Date(),
    end: new Date(
      new Date().getFullYear(),
      new Date().getMonth(),
      new Date().getDate() + 7
    ),
  });

  const usersSpecialPermissions = import.meta.env.VITE_PERMISSION_REPORT;

  const handleClick = useCallback((e) => {
    setActiveButton(e.target.value);
    callbackChangeTab(e.target.value);
  }, []);

  const [filterOptions, setFilterOptions] = useState({} as FilterProps);
  const [filterTerm, setFilterTerm] = useState("");

  const searchUser = useCallback(
    async (term: string, cancelTokenSource: CancelTokenSource) => {
      if (term === "") {
        setFilterOptions({} as FilterProps);
        return;
      }
      if (term.length < 3) {
        return;
      }

      try {
        const { data } = await api.get(
          `${import.meta.env.VITE_DOMAIN}/${
            import.meta.env.VITE_URL_GET_ACCOUNTS
          }/${term}?showPermissions=true`,
          {
            cancelToken: cancelTokenSource.token,
          }
        );
        setFilterOptions(data);
      } catch (err) {
        setFilterOptions({} as FilterProps);
      }
    },
    [user]
  );

  useEffect(() => {
    const cancelTokenSource = Axios.CancelToken.source();
    const timer = window.setTimeout(() => {
      searchUser(filterTerm, cancelTokenSource);
    }, 500);
    return () => {
      setEmployeeRepresentedId("");
      window.clearTimeout(timer);
      cancelTokenSource.cancel();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterTerm]);

  const closeButtonIfOpen = useCallback(() => {
    inputRef.current?.parentElement?.classList.remove("show");
  }, []);

  const handleSetRepresentativeUser = useCallback(
    async (element) => {
      try {
        closeButtonIfOpen();

        await getTokenForThisUser({
          userId: element.employeeId,
          action: "admin",
          permissionsApproval: element.permissionsApproval,
          userReading: element.name,
        });
        setTotalAribaPending(0);

        setEmployeeId(element.employeeId);
        setEmployeeRepresentedId(element.employeeId);
      } catch (err) {
        handleSetListErrors(
          t("Oops.Something.went.wrong.Try.again.later."),
          "",
          "",
          "failure"
        );
      }
    },
    [user]
  );

  const handleGobackUser = useCallback(() => {
    getTokenInitial();
    setEmployeeId(user.employeeID);
    setFilterTerm("");
    inputRef.current && (inputRef.current.value = "");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const handleSubmitLogErrors = useCallback(async () => {
    try {
      const keyJobs = import.meta.env.VITE_APP_SETTINGS_KEY_JOBS;
      const initialDate = new Date(errosLogs.start)
        .toLocaleDateString("pt-BR")
        .split("/")
        .reverse()
        .join("-");
      const finalDate = new Date(errosLogs.end)
        .toLocaleDateString("pt-BR")
        .split("/")
        .reverse()
        .join("-");
      const errorCode = errosLogs.code;
      const requestPath = errosLogs.path;
      const { accountId, reportAccountId } = user;

      const { data } = await api.get<ListErrorProps[]>(
        `${
          import.meta.env.VITE_ANALYTICS
        }/ErrorsReport/${initialDate}/${finalDate}?keyJobs=${keyJobs}&process=${
          errosLogs.process
        }&errorCode=${errorCode}&requestPath=${requestPath}&accountId=${
          reportAccountId || accountId
        }`
      );
      callbackSetErrorsList(
        data.reduce((acc, act) => {
          acc.push({
            ...act,
            name: user.userReading || user.employeeName,
          });
          return acc;
        }, [] as ListErrorProps[]) || ([] as ListErrorProps[])
      );
    } catch (err) {
      callbackSetErrorsList([] as ListErrorProps[]);
      handleSetListErrors(err.message, "info");
    }
  }, [errosLogs, user]);

  const handleSubmitApproval = useCallback(async () => {
    try {
      const keyJobs = import.meta.env.VITE_APP_SETTINGS_KEY_JOBS;
      const initialDate = new Date(approvalFilters.start)
        .toLocaleDateString("pt-BR")
        .split("/")
        .reverse()
        .join("-");
      const finalDate = new Date(approvalFilters.end)
        .toLocaleDateString("pt-BR")
        .split("/")
        .reverse()
        .join("-");
      const { document } = approvalFilters;
      const { accountId, reportAccountId } = user;
      const variables = `${initialDate}/${finalDate}?keyJobs=${keyJobs}&document=${document}&process=${
        approvalFilters.process
      }&accountId=${reportAccountId || accountId}`;
      const { data } = await api.get<ApprovalProps[]>(
        `${import.meta.env.VITE_ANALYTICS}/AuditReport/${variables}`
      );
      callbackSetApprovalList(
        data.reduce((acc, act) => {
          acc.push({
            ...act,
            employeeId,
          });
          return acc;
        }, [] as ApprovalProps[]) || ([] as ApprovalProps[])
      );
    } catch (err) {
      callbackSetApprovalList([] as ApprovalProps[]);
      handleSetListErrors(err.message, "info");
    }
  }, [approvalFilters, user, employeeId]);

  return (
    <MenuButtons>
      <Wrapper>
        <form>
          <Label
            htmlFor="1"
            className={`${activeButton === "1" ? "active" : ""}`}
          >
            <input
              id="1"
              value="1"
              type="radio"
              name="option"
              onClick={handleClick}
            />
            {t("Pendencies")}
          </Label>
          <Label
            htmlFor="2"
            className={`${activeButton === "2" ? "active" : ""}`}
          >
            <input
              id="2"
              value="2"
              type="radio"
              name="option"
              onClick={handleClick}
            />
            {t("Permissions")}
          </Label>
          {usersSpecialPermissions?.includes(user.employeeID) && (
            <Label
              htmlFor="3"
              className={`${activeButton === "3" ? "active" : ""}`}
            >
              <input
                id="3"
                value="3"
                type="radio"
                name="option"
                onClick={handleClick}
              />
              {t("Error.Log")}
            </Label>
          )}
          {usersSpecialPermissions?.includes(user.employeeID) && (
            <Label
              htmlFor="4"
              className={`${activeButton === "4" ? "active" : ""}`}
            >
              <input
                id="4"
                value="4"
                type="radio"
                name="option"
                onClick={handleClick}
              />
              {t("Approvals")}
            </Label>
          )}
        </form>
      </Wrapper>

      <FilterWrapper
        className={`${Number(activeButton) <= 2 ? "no-style" : "wrapper"}`}
      >
        <Dropdown
          text={
            user.userReading ? user.userReading : t("Search.by.ID.Acronym.Name")
          }
          className={`notiflix-filter-user ${
            Number(activeButton) <= 2 ? "dropdown-outlined" : "dropdown-borded"
          }`}
          action={<FiX onClick={handleGobackUser} className="action" />}
          tooltip={
            Number(activeButton) > 2 ? t("Search.by.ID.Acronym.Name") : ""
          }
        >
          <input
            // placeholder={t('Search.by.ID.Acronym.Name')}
            onInput={(e) => setFilterTerm(e.currentTarget.value)}
            ref={inputRef}
            autoFocus
          />
          <div
            style={{ width: "100%" }}
            onScroll={(e) => showMoreUsers(e.currentTarget)}
          >
            {filterOptions &&
              filterOptions.accounts?.map((element) => (
                <button
                  type="button"
                  key={element.name}
                  onClick={() => handleSetRepresentativeUser(element)}
                >
                  {`${element.initials} - ${element.name}`}
                </button>
              ))}
          </div>
        </Dropdown>
        {Number(activeButton) === 3 && (
          <>
            <Input
              placeholder="Request Path"
              onChange={(e) => {
                const { value } = e.target;
                setErrorLogs((prev) => ({
                  ...prev,
                  path: value,
                }));
              }}
            />
            <Input
              placeholder={t("Process")}
              onChange={(e) => {
                const { value } = e.target;
                setErrorLogs((prev) => ({
                  ...prev,
                  process: value,
                }));
              }}
            />
            <Input
              placeholder="Error Code"
              onChange={(e) => {
                const { value } = e.target;
                setErrorLogs((prev) => ({
                  ...prev,
                  code: value,
                }));
              }}
            />
            <DatePicker
              placeholderText={t("Start.Date.II")}
              wrapperClassName="datepicker-wrapper"
              selected={errosLogs.start}
              dateFormat="dd/MM/yyyy"
              onChange={(data: Date) => {
                setErrorLogs((prev) => ({ ...prev, start: data, end: data }));
              }}
              locale={ptBR}
            />
            <DatePicker
              placeholderText={t("End.Date")}
              wrapperClassName="datepicker-wrapper"
              selected={errosLogs.end}
              dateFormat="dd/MM/yyyy"
              minDate={errosLogs.start}
              maxDate={
                errosLogs.start
                  ? new Date(
                      errosLogs.start.getFullYear(),
                      errosLogs.start.getMonth(),
                      errosLogs.start.getDate() + 7
                    )
                  : null
              }
              onChange={(data: Date) => {
                setErrorLogs((prev) => ({ ...prev, end: data }));
              }}
              locale={ptBR}
            />
            <Button
              color="green"
              className="submit"
              onClick={handleSubmitLogErrors}
            >
              {t("Filter")}
            </Button>
          </>
        )}
        {Number(activeButton) === 4 && (
          <>
            <Input
              placeholder={t("Process")}
              onInput={(e) => {
                const { value } = e.currentTarget;
                setApprovalFilters((prev) => ({
                  ...prev,
                  process: value,
                }));
              }}
            />
            <Input
              placeholder={t("Document")}
              onInput={(e) => {
                const { value } = e.currentTarget;
                setApprovalFilters((prev) => ({
                  ...prev,
                  document: value,
                }));
              }}
            />
            <DatePicker
              placeholderText={t("Start.Date.II")}
              wrapperClassName="datepicker-wrapper"
              selected={approvalFilters.start}
              dateFormat="dd/MM/yyyy"
              onChange={(data: Date) => {
                setApprovalFilters((prev) => ({
                  ...prev,
                  start: data,
                  end: data,
                }));
              }}
              locale={ptBR}
            />
            <DatePicker
              placeholderText={t("End.Date")}
              wrapperClassName="datepicker-wrapper"
              selected={approvalFilters.end}
              dateFormat="dd/MM/yyyy"
              minDate={approvalFilters.start}
              maxDate={
                approvalFilters.start
                  ? new Date(
                      approvalFilters.start.getFullYear(),
                      approvalFilters.start.getMonth(),
                      approvalFilters.start.getDate() + 7
                    )
                  : null
              }
              onChange={(data: Date) => {
                setApprovalFilters((prev) => ({ ...prev, end: data }));
              }}
              locale={ptBR}
            />
            <Button
              color="green"
              className="submit"
              onClick={handleSubmitApproval}
            >
              {t("Filter")}
            </Button>
          </>
        )}
      </FilterWrapper>
    </MenuButtons>
  );
};

export default NavigationAdmin;
