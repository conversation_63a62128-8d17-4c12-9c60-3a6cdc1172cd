import { ISap } from "@/interfaces";
import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { getAdditionalField } from "@/utils/GetItemsSap";
import { useMemo } from "react";
import DataTable, { TableProps, TableStyles } from "react-data-table-component";
import { v4 } from "uuid";
import { handleTranslateColumns } from "../MainDataTable";

const customStyleSecondaryTable: TableStyles = {
  header: { style: {} },
  rows: {
    style: {
      fontSize: "14px",
      paddingX: "10px",
      backgroundColor: "rgb(245, 245, 245)",
    },
  },
  headCells: {
    style: {
      fontSize: "14px",
      fontWeight: "bold",
      margin: "0px",
      padding: "0px",
      outline: "0px",
      boxSizing: "border-box",
      textDecoration: "none",
      fontFamily: "Roboto, sans-serif",
      backgroundColor: "rgb(245, 245, 245)",
    },
  },
  cells: {
    style: {
      fontSize: "14px",
      margin: "0px",
      padding: "0px",
      outline: "0px",
      boxSizing: "border-box",
      textDecoration: "none",
      fontFamily: "Roboto, sans-serif",
    },
  },
  headRow: { style: { borderBottomStyle: "none", paddingX: "10px" } },
};

const getRowColor = (pstyvCode: string) => {
  switch (pstyvCode) {
    case "YAE0":
    case "YA10":
      return "#80e27e";
    case "YAE1":
    case "YA11":
      return "#ffff71";
    case "YAE2":
    case "YA12":
      return "#FFCE93";
    case "YAE3":
    case "YA13":
      return "#F6BEC1";
    default:
      return "";
  }
};

const conditionalRowStyles = [
  {
    when: (row: ISap.ItemProps) => {
      const color = getRowColor(
        getAdditionalField("PSTYV_CODE", row.adicionais)
      );
      return !!color;
    },
    style: (row: ISap.ItemProps) => {
      return {
        backgroundColor: getRowColor(
          getAdditionalField("PSTYV_CODE", row.adicionais)
        ),
      };
    },
  },
];

export const SecondaryDataTable = (props: TableProps<any>) => {
  const { columns, ...restProps } = props;
  const translatedColumns = handleTranslateColumns(columns);

  const { addSelectedRows, cleared } = useSelectedRowsStore();

  const tableId = useMemo(() => v4(), []);

  return (
    <DataTable
      key={tableId}
      conditionalRowStyles={conditionalRowStyles}
      customStyles={customStyleSecondaryTable}
      columns={translatedColumns}
      clearSelectedRows={cleared}
      // onSelectedRowsChange={(event) => {
      //   addSelectedRows(tableId, event.selectedRows);
      // }}
      {...restProps}
    />
  );
};
