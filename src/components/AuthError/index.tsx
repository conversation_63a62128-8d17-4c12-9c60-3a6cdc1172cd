import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
} from "@mui/material";
import { t } from "i18next";

export const AuthError = () => {
  const handleContactSupport = () => {
    // Você pode adicionar lógica aqui para abrir um sistema de chamados ou email
    window.open(
      "https://brfprd.service-now.com/nav_to.do?uri=%2Fincident.do%3Fsys_id%3D-1%26sys_is_list%3Dtrue%26sys_target%3Dincident%26sysparm_checked_items%3D%26sysparm_fixed_query%3D%26sysparm_group_sort%3D%26sysparm_list_css%3D%26sysparm_query%3Dactive%3Dtrue%5Eassignment_group%3D49dcb6791b82ec504ffd0f60f54bcbc1%5EORassignment_group%3D6f460c836f706a005d59d7aabb3ee4e6%5EORassignment_group%3D9571fb551bc46c94358687f1f54bcbd8%26sysparm_referring_url%3Dincident_list.do%3Fsysparm_query%3Dactive%253Dtrue%255Eassignment_group%253D49dcb6791b82ec504ffd0f60f54bcbc1%255EORassignment_group%253D6f460c836f706a005d59d7aabb3ee4e6%255EORassignment_group%253D9571fb551bc46c94358687f1f54bcbd8@99@active%3Dtrue@99@sysparm_list_mode%3Dgrid%26sysparm_target%3D%26sysparm_view%3D",
      "_blank"
    );
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100%",
      }}
    >
      <Card sx={{ maxWidth: 600, width: "100%" }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" color="error" gutterBottom align="center">
            {t("Authentication.Error.Title", "Erro de Autenticação")}
          </Typography>

          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1">
              {t(
                "Authentication.Error.Message",
                "O sistema detectou um problema de autorização. Sua conta pode não ter as permissões necessárias."
              )}
            </Typography>
          </Alert>

          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              <strong>
                {t("Authentication.Error.ContactTitle", "Suporte Técnico:")}
              </strong>
            </Typography>
            <Typography variant="body2">
              {t("Authentication.Error.ContactInfo", "📧 Grupo de Incidente:")}{" "}
              <strong>SQUAD - Global</strong>
            </Typography>
            <Typography variant="body2">
              {t("Authentication.Error.ContactSystem", "💻 Sistema:")}{" "}
              <strong>Central de Aprovações</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              {t(
                "Authentication.Error.IncludeInfo",
                "Ao reportar o problema, inclua a data/hora do erro e sua matrícula para agilizar o atendimento."
              )}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "end" }}>
              <Button
                variant="contained"
                className="button-secondary"
                onClick={handleContactSupport}
                color="secondary"
              >
                {t("Authentication.Error.ContactSupport", "Contatar Suporte")}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};
