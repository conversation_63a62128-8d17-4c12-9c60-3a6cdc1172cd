import {
  Box,
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

interface ModalApprovalReasonProps {
  open: boolean;
  onClose: () => void;
  title: string;
  subtitle: string;
  onConfirm: (reason: string) => void;
  confirmButtonText?: string;
  confirmButtonColor?:
    | "primary"
    | "secondary"
    | "error"
    | "warning"
    | "info"
    | "success";
  isRequired?: boolean;
}

export const ModalApprovalReason: React.FC<ModalApprovalReasonProps> = ({
  open,
  onClose,
  title,
  subtitle,
  onConfirm,
  confirmButtonText,
  confirmButtonColor = "primary",
  isRequired = false,
}) => {
  const { t } = useTranslation();
  const [reason, setReason] = useState<string>("");
  const [error, setError] = useState<string>("");

  const handleClose = () => {
    setReason("");
    setError("");
    onClose();
  };

  const handleConfirm = () => {
    if (isRequired && reason.trim() === "") {
      setError(t("Este campo é obrigatório"));
      return;
    }

    onConfirm(reason.trim());
    handleClose();
  };

  const handleReasonChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setReason(event.target.value);
    if (error) {
      setError("");
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          pb: 1,
          fontSize: "1.25rem",
          fontWeight: 600,
        }}
      >
        {title}
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {subtitle}
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={4}
            label={t("Motivo")}
            // placeholder={t("Motivo")}
            value={reason}
            onChange={handleReasonChange}
            error={!!error}
            helperText={error}
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 1,
              },
            }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          color="inherit"
          sx={{
            minWidth: 100,
            borderRadius: 1,
          }}
        >
          {t("Cancel")}
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          color={confirmButtonColor}
          disabled={!reason}
          sx={{
            minWidth: 100,
            borderRadius: 1,
          }}
        >
          {confirmButtonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
