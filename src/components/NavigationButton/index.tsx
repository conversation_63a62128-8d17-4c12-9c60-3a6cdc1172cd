import { Button as MUIButton } from "@mui/material";
import { ButtonProps } from "@mui/material/Button";
import { styled } from "@mui/system";
import { Link, useLocation } from "react-router-dom";

const RouterButton = styled(MUIButton)<{
  location?: ReturnType<typeof useLocation>;
  path?: string;
  active?: boolean;
}>(({ theme, location, path, active }) => {
  const isActive =
    active !== undefined ? active : location && location?.pathname === path;

  return {
    backgroundColor: isActive ? "#ffc30e" : theme.palette.primary.main,
    padding: "12px 16px",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "16px",
    opacity: 0.95,
    textTransform: "uppercase",
    minWidth: "0px",
    "&:hover": {
      backgroundColor: isActive ? "#fcd666" : theme.palette.primary.main,
    },
    "& .iconButton": {
      padding: "12px",
    },
    "& > svg": {
      fontSize: "1.2rem",
    },
  };
});

interface CustomButtonProps extends ButtonProps {
  router?: boolean;
  path?: string;
  className?: string;
  active?: boolean;
  disabled?: boolean;
}

export const NavigationButton: React.FC<CustomButtonProps> = ({
  router,
  path,
  className,
  active,
  disabled,
  ...props
}) => {
  const location = useLocation();

  if (router && path) {
    return (
      <RouterButton
        location={location}
        path={path}
        active={active}
        component={Link}
        to={path}
        className={className}
        {...props}
      />
    );
  }

  return (
    <RouterButton
      location={location}
      path={path || ""}
      active={active}
      className={className}
      onClick={props.onClick}
      disabled={disabled}
      {...props}
    />
  );
};
