import { TimePicker, TimePickerProps } from "@mui/x-date-pickers";
import { Dayjs } from "dayjs";
import React from "react";

interface TimePickerComponentProps extends TimePickerProps<Dayjs> {
  value: Dayjs;
  onChange: (newValue: Dayjs | null) => void;
}

const TimePickerComponent: React.FC<TimePickerComponentProps> = ({
  value,
  onChange,
  ...props
}) => {
  return (
    <div>
      <TimePicker
        data-testid="timeComponent"
        label="Controlled picker"
        timeSteps={{ hours: 1, minutes: 15 }}
        ampm={false}
        value={value}
        onChange={onChange}
        {...props}
      />
    </div>
  );
};

export default TimePickerComponent;
