import { useAuth } from "@/hooks";
import { AuthService } from "@/services/authService";
import theme from "@/styles/theme";
import { InfoOutlined } from "@mui/icons-material";
import { Box, Button, Modal, Stack, Typography } from "@mui/material";
import { t } from "i18next";
import React from "react";

interface SessionExpiredModalProps {
  open: boolean;
}

const SessionExpiredModal: React.FC<SessionExpiredModalProps> = ({ open }) => {
  const { setIsSessionExpired } = useAuth();

  const handleReload = () => {
    setIsSessionExpired(false);
    AuthService.manualLogin();
  };

  return (
    <Modal
      open={open}
      onClose={() => {}} // Prevent closing without action
      aria-labelledby="session-expired-title"
      aria-describedby="session-expired-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: 400,
          bgcolor: "background.paper",
          textAlign: "center",
          boxShadow: 24,
          p: 4,
          borderRadius: 2,
          outline: "none",
          "&:focus-visible": {
            outline: "none",
          },
        }}
      >
        <InfoOutlined
          sx={{ fontSize: "3.5rem", color: theme.palette.secondary.main }}
        />
        <Typography
          id="session-expired-title"
          variant="h5"
          fontWeight="bold"
          component="h2"
          gutterBottom
        >
          {t("Your.session.has.expired")}
        </Typography>

        <Typography id="session-expired-description" sx={{ mt: 2, mb: 3 }}>
          {t("For.security.reasons.your.session.was.closed")}
        </Typography>

        <Stack direction="row" spacing={2} justifyContent="center">
          <Button
            variant="contained"
            onClick={handleReload}
            color="secondary"
            sx={{ borderRadius: "20px" }}
          >
            {t("CLICK.TO.UPDATE")}
          </Button>

          {/* <Button variant="contained" onClick={handleRelogin} color="primary">
            {t("Login.Again")}
          </Button> */}
        </Stack>
      </Box>
    </Modal>
  );
};

export default SessionExpiredModal;
