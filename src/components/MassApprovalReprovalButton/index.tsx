import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { Cancel, CheckCircle } from "@mui/icons-material";
import { Box, Fab, Zoom } from "@mui/material";
import { t } from "i18next";
import { AllProcessesTypes } from "../AccordionTable";
import { ModalApprovalReason } from "../ModalApprovalReason";

interface MassApprovalReprovalButtonProps {
  handleMassReproval: () => void;
  handleConfirmMassReproval: (reason: string) => void;
  handleMassApproval: () => void;
  reprovalModalState: {
    open: boolean;
    rows: AllProcessesTypes[];
  };
  handleCloseReprovalModal: () => void;
}

export const MassApprovalReprovalButton = ({
  handleMassReproval,
  handleConfirmMassReproval,
  handleMassApproval,
  reprovalModalState,
  handleCloseReprovalModal,
}: MassApprovalReprovalButtonProps) => {
  const { selectedRows } = useSelectedRowsStore();

  return (
    <>
      <ModalApprovalReason
        open={reprovalModalState.open}
        onClose={handleCloseReprovalModal}
        title={t("Disapprove")}
        subtitle={t("Disapproval.Reason")}
        onConfirm={handleConfirmMassReproval}
        confirmButtonText={t("Disapprove")}
        confirmButtonColor="error"
        isRequired={true}
      />
      {Object.keys(selectedRows).length > 0 && (
        <Zoom in={true}>
          <Box sx={{ position: "fixed", bottom: 16, right: 16, zIndex: 1000 }}>
            <Fab
              color="primary"
              aria-label="approve"
              onClick={handleMassApproval}
              style={{ marginRight: "8px" }}
            >
              <CheckCircle />
            </Fab>
            <Fab color="error" aria-label="reject" onClick={handleMassReproval}>
              <Cancel />
            </Fab>
          </Box>
        </Zoom>
      )}
    </>
  );
};
