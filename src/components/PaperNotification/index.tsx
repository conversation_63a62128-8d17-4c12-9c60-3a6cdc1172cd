import { Grid, Switch, Typography } from "@mui/material";

import Paper from "@mui/material/Paper";
import { t } from "i18next";

import { TimePicker } from "@mui/x-date-pickers/TimePicker";

import { useState } from "react";

export const PaperNotification = () => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Paper elevation={3}>
        <div style={{ padding: "1rem 2rem" }}>
          <Grid
            container
            direction="column"
            sx={{
              justifyContent: "flex-start",
              alignItems: "flex-start",
            }}
          >
            <Grid
              container
              direction="row"
              sx={{
                justifyContent: "flex-start",
                alignItems: "center",
              }}
            >
              <Typography
                component="span"
                sx={{ fontWeight: "bold", fontSize: "2rem" }}
              >
                {t("Notifications")}
              </Typography>
            </Grid>
            <Grid
              container
              direction="row"
              sx={{
                justifyContent: "flex-start",
                alignItems: "center",
                marginTop: "3rem",
              }}
            >
              <Typography
                component="span"
                sx={{ fontWeight: "", fontSize: "1.5rem" }}
              >
                {t("Notificações Microsoft Teams")}
              </Typography>
              <Switch color="secondary" />
            </Grid>
            <Grid
              container
              direction="row"
              sx={{
                justifyContent: "flex-start",
                alignItems: "center",
                marginTop: "1rem",
              }}
            >
              <Typography
                component="span"
                sx={{ fontWeight: "", fontSize: "1.5rem" }}
              >
                {t("Application.Notification")}
              </Typography>
              <Switch color="secondary" />
            </Grid>
            <Grid
              container
              direction="row"
              sx={{
                justifyContent: "flex-start",
                alignItems: "center",
                marginTop: "1rem",
              }}
            >
              <Typography
                component="span"
                sx={{ fontWeight: "", fontSize: "1.5rem" }}
              >
                {t("Notification.interval")}
              </Typography>
              <TimePicker
                label="Basic time picker"
                sx={{ borderRadius: "20rem" }}
              />
              <TimePicker
                label="Basic time picker"
                sx={{ borderRadius: "20rem" }}
              />
            </Grid>
          </Grid>
        </div>
      </Paper>
    </>
  );
};
