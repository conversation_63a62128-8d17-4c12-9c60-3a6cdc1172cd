import { APIAccountConfig } from "@/api";
import { useAuth } from "@/hooks";
import { Avatar, Box, IconButton, Menu, MenuItem } from "@mui/material";
import i18next, { t } from "i18next";
import { useCallback, useEffect, useState } from "react";
import i18n from "../../hooks/translation";

export const LanguageMenu = () => {
  const [currentLangImg, setCurrentLangImg] = useState<string>("pt-BR");

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const { user } = useAuth();

  const icon = useCallback(() => {
    if (currentLangImg === "pt-BR") {
      return "data:image/png;base64,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";
    } else if (currentLangImg === "en-US") {
      return "data:image/png;base64,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";
    } else if (currentLangImg === "es-ES") {
      return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAADn0lEQVR4nO3Yy24bZRwF8D9lAysQrSgUpDglaZumJXZ8i534ksSO49u4Teok7oZnaMM+ohGsmkaFV4AWhMKGB2l8KaSOnUjgGd/iy3hsZ9UeNFOBKgvJHuzYn5CPdJaWfmcukucjGmaYYboOiN4SfRtmyb+2WfOt/Sj6wgnRe4cXl8NN0bParHpW+apnNS4urTwVXSubopszy7+hQafhD38iBda/kfwbx1JgHZJ/DTWf3DBq3jBqy3cgelZfd2kFovu20qrrFqqL3HFlnvu66OIu9R1eC0Qu1LnIbi24fioFNqAeH0J1QS6HynywWXYGdmuOwIW+4OuhiE8KRngpKMO7xXOoOIOoOAMoO/z5it1/+8zgCIffrofuflfnIug1vuIIoGz3o2zzoWTzPgZtnes1/l0pFPnlrPFluXNelKzLe8cOxzu9wW9tnauHInt9w88uo2T14GTG/at817seUOfufttvfMmyJA9A0ex61BW+EYoEB4U/MbtxYnLhxLjw317sSuiL9yUukhksfhFFw4JQnVn6QPWAl0e08+qI8Crd0lRLD99osqUvWnrQ0t9fF3/3t5Y+/6c7qvBI08WXaWowggeeUwMxutj51U/TNkN4IEFAnB6oGXDIFD6hNAV08AcQaTIyiIfSGBnaDziiL5nEx5UB99s/Pin6iUl8XBnwpJPnP8EkPq401n7AIWUZxQMx4jsZcMooHojSaecDWMPHlDY6GSAwigdilGk/IEkJRvHAficvcZKeMomPKgO+bzsAh7TJJD6q9F77AQdkZBQPxEjfdoDyGL2gJHP4fUp1hFcGJGmbKXyUgGf0lboPmgNqMIPfpwb26cOOByh34YAeMoIHntFDUpuyy/VexR74U+UHOIr6eRSnnSjo5DpQ0NpRmLIh//kc8jdnkb8xi9ykFbnrFuQmZpC7Zkb2qhnZKyZkx40Qxg0QxvQQPpuGcHka/KhW+OPT6+o/6pURNp9/sHgd+JGpW9RNSnPe3YHhNVPqH53WyAet8lnlAK78zyDqzSGvfNBasrj3+ok/HunR4e6bd6Jocj0+a3xGo93t2ZX/t5QMC96CYZ7vNT6j0eV4jTZE/YhoXDxfnHY+Kmidze7xugav0e5kLhnPU79T0Ns+zmvt24UpW1otnr+sS/Gjugf5EeNHxEKESYsxd8N6PztpfZKdsESzE+ZM7pqpmb1qamavGDPCmCEqjBt+EMb094RRrXHQ3mGG+b/kL/wvcyG78K8GAAAAAElFTkSuQmCC";
    }
  }, [currentLangImg]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const setTitleInHTML = useCallback((title: string) => {
    const titleEl = document.querySelector("head title");
    titleEl && (titleEl.innerHTML = title);
  }, []);

  const handleChangeLanguage = (language: string) => {
    return () => {
      i18n.changeLanguage(language);
      setTitleInHTML(t("Approvals.Center"));
      setCurrentLangImg(language);
      saveLanguageOption(language);
    };
  };

  const handleLanguageDefault = (lang: string) => {
    if (lang.includes("pt")) {
      return "pt-BR";
    }
    if (lang.includes("es")) {
      return "es-ES";
    }
    return "en-US";
  };

  const saveLanguageOption = (lang: string) => {
    APIAccountConfig.registerAccountConfig(String(user.accountId), lang);
  };

  useEffect(() => {
    if (user.employeeID) {
      const lng = user.languageOption ?? user.language;
      if (!user.languageOption) {
        saveLanguageOption(lng);
      }

      const lngDefault = handleLanguageDefault(lng);

      i18n.changeLanguage(lngDefault);
      setCurrentLangImg(lngDefault);
      setTitleInHTML(i18next.getFixedT(lngDefault)("Approvals Center"));
    }
  }, [user]);

  return (
    <div data-testid="languageComponent">
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          textAlign: "center",
        }}
      >
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{ ml: 2 }}
          aria-controls={open ? "language-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={open ? "true" : undefined}
        >
          <Avatar sx={{ width: 32, height: 32 }} src={icon()} />
        </IconButton>
      </Box>
      <Menu
        anchorEl={anchorEl}
        id="language-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
              mt: 1.5,
              "& .MuiAvatar-root": {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              "&::before": {
                content: '""',
                display: "block",
                position: "absolute",
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: "background.paper",
                transform: "translateY(-50%) rotate(45deg)",
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <MenuItem onClick={handleChangeLanguage("pt-BR")}>
          <img
            src="data:image/png;base64,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"
            alt="brazil-circular"
            style={{ maxHeight: "26px", marginRight: "1rem" }}
          ></img>
          Português
        </MenuItem>
        <MenuItem onClick={handleChangeLanguage("en-US")}>
          <img
            src="data:image/png;base64,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"
            alt="great-britain-circular"
            style={{ maxHeight: "26px", marginRight: "1rem" }}
          ></img>
          English
        </MenuItem>
        <MenuItem onClick={handleChangeLanguage("es-ES")}>
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAADn0lEQVR4nO3Yy24bZRwF8D9lAysQrSgUpDglaZumJXZ8i534ksSO49u4Teok7oZnaMM+ohGsmkaFV4AWhMKGB2l8KaSOnUjgGd/iy3hsZ9UeNFOBKgvJHuzYn5CPdJaWfmcukucjGmaYYboOiN4SfRtmyb+2WfOt/Sj6wgnRe4cXl8NN0bParHpW+apnNS4urTwVXSubopszy7+hQafhD38iBda/kfwbx1JgHZJ/DTWf3DBq3jBqy3cgelZfd2kFovu20qrrFqqL3HFlnvu66OIu9R1eC0Qu1LnIbi24fioFNqAeH0J1QS6HynywWXYGdmuOwIW+4OuhiE8KRngpKMO7xXOoOIOoOAMoO/z5it1/+8zgCIffrofuflfnIug1vuIIoGz3o2zzoWTzPgZtnes1/l0pFPnlrPFluXNelKzLe8cOxzu9wW9tnauHInt9w88uo2T14GTG/at817seUOfufttvfMmyJA9A0ex61BW+EYoEB4U/MbtxYnLhxLjw317sSuiL9yUukhksfhFFw4JQnVn6QPWAl0e08+qI8Crd0lRLD99osqUvWnrQ0t9fF3/3t5Y+/6c7qvBI08WXaWowggeeUwMxutj51U/TNkN4IEFAnB6oGXDIFD6hNAV08AcQaTIyiIfSGBnaDziiL5nEx5UB99s/Pin6iUl8XBnwpJPnP8EkPq401n7AIWUZxQMx4jsZcMooHojSaecDWMPHlDY6GSAwigdilGk/IEkJRvHAficvcZKeMomPKgO+bzsAh7TJJD6q9F77AQdkZBQPxEjfdoDyGL2gJHP4fUp1hFcGJGmbKXyUgGf0lboPmgNqMIPfpwb26cOOByh34YAeMoIHntFDUpuyy/VexR74U+UHOIr6eRSnnSjo5DpQ0NpRmLIh//kc8jdnkb8xi9ykFbnrFuQmZpC7Zkb2qhnZKyZkx40Qxg0QxvQQPpuGcHka/KhW+OPT6+o/6pURNp9/sHgd+JGpW9RNSnPe3YHhNVPqH53WyAet8lnlAK78zyDqzSGvfNBasrj3+ok/HunR4e6bd6Jocj0+a3xGo93t2ZX/t5QMC96CYZ7vNT6j0eV4jTZE/YhoXDxfnHY+Kmidze7xugav0e5kLhnPU79T0Ns+zmvt24UpW1otnr+sS/Gjugf5EeNHxEKESYsxd8N6PztpfZKdsESzE+ZM7pqpmb1qamavGDPCmCEqjBt+EMb094RRrXHQ3mGG+b/kL/wvcyG78K8GAAAAAElFTkSuQmCC"
            alt="spain-circular"
            style={{ maxHeight: "26px", marginRight: "1rem" }}
          ></img>
          Español
        </MenuItem>
      </Menu>
    </div>
  );
};

export default LanguageMenu;
