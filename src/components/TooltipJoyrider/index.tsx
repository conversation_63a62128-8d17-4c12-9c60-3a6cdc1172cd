import React, { forwardRef } from "react";
import { useTranslation } from "react-i18next";

import { BeaconRenderProps, TooltipRenderProps } from "react-joyride";
import {
  Beacon,
  Button,
  FormattedMessage,
  TooltipBody,
  TooltipContent,
  Toolt<PERSON>Footer,
  TooltipTitle,
} from "./styles";

const Tooltip: React.FC<TooltipRenderProps> = ({
  continuous,
  index,
  step,
  backProps,
  closeProps,
  primaryProps,
  tooltipProps,
  skipProps,
  isLastStep,
}) => {
  const { t } = useTranslation();
  return (
    <TooltipBody {...tooltipProps}>
      {step.title && <TooltipTitle>{step.title}</TooltipTitle>}
      <TooltipContent>{step.content}</TooltipContent>
      <TooltipFooter>
        {!isLastStep && (
          <Button {...skipProps} title={t("skip")}>
            <FormattedMessage id="skip">{t("skip")}</FormattedMessage>
          </Button>
        )}
        {continuous && (
          <Button
            {...primaryProps}
            title={isLastStep ? t("conclude") : t("next")}
          >
            <FormattedMessage id="next">
              {isLastStep ? t("conclude") : t("next")}
            </FormattedMessage>
          </Button>
        )}
        {index > 0 && (
          <Button {...backProps} title={t("back")}>
            <FormattedMessage id="back">{t("back")}</FormattedMessage>
          </Button>
        )}

        <Button {...closeProps} title={t("close")}>
          <FormattedMessage id="close">
            {/* <FiX size={16} color={"rgba(0, 0, 0, 0.54)"} /> */}
          </FormattedMessage>
        </Button>
        <div style={{ clear: "both" }}></div>
      </TooltipFooter>
    </TooltipBody>
  );
};

export const BeaconJoyrider = forwardRef<HTMLSpanElement, BeaconRenderProps>(
  (props, ref) => (
    <span
      ref={ref}
      className="joyride-beacon"
      data-testid="joyride-beacon"
      {...props}
    >
      <Beacon {...props} data-step={props.step?.placement} />
    </span>
  )
);

export default Tooltip;
