/** @jsxImportSource @emotion/react */
import { keyframes } from "@emotion/react";
import styled from "@emotion/styled";

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  55% {
    background-color: rgba(129, 41, 144,0.9);
    transform: scale(1.6);
  }
`;

export const TooltipBody = styled.div`
  box-shadow: 0px 1px 14px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 24px;
  background: #fff;
  position: relative;
  max-width: 426px;
  @media (max-width: 768px) {
    max-width: 295px;
  }
`;
export const TooltipTitle = styled.div`
  background: blue;
`;
export const TooltipContent = styled.div`
  font-family: Roboto;
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.87);
  margin-top: 30px;
`;
export const TooltipFooter = styled.div`
  margin-top: 40px;
`;
export const Button = styled.button`
  background: green;
  outline: none;
  border: none;

  &[data-action="close"] {
    position: absolute;
    top: 24px;
    right: 24px;
    background: transparent;
  }
  &[data-action="close"] {
    position: absolute;
    top: 24px;
    right: 24px;
    background: transparent;
  }
  &[data-action="primary"] {
    color: #fff;
    padding: 7px 10px;
    height: 36px;
    background: #812990;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 200px;
    float: right;
  }
  &[data-action="back"] {
    color: #812990;
    padding: 7px 10px;
    height: 36px;
    background: #fff;
    float: right;
    margin-right: 16px;
  }
  &[data-action="skip"] {
    color: rgba(0, 0, 0, 0.87);
    padding: 7px 10px;
    height: 36px;
    background: #fff;
    float: left;
  }
`;
export const FormattedMessage = styled.div`
  /* background: purple; */
  text-transform: uppercase;
`;

export const Beacon = styled.button`
  animation: ${pulse} 1s ease-in-out infinite;
  background-color: rgba(129, 41, 144, 0.6);
  border-radius: 50%;
  display: inline-block;
  height: 16px;
  width: 16px;
  outline: none;
  border: none;
`;

