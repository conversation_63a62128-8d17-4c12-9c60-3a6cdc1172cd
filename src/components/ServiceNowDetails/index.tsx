import { Box, CircularProgress, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { getDocumentDetails } from "../../api/serviceNow";
import { IServiceNow } from "../../interfaces";

interface ServiceNowDetailsProps {
  row: IServiceNow.ItemProps;
}

export const ServiceNowDetails = ({ row }: ServiceNowDetailsProps) => {
  const [loading, setLoading] = useState(true);
  const [details, setDetails] = useState<string>("");

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        setLoading(true);
        const response = await getDocumentDetails(row.Registro);
        if (response.success && response.data) {
          setDetails(response.data);
        }
      } catch (error) {
        console.error("Erro ao buscar detalhes do ServiceNow:", error);
      } finally {
        setLoading(false);
      }
    };

    if (row.Registro) {
      fetchDetails();
    }
  }, [row.Registro]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!details) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <Typography variant="body1">
          Nenhum detalhe encontrado para esta solicitação.
        </Typography>
      </Box>
    );
  }

  return (
    <div
      className="content servicenow"
      dangerouslySetInnerHTML={{ __html: details }}
    />
  );
};

export default ServiceNowDetails;
