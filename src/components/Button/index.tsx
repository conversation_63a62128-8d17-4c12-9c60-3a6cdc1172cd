import { Button as ButtonMui } from "@mui/material";
import { SxProps, Theme } from "@mui/material/styles";
import React from "react";

interface ButtonProps {
  color: "inherit" | "primary" | "secondary" | "error" | "success";
  textColor?: string;
  text: string;
  onClick?: () => void;
  sx?: SxProps<Theme>;
  type?: "button" | "submit" | "reset";
  variant?: "contained" | "outlined" | "text";
}

const Button: React.FC<ButtonProps> = ({
  color,
  textColor,
  text,
  onClick,
  sx,
  variant,
  type = "button",
}) => {
  return (
    <ButtonMui
      type={type}
      variant={variant}
      onClick={onClick}
      color={color}
      sx={{
        color: textColor ?? "#000",
        borderRadius: "5rem",
        ...sx,
      }}
    >
      {text}
    </ButtonMui>
  );
};

export default Button;
