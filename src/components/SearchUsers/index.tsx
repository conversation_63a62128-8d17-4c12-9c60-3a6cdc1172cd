import { useCards } from "@/stores/cardsStore";
import { Menu } from "@mui/material";
import { useAuth, useSearchUsers } from "../../hooks";
import { ISearchUser } from "../../interfaces";
import { Select } from "../Select";

export const SearchUsers = ({
  action,
  anchorEl,
  setAnchorEl,
  open,
}: ISearchUser.SearchUsersProps) => {
  const { getTokenForThisUser, login, user } = useAuth();
  const { setCards, resetAribaApiCalled } = useCards(user);
  const {
    selectedValue,
    options,
    isLoadingOptions,
    handleChange,
    handleInputChange,
    loadMore,
    setLoadMore,
    resetValues,
    loadMoreResults,
  } = useSearchUsers(
    getTokenForThisUser,
    login,
    setCards,
    action,
    resetAribaApiCalled
  );

  const handleClose = () => {
    if (setAnchorEl) {
      setAnchorEl(null);
    }
  };

  return (
    <Menu
      disablePortal
      open={!!open}
      anchorEl={anchorEl}
      transformOrigin={{ horizontal: "center", vertical: "top" }}
      anchorOrigin={{ horizontal: "center", vertical: "bottom" }}
      slotProps={{
        paper: {
          style: {
            width: "280px",
            maxWidth: "unset",
          },
        },
        list: {
          style: {
            paddingTop: 0,
            paddingBottom: 0,
          },
        },
      }}
      id="search-users"
      onClose={handleClose}
      onClick={handleClose}
      sx={{ width: "280px" }}
    >
      <div onClick={(event) => event.stopPropagation()}>
        <Select
          value={selectedValue[0]?.name ?? ""}
          options={options}
          onChange={(value) => handleChange(value, handleClose)}
          onInputChange={handleInputChange}
          isLoadingOptions={isLoadingOptions}
          loadMore={loadMore}
          setLoadMore={setLoadMore}
          loadMoreResults={loadMoreResults}
          resetValues={resetValues}
        />
      </div>
    </Menu>
  );
};
