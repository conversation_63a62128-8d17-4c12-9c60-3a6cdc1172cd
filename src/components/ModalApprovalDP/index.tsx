import { ISap } from "@/interfaces";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

interface ModalApprovalDPProps {
  open: boolean;
  onClose: () => void;
  title: string;
  subtitle: string;
  onConfirm: (reason: string, selectedValue: string) => void;
  confirmButtonText?: string;
  confirmButtonColor?:
    | "primary"
    | "secondary"
    | "error"
    | "warning"
    | "info"
    | "success";
  isRequired?: boolean;
  options: ISap.DPOptions[];
  selectLabel?: string;
}

export const ModalApprovalDP: React.FC<ModalApprovalDPProps> = ({
  open,
  onClose,
  title,
  subtitle,
  onConfirm,
  confirmButtonText,
  confirmButtonColor = "primary",
  isRequired = false,
  options,
  selectLabel,
}) => {
  const { t } = useTranslation();
  const [reason, setReason] = useState<string>("");
  const [selectedValue, setSelectedValue] = useState<string>("");
  const [reasonError, setReasonError] = useState<string>("");
  const [selectError, setSelectError] = useState<string>("");

  const handleClose = () => {
    setReason("");
    setSelectedValue("");
    setReasonError("");
    setSelectError("");
    onClose();
  };

  const handleConfirm = () => {
    let hasError = false;

    if (isRequired && reason.trim() === "") {
      setReasonError(t("Este campo é obrigatório"));
      hasError = true;
    }

    if (selectedValue === "") {
      setSelectError(t("Por favor, selecione uma opção"));
      hasError = true;
    }

    if (hasError) {
      return;
    }

    onConfirm(reason.trim(), selectedValue);
    handleClose();
  };

  const handleReasonChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setReason(event.target.value);
    if (reasonError) {
      setReasonError("");
    }
  };

  const handleSelectChange = (event: any) => {
    setSelectedValue(event.target.value);
    if (selectError) {
      setSelectError("");
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          pb: 1,
          fontSize: "1.25rem",
          fontWeight: 600,
        }}
      >
        {title}
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {subtitle}
          </Typography>

          <FormControl fullWidth error={!!selectError} sx={{ mb: 2 }}>
            <InputLabel id="select-option-label">
              {selectLabel || t("Selecione uma opção")}
            </InputLabel>
            <Select
              labelId="select-option-label"
              value={selectedValue}
              label={selectLabel || t("Selecione uma opção")}
              onChange={handleSelectChange}
              sx={{
                borderRadius: 1,
              }}
            >
              {options.map((option) => (
                <MenuItem key={option.id} value={option.name}>
                  {option.name}
                </MenuItem>
              ))}
            </Select>
            {selectError && (
              <Typography
                variant="caption"
                color="error"
                sx={{ mt: 0.5, ml: 1 }}
              >
                {selectError}
              </Typography>
            )}
          </FormControl>

          <TextField
            fullWidth
            multiline
            rows={4}
            label={t("Motivo")}
            value={reason}
            onChange={handleReasonChange}
            error={!!reasonError}
            helperText={reasonError}
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 1,
              },
            }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          color="inherit"
          sx={{
            minWidth: 100,
            borderRadius: 1,
          }}
        >
          {t("Cancel")}
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          color={confirmButtonColor}
          disabled={!reason || !selectedValue}
          sx={{
            minWidth: 100,
            borderRadius: 1,
          }}
        >
          {confirmButtonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
