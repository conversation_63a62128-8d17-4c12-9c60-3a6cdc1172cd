import { ApprovalOrigin } from "@/api/approval/approval.service";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useCards } from "@/stores/cardsStore";
import { is } from "date-fns/locale";
import React, { FC, useCallback, useState, useTransition } from "react";
import { TableColumn } from "react-data-table-component";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { MainDataTable, SecondaryDataTable } from "..";
import { APISap } from "../../api";
import { processesProps } from "../../constant";
import {
  DataPropsAL,
  getUniqueDocAL,
} from "../../constant/processesProps/sap/AL";
import { useAuth } from "../../hooks";
import { ISap } from "../../interfaces";
import { SapService } from "../../services";
import { getAdditionalField } from "../../utils/GetItemsSap";
import { ActionButtons } from "../ActionButtons";
import Loading from "../Loading";
import { ModalApprovalDP } from "../ModalApprovalDP";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";

export const DataTableSap: FC<{
  headerData: ISap.ItemProps[] | any;
  detailData?: ISap.ItemProps[] | any;
  process: string;
}> = ({ headerData, detailData, process }) => {
  const {
    headerColumns,
    detailColumns,
    documentDetailHtml,
    title,
    hasDetailModal,
    detailModalHeader,
    detailModalContent,
    hasDetailRoute,
    approveItems,
    origin,
    additional1,
    additional2,
  } = processesProps(process)[0] || {};

  const { user } = useAuth();
  const { loadingDataTable } = useCards(user);
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();

  const [modalState, setModalState] = useState<{
    open: boolean;
    data: ISap.ItemProps | ISap.ItemProps[];
  }>({ open: false, data: [] as ISap.ItemProps[] });

  const [expandedRows, setExpandedRows] = useState<ISap.ItemProps[]>([]);
  const [selectedDetailRows, setSelectedDetailRows] = useState<
    ISap.ItemProps[]
  >([]);

  const [approvalModalState, setApprovalModalState] = useState<{
    open: boolean;
    type: "approve" | "disapprove" | "return" | null;
    row: ISap.ItemProps | null;
  }>({
    open: false,
    type: null,
    row: null,
  });

  const [approvalDPModalState, setApprovalDPModalState] = useState<{
    open: boolean;
    row: ISap.ItemProps | null;
  }>({
    open: false,
    row: null,
  });

  const {
    isLoading: isLoadingDPOptions,
    mutate: mutateDPListOptions,
    data,
  } = useMutation(async () => {
    const { data } = await APISap.getDpOptions();
    return data;
  });

  const handleRowExpansion = useCallback(
    (_row: ISap.ItemProps) => {
      const documentDetails = SapService.filterDocumentDetails(
        detailData ?? [],
        _row.documento
      );
      if (typeof documentDetailHtml === "function") {
        const content = documentDetailHtml(documentDetails);
        if (process === "OB") {
          return (
            <div>
              {React.isValidElement(content) ? (
                content
              ) : (
                <div style={{ padding: "16px" }}>{content}</div>
              )}
              <SecondaryDataTable
                columns={detailColumns as TableColumn<ISap.ItemProps>[]}
                data={documentDetails}
                selectableRows={approveItems}
              />
            </div>
          );
        } else if (React.isValidElement(content)) {
          return content;
        } else if (content) {
          // Se não for um elemento React, mas tiver conteúdo, envolvê-lo em um div
          return <div style={{ padding: "16px" }}>{content}</div>;
        }
      }

      return null;
    },
    [documentDetailHtml]
  );

  const handleOpenModal = (data: ISap.ItemProps | ISap.ItemProps[]) => {
    setModalState({ open: true, data });
  };

  const handleCloseModal = () => {
    setModalState({ open: false, data: [] as ISap.ItemProps[] });
  };

  const handleModalApprove = async (
    data: ISap.ItemProps | ISap.ItemProps[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    if (process === "DP") {
      mutateDPListOptions(undefined, {
        onSuccess: () => {
          handleOpenApprovalDPModal(row);
        },
      });
    } else {
      handleApprovalReprovalDocument(row, "A");
    }
    handleCloseModal();
  };

  const handleModalReject = (data: ISap.ItemProps | ISap.ItemProps[]) => {
    const row = Array.isArray(data) ? data[0] : data;
    handleOpenApprovalModal(row, "disapprove");
    handleCloseModal();
  };

  const handleOpenApprovalModal = (
    row: ISap.ItemProps,
    type: "approve" | "disapprove" | "return"
  ) => {
    setApprovalModalState({
      open: true,
      type,
      row,
    });
  };

  const handleCloseApprovalModal = () => {
    setApprovalModalState({
      open: false,
      type: null,
      row: null,
    });
  };

  const handleOpenApprovalDPModal = (row: ISap.ItemProps) => {
    setApprovalDPModalState({
      open: true,
      row,
    });
  };

  const handleCloseApprovalDPModal = () => {
    setApprovalDPModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (reason: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status =
        approvalModalState.type === "approve"
          ? "A"
          : approvalModalState.type === "return"
          ? "B"
          : "R";
      const isDetail = !!(approveItems && detailColumns); // Assumindo que é sempre false para a modal

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        isDetail,
        reason
      );
      handleCloseApprovalModal();
    }
  };

  const handleConfirmApprovalDPModal = async (
    reason: string,
    selectedValue: string
  ) => {
    if (approvalDPModalState.row) {
      const status = "A";
      const isDetail = false;

      await handleApprovalReprovalDocumentDP(
        approvalDPModalState.row,
        status,
        isDetail,
        reason,
        selectedValue
      );
      handleCloseApprovalDPModal();
    }
  };

  const { approveReproveDocument, isLoading } =
    useApprovalDocument<ISap.ApprovalReprovalParams>();

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: ISap.ItemProps,
      status: "A" | "R" | "B",
      isDetail: boolean = false,
      reason?: string
    ) => {
      const rowData = {
        ...row,
        additional1: additional1?.(row),
        additional2: additional2?.(row),
        item: isDetail ? row.item : "",
      };

      approveReproveDocument({
        rowData,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [process, origin, is]
  );

  const handleApprovalReprovalDocumentDP = useCallback(
    async (
      row: ISap.ItemProps,
      status: "A" | "R",
      isDetail: boolean,
      reason?: string,
      selectedValue?: string
    ) => {
      const rowData = {
        ...row,
        additional1: selectedValue,
        additional2: additional2?.(row),
        item: isDetail ? row.item : "",
      };

      approveReproveDocument({
        rowData,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [
      process,
      approveItems,
      detailData,
      headerData,
      origin,
      t,
      additional1,
      additional2,
    ]
  );

  const renderExpandableRowsComponent = useCallback(
    ({ data }: { data: ISap.ItemProps }) => {
      const row = data as ISap.ItemProps;
      if (!detailData?.length || !detailColumns) return null;

      const documentDetails = SapService.filterDocumentDetails(
        detailData,
        row.documento
      );

      return (
        <SecondaryDataTable
          columns={detailColumns as TableColumn<ISap.ItemProps>[]}
          data={documentDetails}
          selectableRows={approveItems}
        />
      );
    },
    [
      detailData,
      detailColumns,
      loadingDataTable,
      approveItems,
      selectedDetailRows,
    ]
  );

  const processesWithItemsInModal = ["DV", "PR", "MI", "AL", "NC", "LC"];
  const inventaryProcessesWithAdditional1 = ["RA", "IV", "ID", "IM", "RI"];

  const getDocumentNumber = (process: string, row: ISap.ItemProps): string => {
    switch (process) {
      case "VX":
        return getAdditionalField("ACNUM", row.adicionais);
      case "OX":
        return getAdditionalField("TICKET", row.adicionais);
      case "AL":
        return getAdditionalField("Transporte", row.adicionais);
      default:
        return row.documento;
    }
  };

  const handleDataToModal = useCallback(
    (row: ISap.ItemProps) => {
      if (!!hasDetailRoute) {
        const documentNumber = getDocumentNumber(process, row);

        const additional1 = inventaryProcessesWithAdditional1.includes(process)
          ? getAdditionalField("EXERCICIO", row.adicionais)
          : "";

        startTransition(() => {
          SapService.getDocumentDetails(
            documentNumber,
            process,
            row,
            additional1
          )
            .then((data) => {
              if (data) {
                handleOpenModal(data);
              }
            })
            .catch((error) => console.error(error));
        });
      } else if (processesWithItemsInModal.includes(process)) {
        const document = Array.isArray(row)
          ? row[0]?.documento
          : row?.documento;

        if (process === "AL") {
          const modalALData = getUniqueDocAL(headerData);
          if (modalALData.length > 0) {
            const modalAL: DataPropsAL = modalALData[0];
            handleOpenModal(modalAL.object);
          }
        }
        const modalDetailsWithItems = headerData.filter(
          (header: ISap.ItemProps) =>
            header.documento?.toString().trim() === document?.toString().trim()
        );

        handleOpenModal(modalDetailsWithItems);
      } else {
        handleOpenModal(row);
      }
    },
    [hasDetailRoute, process, headerData]
  );

  const addActionColumn = useCallback(
    (
      columns: TableColumn<ISap.ItemProps>[],
      hasDetailModal: boolean,
      isDetail: boolean = false
    ) => {
      const actionsColumnExists = columns?.some(
        (column) => column.id === "actions"
      );

      if (!actionsColumnExists) {
        columns?.push({
          id: "actions",
          width: "100px",
          maxWidth: "150px !important",
          cell: (row: ISap.ItemProps) => {
            // Para PR: header só mostra abrir modal, detalhes mostram aprovar/reprovar
            if (process === "PR") {
              if (isDetail) {
                // Detalhes: aprovar/reprovar
                return (
                  !user.onlyReading && (
                    <div>
                      <ActionButtons<ISap.ItemProps>
                        row={row}
                        showApproveButton={true}
                        showDisapproveButton={true}
                        onApprove={async () => {
                          handleApprovalReprovalDocument(row, "A", isDetail);
                        }}
                        onDisapprove={() => {
                          handleOpenApprovalModal(row, "disapprove");
                        }}
                        showOpenModalButton={false}
                        onOpenModal={() => {}}
                        showOpenUrlButton={false}
                      />
                    </div>
                  )
                );
              } else {
                // Header: só abrir modal
                return (
                  !user.onlyReading && (
                    <div>
                      <ActionButtons<ISap.ItemProps>
                        row={row}
                        showApproveButton={false}
                        showDisapproveButton={false}
                        onApprove={() => {}}
                        onDisapprove={() => {}}
                        showOpenModalButton={true}
                        onOpenModal={() => handleDataToModal(row)}
                        showOpenUrlButton={false}
                      />
                    </div>
                  )
                );
              }
            }
            // Outros processos: comportamento padrão
            // Para NC, mostrar apenas o botão undo (NC Action), ocultar reprovar
            if (process === "NC") {
              return (
                !user.onlyReading && (
                  <div>
                    <ActionButtons<ISap.ItemProps>
                      row={row}
                      showApproveButton={true}
                      onApprove={async () => {
                        handleApprovalReprovalDocument(row, "A", isDetail);
                      }}
                      showDisapproveButton={false}
                      showOpenModalButton={!isDetail && hasDetailModal}
                      onOpenModal={() => handleDataToModal(row)}
                      showOpenUrlButton={false}
                      isSapNcProcess={true}
                      onNCAction={() => {
                        handleOpenApprovalModal(row, "return");
                      }}
                    />
                  </div>
                )
              );
            }
            // Outros processos: comportamento padrão
            return (
              !user.onlyReading && (
                <div>
                  <ActionButtons<ISap.ItemProps>
                    row={row}
                    onApprove={async () => {
                      if (process === "DP") {
                        mutateDPListOptions(undefined, {
                          onSuccess: () => {
                            handleOpenApprovalDPModal(row);
                          },
                        });
                      } else {
                        handleApprovalReprovalDocument(row, "A", isDetail);
                      }
                    }}
                    onDisapprove={() => {
                      handleOpenApprovalModal(row, "disapprove");
                    }}
                    showOpenModalButton={!isDetail && hasDetailModal}
                    onOpenModal={() => handleDataToModal(row)}
                    showOpenUrlButton={false}
                  />
                </div>
              )
            );
          },
        });
      }
    },
    [
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      handleOpenApprovalDPModal,
      user.onlyReading,
      process,
    ]
  );

  // Adicionar colunas de ações
  if (!approveItems || process === "FH" || process === "PR") {
    addActionColumn(
      headerColumns as TableColumn<ISap.ItemProps>[],
      !!hasDetailModal
    );
  }

  if (approveItems && detailColumns) {
    addActionColumn(
      detailColumns as TableColumn<ISap.ItemProps>[],
      false,
      true
    );
  }

  return (
    <div>
      <MainDataTable
        expandedRows={expandedRows}
        rowKey={(row: ISap.ItemProps) => row.documento}
        columns={headerColumns as TableColumn<ISap.ItemProps>[]}
        data={headerData}
        expandableRows={!!detailData?.length}
        selectableRowsNoSelectAll={process === "FJ"}
        selectableRows={process !== "DP"}
        onRowExpandToggled={(expanded: boolean, row: ISap.ItemProps) => {
          if (expanded) {
            setExpandedRows((prev) => [...prev, row]);
          } else {
            setExpandedRows((prev) =>
              prev.filter(
                (expandedRow) => expandedRow.documento !== row.documento
              )
            );
          }
        }}
        expandableRowsComponent={({ data }: { data: ISap.ItemProps }) => {
          const customContent = handleRowExpansion(data);

          if (customContent) {
            return customContent;
          }

          return detailColumns ? renderExpandableRowsComponent({ data }) : null;
        }}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            detailModalContent={detailModalContent}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={
          approvalModalState.type === "approve"
            ? t("Approve")
            : approvalModalState.type === "return"
            ? t("Return")
            : t("Disapprove")
        }
        subtitle={
          approvalModalState.type === "approve"
            ? t("Approval.Reason")
            : approvalModalState.type === "return"
            ? t("Return.Reason")
            : t("Disapproval.Reason")
        }
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={
          approvalModalState.type === "approve"
            ? t("Approve")
            : approvalModalState.type === "return"
            ? t("Return")
            : t("Disapprove")
        }
        confirmButtonColor={
          approvalModalState.type === "approve"
            ? "success"
            : approvalModalState.type === "return"
            ? "secondary"
            : "error"
        }
        isRequired={
          approvalModalState.type === "disapprove" ||
          approvalModalState.type === "return"
        }
      />

      <ModalApprovalDP
        open={approvalDPModalState.open}
        onClose={handleCloseApprovalDPModal}
        title={t("Approve")}
        subtitle={t("Approval.Reason")}
        onConfirm={handleConfirmApprovalDPModal}
        confirmButtonText={t("Approve")}
        confirmButtonColor="success"
        isRequired={true}
        options={data || []}
        selectLabel={t("Select approval type")}
      />

      <Loading open={isLoading || isLoadingDPOptions || isPending} />
    </div>
  );
};
