import { App<PERSON><PERSON>, <PERSON>, Divider, Toolbar, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { MouseEvent, useState } from "react";
import { useTranslation } from "react-i18next";
import { SearchUsers } from "../../components";
import { useAuth } from "../../hooks";
import LanguageMenu from "../LanguageMenu";

export const Header = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { t } = useTranslation();
  const { user, isLoading, showErrorModal } = useAuth();

  const qasEnv =
    import.meta.env.VITE_ENVIROMENT === "DEV" ||
    import.meta.env.VITE_ENVIROMENT === "QAS";

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const open = Boolean(anchorEl);

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  return (
    <AppBar
      position="static"
      color="primary"
      // style={{ height: "72px" }}
      data-testid="headerComponent"
    >
      <Toolbar>
        <Box display="flex" alignItems="center" flexGrow={1} height="72px">
          <img
            src="/images/logo-brf.png"
            alt="Logo"
            style={{
              marginRight: "16px",
              maxHeight: "100px",
            }}
          />
          {!isMobile && (
            <Typography
              variant="h6"
              sx={{
                fontFamily: "CoTextCorp",
                fontWeight: 600,
                fontSize: "18px",
                lineHeight: "2.5625rem", // 41px converted to rem
                display: "flex",
                alignItems: "flex-end",
                textTransform: "uppercase",
                color: "rgba(0, 0, 0, 0.87)",
                marginLeft: "1rem",
              }}
            >
              {qasEnv
                ? `${import.meta.env.VITE_ENVIROMENT} - ${t("Approval.Center")}`
                : t("Approval.Center")}
            </Typography>
          )}
        </Box>
        <Box flexGrow={1} />
        <Divider orientation="vertical" flexItem />

        {qasEnv && (
          <Box
            display="flex"
            alignItems="center"
            onMouseEnter={(e) => {
              e.currentTarget.style.cursor = "pointer";
            }}
            onClick={handleClick}
            aria-controls={open ? "search-users" : undefined}
            aria-haspopup="true"
            aria-expanded={open ? "true" : undefined}
            aria-label="Abrir select de usuários para representar"
          >
            <Typography
              variant="body1"
              style={{
                marginLeft: "2rem",
                marginRight: "2rem",
                textTransform: "uppercase",
                fontWeight: 600,
                fontSize: "14px",
              }}
            >
              {!isLoading && user && user.authenticated === true
                ? `${user?.employeeName?.split(" ")[0]} - ${user?.employeeID}`
                : user?.authenticated === false
                ? "User Not Found"
                : "-"}
            </Typography>
            {!showErrorModal && (
              <SearchUsers
                action="reports"
                anchorEl={anchorEl}
                setAnchorEl={setAnchorEl}
                open={open}
              />
            )}
          </Box>
        )}

        {!qasEnv && (
          <Box display="flex" alignItems="center">
            <>
              {user.employeeName} - {user.employeeID}
            </>
          </Box>
        )}
        <Divider orientation="vertical" flexItem />
        <LanguageMenu />
      </Toolbar>
    </AppBar>
  );
};
