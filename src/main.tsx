import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@azure/msal-react";
import { ThemeProvider } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { createRoot } from "react-dom/client";
import { I18nextProvider } from "react-i18next";
import { QueryClient, QueryClientProvider } from "react-query";
import { BrowserRouter } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import App from "./App";
import { pca } from "./config/msalConfig";
import { AuthProvider } from "./hooks/auth.tsx";
import i18n from "./hooks/translation.tsx";
import "./index.css";
import { AuthService } from "./services";
import theme from "./styles/theme.ts";
import { PrimeReactProvider } from 'primereact/api';

const queryClient = new QueryClient();

AuthService.initialize();

const Root = () => (
  <BrowserRouter>
    <MsalProvider instance={pca}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <PrimeReactProvider>
          <I18nextProvider i18n={i18n}>
            <ThemeProvider theme={theme}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <ToastContainer
                  position="top-right"
                  autoClose={5000}
                  hideProgressBar={false}
                  newestOnTop={false}
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme="light"
                />
                <App />
              </LocalizationProvider>
            </ThemeProvider>
            </I18nextProvider>
          </PrimeReactProvider>
        </AuthProvider>
      </QueryClientProvider>
    </MsalProvider>
  </BrowserRouter>
);

export default Root;

createRoot(document.getElementById("root")!).render(<Root />);
