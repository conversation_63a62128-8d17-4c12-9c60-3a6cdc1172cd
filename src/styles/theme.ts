import { createTheme, PaletteOptions } from "@mui/material/styles";

const palette: PaletteOptions = {
  mode: "light",
  primary: {
    main: "#FFFFFF",
    contrastText: "#000000",
  },
  secondary: {
    main: "#812990",
  },
  background: {
    default: "#000000",
  },
};

export default createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 748,
      md: 914,
      lg: 1200,
      xl: 1920,
    },
  },
  palette: { ...palette },
  shape: {
    borderRadius: 10,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: "10px",
        },
      },
      variants: [
        {
          props: { color: "secondary" },
          style: {
            borderRadius: "20px",
          },
        },
      ],
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiInputBase-root": {
            border: (palette.primary as { main: string }).main,
          },
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          borderRadius: "10px",
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          ".MuiAccordionSummary-content": {
            margin: "0 !important",
          },
          "& .MuiTypography-h6": {
            fontFamily: "CoTextCorp !important",
            fontWeight: "bold",
          },
          color: (palette.secondary as { main: string }).main,
        },
      },
    },
    MuiDialogActions: {
      styleOverrides: {
        root: {
          "& .MuiInputBase-root": {
            border: (palette.primary as { main: string }).main,
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          "&:last-child": {
            paddingBottom: "16px",
          },

          "> table > tbody > tr > td > div:first-of-type": {
            color: "rgba(0, 0, 0, 0.54);",
          },
          "> table > tbody > tr > td": {
            paddingBottom: "8px",
          },
        },
      },
    },
  },
});
