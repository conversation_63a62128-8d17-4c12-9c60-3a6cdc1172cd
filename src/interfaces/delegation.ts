export interface DelegationProps {
  id: string;
  delegator: string;
  delegatorName: string;
  delegatorOffice: string;
  delegate: string;
  delegateName: string;
  delegateOffice: string;
  startDate: string;
  endDate: string;
}

export interface DelegationResponse {
  type: string;
  message: string;
  returnValues: DelegationProps[];
}

export interface DelegationParams {
  delegatorInitials?: string; // e.g., CBR
  userInitials?: string; // e.g., 323951
  employeeid?: string; // e.g., 045139
  language?: string; // e.g., PT-BR
  startDate?: string;
  endDate?: string;
}
