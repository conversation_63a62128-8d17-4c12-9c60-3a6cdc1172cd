export interface IHRSuccessFactorBase {
  Codigo: string;
  Empregado: string;
  CommentsBy: string;
  Comments: string;
  EventReason: string;
  Date: string;
  TerminationDate: string;
  LeavingReason: string;
  Solicitante: string;
}

export interface IHRTransference extends IHRSuccessFactorBase {
  Posicao: string;
  Cargo: string;
  Categoria: string;
  Codigo2: string;
  IniciadoPorFirstName: string;
  EfetivoEm: string;
  CentroCusto: string;
  UnidadeOrganizacional: string;
  Pais: string;
  Supervisor: string;
  PlanoHorarioTrabalho: string;
}

export interface IHRDemissionRequest extends IHRSuccessFactorBase {
  TerminationDate: string;
  LeavingReason: string;
}

export interface IHRSalaryUpdate extends IHRSuc<PERSON>FactorBase {
  Cargo: string;
  Categoria: string;
  Codigo2: string;
  EventReason: string;
  IniciadoPorFirstName: string;
  EfetivoEm: string;
  Pais: string;
  MoedaCorrente: string;
  ComponentesPagamento: string;
  Valor: string;
}

export interface IHRPositionCreate extends IHRSuccessFactorBase {
  Cargo: string;
  Categoria: string;
  Codigo2: string;
  QualCargo: string;
  AreaRH: string;
  IniciadoPorFirstName: string;
  Status: string;
  DataInicial: string;
  NivelFuncao: string;
  ClasseColaborador: string;
  RegularTemporario: string;
  ASerContratado: string;
  Empresa: string;
  UnidadeOrganizacional: string;
  UnidadeNegocio: string;
  Divisao: string;
  CentroCusto: string;
  Recruting: string;
  SubArea: string;
  WorkSchedule: string;
  ApprovedSalary: string;
  RangeMin: string;
  RangeMed: string;
  RangeMax: string;
  PosicaoNivelSuperior: string;
  AllowanceList: string;
}

export interface IHRLearningManagement extends IHRSuccessFactorBase {
  documentNumber: string;
  documentTitle: string;
  requesterName: string;
  userId: string;
  documentType: string;
  startDate: string;
  endDate: string;
  description: string;
}
export interface IHRExceptionRequest extends IHRSuccessFactorBase {
  ALMOXARIFADO: string;
  CA: string;
  CODIGO: string;
  CODIGOGESTORSOC: string;
  DATACADASTRO: string;
  IDEPI: string;
  IDREQUISICAO: string;
  JUSTIFICATIVA: string;
  MATRICULA: string;
  NOMEAPROVADOR: string;
  NOMECENTROCUSTO: string;
  NOMEEPI: string;
  NOMEFUNCIONARIO: string;
  QUANTIDADE: string;
  REFERENCIA: string;
  SOLICITANTE: string;
  TIPOEXCECAO: string;
  TIPOREQUISICAO: string;
  UNIDADEMEDIDA: string;
}

export interface EPIApprovalParams {
  code?: string;
  requestType?: string;
  requestId?: string;
  epiId?: string;
  socManagerCode?: string;
  rejectionReason?: string;
  status?: boolean;
}

export type HRDocument = IHRTransference &
  IHRDemissionRequest &
  IHRSalaryUpdate &
  IHRPositionCreate &
  IHRLearningManagement &
  IHRExceptionRequest;
