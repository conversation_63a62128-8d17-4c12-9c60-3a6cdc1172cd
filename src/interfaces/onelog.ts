export interface AprovacoesProps {
  id: string;
  idDiaria: string;
  dataSolicitacao: string;
  motivo: string;
  pesoEntrega: string;
  tempoEmDiaria: string;
  numeroViagem?: string;
  placa?: string;
}

interface ReasonProps {
  id: string;
  descricao: string;
  areaResponsavel: string;
  subMotivos: { descricao: string }[];
}

export interface ItemDetailProps extends AprovacoesProps {
  numeroViagem: string;
  isClienteResponsavel: string;
  status: string;
  entregaViagem: string;
  placaVeiculo: string;
  nomeCliente: string;
  nomeClienteResponsavel: string;
  custoEstimado: string;
  categoria: string;
  justificativa: string;
  subMotivo: string;
  motivos: ReasonProps[];
  produto: string;
  dataPrevisaoEntrega: string;
  subMotivos: { descricao: string }[];
  clienteBairro: string;
  clienteCidade: string;
  clienteUF: string;
}

export interface ClientProps {
  nome: string;
  aprovacoes: AprovacoesProps[];
}

export interface ReturnProps {
  data: ClientProps;
  success: boolean;
  message: string;
}

export interface ApprovalReprovalResponse {
  message: string;
  success: string;
  tipo: string;
}

export interface ApprovalReprovalParams {
  id: string;
  status: "A" | "R";
  motivo?: string;
  submotivo?: string;
  reason?: string;
}

// Exportações diretas em vez de namespace para seguir as boas práticas de ES2015
export type OnelogItem = AprovacoesProps;
export type OnelogApprovalParams = ApprovalReprovalParams;
export type OnelogApprovalResponse = ApprovalReprovalResponse;
