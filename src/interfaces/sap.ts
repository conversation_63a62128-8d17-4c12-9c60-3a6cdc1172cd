import { ApprovalOrigin } from "@/api/approval/approval.service";

export interface AdicionalProps {
  campo: string;
  valor: string | number;
}

export interface ItemProps {
  sub_items?: any;
  adicionais: AdicionalProps[];
  centro: string;
  data_emissao: string;
  documento: string;
  fornecedor: string;
  item: string;
  material: string;
  qtd: string;
  requisitante: string;
  tipo: string;
  vldoc: string;
  vlitem: string;
  vlunit: string;
  ecp?: boolean;
  mes?: string;
  type: string;
  origin: ApprovalOrigin;
}

export interface SapReturnProps {
  retorno?: ItemProps[] | any;
}

export interface ReturnProps {
  retorno: ItemProps[] | any;
  rawResponse: any;
}

export interface ApprovalReprovalResponse {
  msg: [
    {
      tipo: string;
      codigo: string;
      mensagem: string;
    }
  ];
}

export interface ApprovalReprovalParams {
  document: string;
  item?: string;
  status: "A" | "R" | "B";
  additional1?: string;
  additional2?: string;
  reason?: string;
  language: string;
  process: string;
  ecp: boolean;
}

export interface DPOptions {
  created: string;
  id: string;
  name: string;
  updated: string;
}
