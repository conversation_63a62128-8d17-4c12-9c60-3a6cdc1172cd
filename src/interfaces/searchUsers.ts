import { IPermissionsApproval } from "./permission";

export interface SearchUsersProps {
  action: "subordinate" | "reports";
  anchorEl?: HTMLElement | null;
  setAnchorEl?: React.Dispatch<React.SetStateAction<HTMLElement | null>>;
  open?: boolean;
  className?: string;
}

export interface SearchUsersAccounts {
  email: string;
  employeeId: string;
  id: number;
  initials: string;
  name: string;
  opid: string;
  permissionsApproval: IPermissionsApproval[];
}

export interface SearchUsersResponse {
  accounts: SearchUsersAccounts[];
  page: number;
  qttyPages: number;
  qttyRegisters: number;
}
