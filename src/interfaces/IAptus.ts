import { AptusProcessType } from "@/services/aptus";

export interface ISubTotalPorDespesas {
  Relatorio: string;
  Valor: string;
  Despesa: string;
  DespesaDescricao: string;
  Data: string;
  Critica: string;
  Limite: string;
  Itens: IItens[];
}

export interface ISubTotalPorDias {
  Relatorio: string;
  Valor: string;
  Data: string;
  SubTotalPorDespesas: ISubTotalPorDespesas[];
}

export interface IItens {
  MesAnoFatura?: string;
  NumeroCartao?: string;
  Codigo?: string;
  SolicitanteID?: string;
  idSolicitante?: string;
  Solicitante?: string;
  FavorecidoCPF?: string;
  Favorecido?: string;
  Empresa?: string;
  EmpresaNome?: string;
  DataInicioPeriodoViagem?: string;
  DataFimPeriodoViagem?: string;
  CidadeOrigem?: string;
  CidadeDestino?: string;
  PossuiAdiantamento?: string;
  NumeroAdiantamento?: string;
  ValorAdiantamento?: string;
  Objetivo?: string;
  DataDespesa?: string;
  ContaContabil?: string;
  ContaContabilDescricao?: string;
  tipo_despesa?: string;
  Tipo_Despesa?: string;
  ValorTotal?: string;
  ValorLimite?: string;
  Comentarios?: string;
  VAT?: string;
  Valor_VAT?: string;
  OrdemInterna?: string;
  CentroCusto?: string;
  CentroCustoDescricao?: string;
  Critica?: string;
  CriticaMotivo?: string;
  Anomalia?: string;
  NotaFiscal?: string;
  Participantes?: string;
  QuantidadeParticipantes?: string;
  Titular?: string;
  TitularCPF?: string;
  Cargo?: string;
  Data?: string;
  CPMF?: string;
  Impostos?: string;
  mes_referencia_mensalidade?: string;
  NomeCurso?: string;
  Idioma_Descricao?: string;
  [key: string]: any;
}
// PD, AD
export interface IAptusBaseDocument {
  DataSolicitacao: string;
  Codigo: string;
  Favorecido: string;
  CentroCusto: string;
  CentroCustoDescricao: string;
  OrdemInterna: string;
  Valor: string;
  DataInicioPeriodoViagem: string;
  DataFimPeriodoViagem: string;
  Critica: string;
  PossuiProjeto: string;
  Cargo: string;
  AreaRH: string;
  CriticaMotivo?: string;
  SubTotalPorDespesas: ISubTotalPorDespesas[];
  SubTotalPorDias: ISubTotalPorDias[];
  cidadeOrigem?: string;
  cidadeDestino?: string;
  TIPO_INCENTIVO_DESCRICAO?: string;
  AREA_RH?: string;
  AREA_RH_DESCRICAO?: string;
  Itens: IItens[];
}

export interface AptusResponse {
  Dados: IAptusBaseDocument;
  MensagemRetorno: string;
  StatusRetorno: string | null;
  TempoProcessamento: number;
}

export interface ApprovalReprovalParams {
  codigo: string;
  isApprove: boolean;
  processType: AptusProcessType;
  comentary?: string;
}
