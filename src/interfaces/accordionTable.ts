import { CardProps } from "@mui/material";

export interface IAccordionTableProps<T> extends CardProps {
  process: string;
  type: string;
  title: string;
  headerData: T[];
  detailData?: T[];
  total: number;
  origin?: string;
  onApprove?: (
    row: T,
    status: string,
    isDetail?: boolean,
    reason?: string
  ) => Promise<void>;
  onReprove?: (
    row: T,
    status: string,
    isDetail?: boolean,
    reason?: string
  ) => Promise<void>;
}
