export interface Sv3ExpenseItemProps {
  DEST_DESCRICAO: string;
  DESI_OUTDESCRI: string;
  DEST_OUT: number;
  VATVALOR: number;
  DESI_IMPORTANCIA: number;
  DESI_CCUSTO: string;
  VATNOME: string;
  ANEXO: string;
}

export interface Sv2ExpenseItemProps {
  data: {
    PTI_CODIGO: string;
    VIA_CODIGO: string;
    TMOV_CODIGO: string;
    PTI_DATAMOV: string;
    PTI_CCUSTO: string;
    PTI_VALOR: number;
    PTI_OBS: string;
    PTI_KMINI: string;
    PTI_KMFIM: string;
    PTI_VALKM: string;
    PTI_TOTALKM: string;
    PTI_CFIXO: string;
    PTI_CCONTABI: string;
    VATIDPK: number;
    VATVALOR: number;
    PTI_ORDEM: string;
    VATNOME: string;
    ANEXO: string;
    REFEICAO: string;
    TAXI: string;
    KM: string;
    PASSAGEM: string;
    OUTROS: string;
  }[];
}

export interface SvProps
  extends Pick<
    ItemProps,
    "CODIGO" | "FAVORECIDO" | "VALOR" | "CC" | "DATASOLICITACAO"
  > {
  ORDEM: string;
  DATA_INICIO: string;
  DATA_FIM: string;
  SIGLA_MOEDA: string;
}

export interface CpItemProps
  extends Pick<
    ItemProps,
    | "VALOR"
    | "FAVORECIDO"
    | "DATASOLICITACAO"
    | "CODIGO"
    | "EMAILAPROVADOR"
    | "NOME"
    | "ID"
  > {
  RESERVA: string;
  OBJETIVO: string;
  DATA_FATURA: string;
  MOEDA: string;
  VALORTOTAL: string;
}

export interface CpExpenseProps extends Pick<ItemProps, "VALOR"> {
  DATA: string;
  CONTA_CONTABIL: string;
  TIPO_DESPESA: string;
  CENTRO_CUSTO: string;
  DESCRICAO: string;
  ORDEM: string;
  MOEDA: string;
  ANEXO: string;
}

export interface NormEvolvedProps {
  data: {
    USR_SIGLA: string;
    USR_ID: string;
    USR_NOME: string;
    NEN_DATA_APROV: string;
    NEN_CARGO: string;
    USR_CARGO: string;
    NEN_AREA: string;
  }[];
}

export interface NormProps extends Pick<ItemProps, "CODIGO"> {
  NUMERO: string;
  REVISAO: string;
  TITULO: string;
  REDATOR: string;
  TIPO: string;
  EDICAO: string;
  POSSUI_ANEXO: boolean;
}

export interface TrDetailItem {
  TRII_CCUSTO: string;
  TRII_DIVISAO: string;
  TRII_VALOR: string;
  TRII_OBS: string;
  TRIT_DESCRICAO: string;
}

interface Outorgado {
  OUTORGADO: string;
}

export interface ItemProps {
  CODIGO: string;
  FAVORECIDO: string;
  DATAINICIO: string;
  VALOR: string;
  CC: string;
  STATUS: string;
  ID: string;
  NOME: string;
  EMAILAPROVADOR: string;
  DATASOLICITACAO: string;
  LIMITE: string | number;
  OBSERVACAO: string;
  JUSTIFICATIVA: string;
  OUTORGADO: Outorgado[] | string;
  DATAVENCIMENTO: string;
  UNIDADE: string;
}

export interface IntranetProps
  extends ItemProps,
    SvProps,
    CpItemProps,
    CpExpenseProps,
    NormProps,
    TrDetailItem {}

export interface ApprovalReprovalParams {
  docId: string;
  status: "A" | "R";
  comments?: string;
}

export type ProcessKey =
  | "CS"
  | "CP"
  | "C1"
  | "C2"
  | "PM"
  | "TR_INTRANET"
  | "SV_1"
  | "SV_2"
  | "SV_3"
  | "NO"
  | "NR";
