// RateFactor
export interface RateFactorLineDetail {
  id?: number;
  qtdShipments?: number;
  cost: string;
  totalAdjustment: string;
  adjustment: string;
  percent: string;
  rateFactorLinesId: string;
  referenceDate: string;
  estimatedAnnualValue: string;
}

export interface RateFactorLines {
  serviceTypeName: string;
  value: string;
  rateFactorLineDetailsCollection?: RateFactorLineDetail[];
}

export interface RateFactor {
  identifier: string;
  id: string;
  effectiveDate: string;
  rateFactorLines: RateFactorLines[];
}

// RateRecord
export interface RateRecordCostItem {
  accessorialCode?: string;
  accessorialCost?: string;
  id: string;
  lowValue2: string;
  lowValue3: string;
  lowValue4: string;
  chargeAmount: string;
  chargeMultiplierScalar: string;
  effectiveDate: string;
  expirationDate: string;
  baseCost: string;
  operation: string;
  calculatedValue: string;
  accessorialCodeXid?: string;
  accessorialCostXid?: string;
}

export interface RateRecordGroupItem {
  id: string;
  rateGeoCostGroupGid: string;
  rateGeoCostGroupXid: string;
  rateGeoCostGroupSeq: number;
  rateRecordCostsCollection: {
    items: RateRecordCostItem[];
  };
}

export interface RateRecord {
  id: string;
  rateGeoXid: string;
  effectiveDate: string;
  expirationDate: string;
  rateType: string;
  dCountry: string;
  sCountry: string;
  dProvince: string;
  sProvince: string;
  dCity: string;
  sCity: string;
  serviceProviderXid: string;
  serviceProviderName: string;
  rateOfferingXid: string;
  operation: string;
  rateRecordAccessorialsCollection: {
    items: RateRecordCostItem[];
  };
  rateRecordAccNewCollection: {
    items: RateRecordCostItem[];
  };
  rateRecordGroupsCollection: {
    items: RateRecordGroupItem[];
  };
}

// AccessorialCost

export interface AccessorialCost {
  id: string;
  accessorialCostXid: string;
  lowValue2: string;
  lowValue3: string;
  lowValue4: string;
  chargeAmount: string;
  effectiveDate: string;
  expirationDate: string;
  accessorialCodeXid: string | null;
}
// FreightSurcharge
export interface FreightSurcharge {
  id: string;
  createdBy: string;
  servprovXid: string;
  servprovName: string;
  sourceCity: string;
  sourceProvinceCode: string;
  destCity: string;
  destProvinceCode: string;
  shipmentXid: string;
  chargeAmount: string;
  marketType: string;
  serviceType: string;
  totalActualCost: string;
  eventDate: string;
  reason: string;
}

// CostBatch
export interface CostBatchDetail {
  id: string;
  sourceProvinceCode: string;
  sourceCountryCode3Gid: string;
  destProvinceCode: string;
  destCountryCode3Gid: string;
  servprovXid: string;
  servprovName: string;
  rptAdjustment: string;
  prjFRateGeoGid: string;
  sourceCity: string;
  destCity: string;
  lowValue4: string;
  lowValue2: string;
  lowValue3: string;
  chargeAmount: string;
  prjFChargeMultiplierScalar: string;
  appEffectiveDate: string;
  appCalculatedCost: string;
  appChargeMultiplierScalar: string;
  appExpirationDate: string;
}

interface CostBatchGroupItem {
  id: string;
  servprovXid: string;
  servprovName: string;
  rptAdjustment: string;
  rRCostBatchLinesCollection?: {
    items?: CostBatchDetail[];
  };
}

export interface CostBatch {
  id: string;
  requester: string;
  attendant: string;
  rptAdjustment: string;
  rptCost: string;
  rptEstimatedAnnualValue: string;
  rptQtdShipments: string;
  rptTotalAdjustment: string;
  rRCostBatchGroup: {
    items: CostBatchGroupItem[];
  };
}

// CostBatchRep
export interface CostBatchRepDetail {
  id: string;
  sourceProvinceCode: string;
  sourceCountryCode3Gid: string;
  destProvinceCode: string;
  destCountryCode3Gid: string;
  servprovXid: string;
  servprovName: string;
  rptAdjustment: string;
  prjFRateGeoGid: string;
  sourceCity: string;
  destCity: string;
  lowValue4: string;
  lowValue2: string;
  lowValue3: string;
  chargeAmount: string;
  prjFChargeMultiplierScalar: string;
  appEffectiveDate: string;
  appCalculatedCost: string;
  appChargeMultiplierScalar: string;
  appExpirationDate: string;
}

interface CostBatchRepGroupItem {
  id: string;
  servprovXid: string;
  servprovName: string;
  rptAdjustment: string;
  rRCostBatchRepLinesCollection?: {
    items?: CostBatchRepDetail[];
  };
}

export interface CostBatchRep {
  id: string;
  requester: string;
  attendant: string;
  rptAdjustment: string;
  rptCost: string;
  rptEstimatedAnnualValue: string;
  rptQtdShipments: string;
  rptTotalAdjustment: string;
  rRCostBatchRepGroup: {
    items: CostBatchRepGroupItem[];
  };
}

type OracleResponseMap = {
  [key in OracleTypes]?: Array<
    key extends "rateFactor"
      ? RateFactor
      : key extends "rateRecord"
      ? RateRecord
      : key extends "AccessorialCost"
      ? AccessorialCost
      : key extends "FreightSurcharge"
      ? FreightSurcharge
      : key extends "RRCostBatch"
      ? CostBatch
      : key extends "RRCostBatchRep"
      ? CostBatchRep
      : never
  >;
};

export interface OracleResponse extends OracleResponseMap {
  email: string;
}

export interface OracleResponseToCard {
  data?: OracleItemType[];
}

export interface OracleItemType
  extends RateRecord,
    RateFactor,
    AccessorialCost,
    FreightSurcharge,
    CostBatch,
    CostBatchRep {
  origin: string;
  type: OracleTypesProcess;
  email: string;
}

export type OracleTypes =
  | "rateFactor"
  | "rateRecord"
  | "AccessorialCost"
  | "FreightSurcharge"
  | "RRCostBatch"
  | "RRCostBatchRep";

export type OracleTypesProcess =
  | "TMS_1"
  | "TMS_2"
  | "TMS_3"
  | "TMS_4"
  | "TMS_5"
  | "TMS_6";

export interface ApprovalReprovalParams {
  id: string;
  email?: string;
  statusCode: "APPROVED" | "REJECTED";
  obs?: string;
  type: OracleTypes;
}
