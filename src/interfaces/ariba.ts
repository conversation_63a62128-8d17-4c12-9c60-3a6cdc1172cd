import { ApprovalOrigin } from "@/services/approvalService/types";

// Interfaces para usuários e grupos
interface AribaUser {
  passwordAdapter?: string;
  uniqueName: string;
  name: string;
  emailAddress?: string;
  phone?: string;
  realm?: string;
}

interface AribaGroup {
  uniqueName: string;
  id: string;
  name: string;
}

interface AribaApprover {
  user?: AribaUser;
  group?: AribaGroup;
  type: string;
}

// Interface de solicitação de aprovação
export interface ApprovalRequest {
  id: string;
  taskId: string;
  state: number;
  approvalState: string;
  reason: string;
  approvalRequired: boolean;
  dependencyApprovalRequestsUID: string[];
  approvers: AribaApprover[];
  activationDate?: number;
  approvedDate?: number;
  approvedBy?: AribaApprover;
}

// Propriedades de Material
export interface MaterialProps {
  currency: string;
  material: string;
  outline: string;
  quantity: string;
  totalItem: string;
  unitOfMeasure: string;
  unitValue: string;
}

// Interface base para todos os itens Ariba
interface AribaItemBase {
  id: string;
  task: string;
  fullURL: string;
  description: string;
  aribaUser: string;
  type: string;
  origin: ApprovalOrigin;
}

// Interface base para itens relacionados a projetos
interface ProjectRelatedItem extends AribaItemBase {
  projectName: string;
  ownerName: string;
}

// Especializações por tipo de item
export interface ItemPropsAR extends AribaItemBase {
  recordDate: string;
  requester: string;
  recordType: string;
  supplierName: string;
  item: string;
  totalCostAmount: string;
  totalCostCurrency: string;
  currencyItem: string;
  items: MaterialProps[];
}

export interface ItemPropsAP extends ProjectRelatedItem {
  subcategoryNegotiation: string;
  closedNegotiation: string;
  contractAmount: string;
  approvalRequests: ApprovalRequest[];
}

export interface ItemPropsAS extends ProjectRelatedItem {
  workspaceDetails: {
    name: string;
    description: string;
    projectState: string;
    contractState: string | null;
    baselineSpend: string;
    status: string | null;
    customFields: Array<{
      fieldName: string;
      fieldValue: string;
      fieldType: string;
    }>;
  };
  approvalRequests: ApprovalRequest[];

  supplierName: string;
  ownerId: string;
  demandType: string;
  supplierType: string;
  contractManager: string;
  description: string;
}

export interface ItemPropsAT extends ProjectRelatedItem {
  contractor: string;
  supplierName: string;
  title: string;
  contractAmount: string;
  contractCurrency: string;
  effectiveDate: string;
  beginDate: string;
  cusCmDetalhesAditivo: string;
  entityId: string;
  entityType: string;
  attachmentId: string;
  approvalRequests: ApprovalRequest[];
  cusComentariosClusulaCrtica: string;
  cusQuaisClausulasCriticasforamAlteradas: string[];
}

// Type union para os diferentes tipos de itens
export type AribaItemType =
  | ItemPropsAP
  | ItemPropsAR
  | ItemPropsAS
  | ItemPropsAT;

// Interface de tarefa
export interface Tasks {
  task: string;
  description: string;
  aribaUser: string;
  detail: AribaItemType;
}

// Tipos de pendências
export type KeysAribaPending =
  | "contract"
  | "project"
  | "requisition"
  | "sourcing";

// Interface para grupo de tarefas
interface TaskGroup {
  total: number;
  tasks: Tasks[];
}

// Interface principal de aprovações pendentes
export interface PendingApprovalsProps {
  total?: number;
  contract?: TaskGroup;
  project?: TaskGroup;
  requisition?: TaskGroup;
  sourcing?: TaskGroup;
}

// Interface para parâmetros de aprovação/reprovação
export interface ApprovalReprovalParams {
  itemCode: string;
  action: "A" | "D";
  comment: string;
  aribaUser: string;
  typeProcess: string;
}
