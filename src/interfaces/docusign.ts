export interface SignersProps {
  NAME: string;
  STATUS: string;
}

export interface IAdicionalProps {
  CAMPO: string;
  VALOR: string;
}
export interface ItemProps {
  ADICIONAIS: IAdicionalProps[];
  SIGNERS?: SignersProps[];
  CENTRO: string | null;
  DATA_EMISSAO: string | null;
  DOCUMENTO: string | null;
  FORNECEDOR: string | null;
  ITEM: string | null;
  MATERIAL: string | null;
  PROCESSO: string | null;
  QTD: string | null;
  REQUISITANTE: string | null;
  TIPO: string | null;
  VLDOC: string | null;
  VLITEM: string | null;
  VLUNIT: string | null;
}
