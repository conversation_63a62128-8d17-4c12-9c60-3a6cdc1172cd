import { IPermissionsApproval, IProcessApproval } from "./permission";

export interface IUser {
  accessToken: string;
  accountId: number;
  accountIdinitial: string;
  initials: string;
  authenticated: boolean;
  created: string;
  departmentId: string | number;
  email: string;
  opid: string;
  exibitName?: string;
  employeeID: string;
  employeeRepresentedID: string;
  employeeName: string;
  expiration: string;
  language: string;
  languageOption: string;
  permissionsApproval: IPermissionsApproval[];
  permissionsReport?: IPermissionsApproval[];
  permissionsAllowed?: IPermissionsApproval[];
  processApproval?: IProcessApproval[];
  hasFilter: boolean | false;
  accountIdInitial?: string;
  accessTokenInitial?: string;
  permissionsApprovalInitial?: IPermissionsApproval[];
  onlyReading?: boolean;
  userReading?: string;
  showSilentNotification: boolean;
  tutorialDone: boolean;
  originalAccountId: number | string;
  reportAccountId?: string;
}
