export interface SalesProps {
  rob: number;
  recordType: string;
  preCapTotalAmount: number;
  preCapNumber: string;
  paymentType: string;
  chain: string;
  alert: string;
  poValue: string;
  totalInvestimentoPreCap: string;
  accumulatedInvested: string;
  region: string;
  segmentacao: string;
  gerenteVendas: string;
  status: string;
  InvestimentPercentage: string;
  ProductMarginPercentage: string;
  InvestmentAnalisys: string;
  duplicated: string;
}

export interface TradeSpendProps {
  approvalProcessName: string;
  createdDate: string;
  id: string;
  name: string;
  recordDetail: {
    amount: number;
    currencyCode: string;
    customer: string;
    details: string;
    id: string;
    name: string;
  };
  amount: number;
  currencyCode: string;
  customer: string;
  details: string;
  recordLink: string;
  requestor: string;
  status: string;
  targetObjectId: string;
  targetObjectType: string;
}

export interface ApprovalReprovalParamsSales {
  operation?: "A" | "R";
  comment?: string;
  preCapNumber?: string;
}

export interface ApprovalReprovalParamsGcc
  extends Pick<ApprovalReprovalParamsSales, "comment"> {
  status?: "A" | "R";
}

export interface IGCCParams {
  params: {
    comment: string;
    status: string;
  };
  documentCode: string;
}

export interface IPreCapParams {
  preCapNumber: string;
  comment: string;
  operation: string;
}

export interface ApprovalReprovalParams
  extends ApprovalReprovalParamsGcc,
    ApprovalReprovalParamsSales {}

export interface ICommercialProps extends TradeSpendProps, SalesProps {
  type: string;
  origin: string;
}
