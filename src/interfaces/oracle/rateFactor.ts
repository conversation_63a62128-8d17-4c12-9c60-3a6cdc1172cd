export interface RateFactorLineDetail {
  id?: number;
  qtdShipments?: number;
  cost: string;
  totalAdjustment: string;
  adjustment: string;
  percent: string;
  rateFactorLinesId: string;
  referenceDate: string;
  estimatedAnnualValue: string;
}

export interface RateFactorLines {
  serviceTypeName: string;
  value: string;
  rateFactorLineDetailsCollection?: RateFactorLineDetail[];
}

export interface RateFactor {
  identifier: string;
  id: string;
  effectiveDate: string;
  rateFactorLines: RateFactorLines[];
}
