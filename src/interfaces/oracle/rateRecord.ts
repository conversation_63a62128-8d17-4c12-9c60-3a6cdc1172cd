export interface RateRecordCostItem {
  accessorialCode?: string;
  accessorialCost?: string;
  id: string;
  lowValue2: string;
  lowValue3: string;
  lowValue4: string;
  chargeAmount: string;
  chargeMultiplierScalar: string;
  effectiveDate: string;
  expirationDate: string;
  baseCost: string;
  operation: string;
  calculatedValue: string;
  accessorialCodeXid?: string;
  accessorialCostXid?: string;
}

export interface RateRecordGroupItem {
  id: string;
  rateGeoCostGroupGid: string;
  rateGeoCostGroupXid: string;
  rateGeoCostGroupSeq: number;
  rateRecordCostsCollection: {
    items: RateRecordCostItem[];
  };
}

export interface RateRecord {
  id: string;
  rateGeoXid: string;
  effectiveDate: string;
  expirationDate: string;
  rateType: string;
  dCountry: string;
  sCountry: string;
  dProvince: string;
  sProvince: string;
  dCity: string;
  sCity: string;
  serviceProviderXid: string;
  serviceProviderName: string;
  rateOfferingXid: string;
  operation: string;
  rateRecordAccessorialsCollection: {
    items: RateRecordCostItem[];
  };
  rateRecordAccNewCollection: {
    items: RateRecordCostItem[];
  };
  rateRecordGroupsCollection: {
    items: RateRecordGroupItem[];
  };
}
