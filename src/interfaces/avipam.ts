export interface HotelProps {
  totalFare: string;
  currency: string;
  checkinDate: string;
  checkoutDate: string;
  amountDialy: string;
  city: string;
  state: string;
  name: string;
}

export interface CarsProps {
  totalFare: string;
  localCheckout: string;
  currency: string;
  checkinDate: string;
  checkoutDate: string;
  dailyQuantity: string;
}

export interface AirsProps {
  totalFare: string;
  departureDate: string;
  cityDeparture: string;
  arrivalDate: string;
  cityArrival: string;
  currency: string;
}

export interface TransportProps {
  totalFare: string;
  departure: string;
  arrival: string;
  currency: string;
  dateDeparture: string;
  dateArrival: string;
}

export interface JustificationTravelProps {
  typeTravell: string;
  descriptionTravel: string;
}

export interface JustificationExcerptProps {
  type: string;
  description: string;
}

interface Summary {
  valorTotal: number;
}

interface Approvers {
  id: string;
  name: string;
  registration: string;
  email: string;
  login: string;
  approvalDate: string;
}

interface InfGerencial {
  contentManagement: string;
  fieldManagement: string;
}
export interface AvipamItem {
  solicitationId: string;
  orderNumber: string;
  dateOrder: string;
  traveler: string;
  orderNumberRoot: string;
  statusTravel: string;
  centerCostCode: string;
  hotels: HotelProps[];
  cars: CarsProps[];
  airs: AirsProps[];
  transport: TransportProps[];
  approver: string;
  approvers: Approvers[];
  currency: string;
  totalFare: string;
  total: string;
  reason: string;
  travelJustification: JustificationTravelProps[] | null;
  justificationExcerpt: JustificationExcerptProps[] | null;
  summary: Summary[];
  infGerencial: InfGerencial[];
  observation: string;
}

export interface ApprovalReprovalParams {
  status: "A" | "R";
  orderNumber: string;
  reason?: string;
}
