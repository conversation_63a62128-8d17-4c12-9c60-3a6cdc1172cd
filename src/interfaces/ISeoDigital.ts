export interface ISeoDigitalBase {
  id: string;
  topicId: string;
  industryId: string;
  industryName: string;
  processId: string;
  processName: string;
  elementId: string;
  responsibleId: string;
  employeeId: string;
  responsibleName: string;
  supervision: string;
  subElement: string;
  pillarId: string;
  pillarName: string;
  topicName: string;
  elementName: string;
  level: string;
  what: string;
  how: string;
  investment: string;
  status: string;
  statusApproval: string;
  dateExpected: string;
  dateExecuted: string;
  dateCanceled: string;
  created: string;
  updated: string;
  canView: string;
  canEdit: string;
  hasManagerApproval: string;
  hasSpecialistApproval: string;
  approvalManagerId: string;
  approvalSpecialistId: string;
  documentId: string;
  approvalManagerName: string;
  approvalEmployeeId: string;
  approvalSpecialistName: string;
  approvalSpecialistEmployeeId: string;
  topicSpecialist: string;
  attachment: {}[];
  actionFields: {}[];
}

export interface ApprovalReprovalParams {
  processCode: string;
  approvalManagerId: string;
  typeOfApproval: number; //type
  status: "A" | "R";
  reason?: string;
}
