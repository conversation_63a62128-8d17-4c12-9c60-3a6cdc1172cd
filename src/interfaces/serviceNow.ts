export interface ItemProps {
  Codigo: string;
  Descricao: string;
  Favorecido: string;
  Cargo: string;
  Detalhes: string;
  Solicitante: string;
  Link: string;
  LinkAprovacao: string;
  Registro: string;
  SalesforceArea: string;
  SalesforcePerfil: string;
}

export interface ApprovalReprovalParams {
  registerId: string;
  status: "A" | "R";
  comment?: string;
}

export interface ApprovalReprovalResponse {
  MSG: [
    {
      TIPO: string;
      CODIGO: string;
      MENSAGEM: string;
    }
  ];
}
