export interface DadosValores {
  descricao: string;
  valorDe: string;
  valorPara: string;
  databaseDe: string;
  databasePara: string;
  indiceCorrecaoDe: string;
  indiceCorrecaoPara: string;
}

export interface DadosProps extends DadosValores {
  chave: string;
  valor: string;
}

export interface IProcurementItemJR {
  Data: string;
  dadosProcesso: DadosProps[];
  dadosValores: DadosProps[] | DadosValores[];
  advogadoInterno: string;
  areaDejur: string;
  dataPagamento: string;
  dataSolicitacao: string;
  formaPagamento: string;
  numeroSolicitacao: number;
  observacao: string;
  pastaCTG: string;
  tipoPagamento: string;
  tipoSolicitacao: string;
  valorTotal: string;
  linkedId: string;
  requestType: string;
  competencia: string;
  dataExecucao: string;
  movimento: string;
  saldo: string;
  usuarioExecucao: string;
  id: number;
  requestDate: string;
  folderNumber: string;
  legalDepartamentArea: string;
  descriptionOrderProvision: string;
  solicitante: string;
  localidade: string;
}

export interface IProcurementItemEletMarket {
  CapitalSocial: string;
  Clifor: string;
  Comentario: string;
  Descricao: string;
  Divida: string;
  Documento: string;
  Fornecedor: string;
  Negociador: string;
  Solicitante: string;
}

export interface IProcurementApprovalReprovalParams {
  operation: 0 | 1;
  comment?: string;
  userInitials?: string;
  documentNumber?: string;
  requestType?: string;
}

export interface ItemPropsDetails {
  id: number;
  folderNumber: string;
  processNumber: string;
  provisionClass: string;
  sphere: string;
  internalLawyer: string;
  areaDejur: string;
  observation: string;
}

export interface IProcurement
  extends IProcurementItemJR,
    IProcurementItemEletMarket {}

export interface IProcurementReprovalParams {
  documentId: string;
  justification: string;
}

export interface IProcurementApprovalResponse {
  success: boolean;
  message: string;
}
