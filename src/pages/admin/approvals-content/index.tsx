import { useState } from "react"
import { useQuery } from "react-query"
import dayjs, { Dayjs } from "dayjs"
import { useTranslation } from "react-i18next"

import { Box, Button, Card, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField } from "@mui/material"
import { DatePicker as DatePickerMUI } from '@mui/x-date-pickers'
import { FilterAlt, FilterAltOff } from "@mui/icons-material"

import { AuditReportService } from "@/services/auditReports"

interface ApprovalsContentProps {
  reportAccountId: string | undefined
  accountId: number
  employeeId: string
  employeeName: string
}

export const ApprovalsContent = ({ reportAccountId, accountId, employeeId, employeeName }: ApprovalsContentProps) => {
  const { t } = useTranslation()

  const [process, setProcess] = useState<string>("")
  const [document, setDocument] = useState<string>("")
  const [initialDate, setInitialDate] = useState<dayjs.Dayjs>(dayjs(new Date()))
  const [finalDate, setFinalDate] = useState<dayjs.Dayjs>(dayjs(new Date()).add(1, 'week'))
  const [applyFilters, setApplyFilters] = useState(false)

  const handleApplyFilters = () => {
    setApplyFilters((prevState) => !prevState)
  }

  const handleClearFilters = () => {
    setProcess("")
    setDocument("")
    setInitialDate(dayjs(new Date()))
    setFinalDate(dayjs(new Date()))
    setApplyFilters(false)
  }

  const { listAuditReport } = AuditReportService

  const { data: auditReportData } = useQuery({
    queryKey: ['audit-report', initialDate, finalDate, process, reportAccountId, accountId],
    queryFn: async () => listAuditReport({
      initialDate: initialDate.format('YYYY-MM-DD'),
      finalDate: finalDate.format('YYYY-MM-DD'),
      process,
      reportAccountId,
      accountId
    }),
    enabled: !!applyFilters
  })

  const itHasAuditReportData = auditReportData && auditReportData.length >= 1

  return (
    <>
      <Card sx={{
        display: 'flex',
        gap: '2rem',
        alignItems: 'center',
        p: '1.5rem 2rem'
      }}>
        <TextField
          value={process}
          onChange={({ target }) => setProcess(target.value)}
          slotProps={{
            inputLabel: {
              sx: {
                '&.Mui-focused': {
                  color: 'black'
                }
              }
            }
          }}
          label={t('Process')}
          variant="standard"
        />
        <TextField
          value={document}
          onChange={({ target }) => setDocument(target.value)}
          slotProps={{
            inputLabel: {
              sx: {
                '&.Mui-focused': {
                  color: 'black'
                }
              }
            }
          }}
          label={t('Document')}
          variant="standard"
        />

        <DatePickerMUI
          label={t('InitialDate').toUpperCase()}
          value={initialDate}
          maxDate={finalDate}
          onChange={(date) => setInitialDate(date as Dayjs)}
          format="DD/MM/YYYY"
          slotProps={{
            popper: {
              placement: 'bottom-end',
              modifiers: [
                {
                  name: "preventOverflow",
                  options: {
                    altAxis: true,
                    tether: true,
                    rootBoundary: "viewport",
                    padding: 8,
                  },
                },
              ],
            },
          }}
        />

        <DatePickerMUI
          label={t('FinalDate').toUpperCase()}
          value={finalDate}
          minDate={initialDate}
          onChange={(date) => setFinalDate(date as Dayjs)}
          format="DD/MM/YYYY"
          slotProps={{
            popper: {
              placement: 'bottom-start',
              modifiers: [
                {
                  name: "preventOverflow",
                  options: {
                    altAxis: true,
                    tether: true,
                    rootBoundary: "viewport",
                    padding: 8,
                  },
                },
              ],
            },
          }}
        />

        <Box
          sx={{
            display: 'flex',
            gap: '0.5rem'
          }}
        >
          <Button
            startIcon={<FilterAlt />}
            color="success"
            variant="contained"
            onClick={handleApplyFilters}
          >
            {t('ApplyFilters')}
          </Button>
          <Button
            startIcon={<FilterAltOff />}
            color="secondary"
            variant="contained"
            onClick={handleClearFilters}
          >
            {t('ClearFilters')}
          </Button>
        </Box>
      </Card>

      {itHasAuditReportData && (
        <TableContainer
          component={Paper}
          elevation={2}
          sx={{
            marginTop: '2rem'
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="approvals table"
          >
            <TableHead>
              <TableRow>
                <TableCell>{t('EmployeeID')}</TableCell>
                <TableCell>{t('Name')}</TableCell>
                <TableCell>{t('Process')}</TableCell>
                <TableCell>{t('Document')}</TableCell>
                <TableCell>{t('Action')}</TableCell>
                <TableCell>{t('Origin')}</TableCell>
                <TableCell>{t('IP')}</TableCell>
                <TableCell>{t('CreatedDate')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {auditReportData.map(auditReport => (
                <TableRow>
                  <TableCell>{employeeId}</TableCell>
                  <TableCell>{employeeName}</TableCell>
                  <TableCell>{auditReport.process}</TableCell>
                  <TableCell>{auditReport.document}</TableCell>
                  <TableCell>{auditReport.action}</TableCell>
                  <TableCell>{auditReport.origin}</TableCell>
                  <TableCell>{auditReport.ip}</TableCell>
                  <TableCell>{auditReport.created}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </>
  )
}