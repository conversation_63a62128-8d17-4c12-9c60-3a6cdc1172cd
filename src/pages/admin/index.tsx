import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Tabs,
  Tab
} from "@mui/material";

import { useCards } from "@/stores/cardsStore";

import { Select } from "@/components/ModalNewDelegation/components/Select";

import { useAuth, useSearchUsers, useAdmin } from "@/hooks";

import { ApprovalsPendenciesContent } from "./approval-pendencies-content";
import { PermissionsContent } from "./permissions-content";
import { ErrorLogsContent } from "./error-logs-content";
import { ApprovalsContent } from "./approvals-content";
import { useNavigate } from "react-router-dom";

const usersSpecialPermissions = import.meta.env.VITE_PERMISSION_REPORT as string

export const AdminPage: React.FC = () => {
  const { t } = useTranslation();

  const { getTokenForThisUser, login, user } = useAuth();
  const { setCards, resetAribaApiCalled } = useCards(user);
  const navigate = useNavigate();

  const {
    options,
    isLoadingOptions,
    handleInputChange,
    loadMore,
    setLoadMore,
    resetValues,
    loadMoreResults,
  } = useSearchUsers(
    getTokenForThisUser,
    login,
    setCards,
    "reports",
    resetAribaApiCalled
  );

  const {
    currentTab,
    selectedValue,
    handleTabChange,
    handleSelectedValue,
  } = useAdmin()

  const currentUserHasAdminAccess = usersSpecialPermissions.includes(user.employeeID)


  const renderContent = () => {
    switch (currentTab) {
      case "1":
        return (
          <ApprovalsPendenciesContent />
        );
      case "2":
        return (
          <PermissionsContent
            permissionsData={selectedValue[0]?.permissionsApproval ?? user.permissionsApproval}
          />
        );
      case "3":
        return (
          <ErrorLogsContent
            accountId={selectedValue[0]?.id ?? user.accountId}
            reportAccountId={user?.reportAccountId}
            employeeId={selectedValue[0]?.employeeId ?? user.employeeID}
            employeeName={selectedValue[0]?.name ?? user.employeeName}
          />
        );
      case "4":
        return (
          <ApprovalsContent
            accountId={user.accountId}
            reportAccountId={user.reportAccountId}
            employeeId={selectedValue[0]?.employeeId ?? user.employeeID}
            employeeName={selectedValue[0]?.name ?? user.employeeName}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (!currentUserHasAdminAccess) {
      navigate('/')
    }
  }, [currentUserHasAdminAccess, navigate])

  return (
    <Box sx={{ marginTop: "2rem" }}>
      <Box sx={{ width: '100%' }}>
        <Tabs
          value={currentTab}
          onChange={(_, newValue) => handleTabChange(newValue)}
          textColor="secondary"
          indicatorColor="secondary"
          aria-label="admin-tabs"
        >
          <Tab
            value="1"
            sx={{ fontWeight: 'bold' }}
            label={t('Pendencies').toUpperCase()}
          />
          <Tab
            value="2"
            sx={{ fontWeight: 'bold' }}
            label={t('Permissions').toUpperCase()}
          />
          <Tab
            value="3"
            sx={{ fontWeight: 'bold' }}
            label={t('ErrorLogs').toUpperCase()}
          />
          <Tab
            value="4"
            sx={{ fontWeight: 'bold' }}
            label={t('Approvals').toUpperCase()}
          />
        </Tabs>
      </Box>

      <Box
        sx={{
          py: '1rem',
          width: '300px'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.cursor = "pointer";
        }}
      >
        <Select
          value={selectedValue ?? ""}
          options={options}
          onChange={(value) => handleSelectedValue(value)}
          onInputChange={handleInputChange}
          isLoadingOptions={isLoadingOptions}
          loadMore={loadMore}
          setLoadMore={setLoadMore}
          loadMoreResults={loadMoreResults}
          resetValues={resetValues}
        />
      </Box>

      <Box sx={{ py: 2, mt: 2 }}>
        {renderContent()}
      </Box>
    </Box>
  );
};

