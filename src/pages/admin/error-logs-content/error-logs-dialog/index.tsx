import { useTranslation } from "react-i18next"
import {
  Dialog,
  DialogActions,
  DialogTitle,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  Typography,
  Box
} from "@mui/material"

export interface ErrorLogsDialogData {
  trace: string;
  createDate: string;
  queryString: string;
  innerMessage: string | null;
  process: string
  errorCode: string
}

export interface ErrorLogsDialogProps {
  open: boolean;
  onClose: () => void;
  errorLogsDialogData: ErrorLogsDialogData
}

export const ErrorLogsDialog = ({ onClose, open, errorLogsDialogData }: ErrorLogsDialogProps) => {
  const { t } = useTranslation()

  const handleClose = () => {
    onClose()
  };

  return (
    <Dialog
      onClose={handleClose}
      maxWidth={false}
      fullWidth
      open={open}
    >
      <header>
        <DialogTitle fontWeight="bold">Log</DialogTitle>
      </header>

      <Divider />
      <nav aria-label="others errors log information">
        <List sx={{ overflowX: 'auto', px: '0.5rem' }}>
          <ListItem>
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
              <ListItemText primary={t('Trace')} sx={{ minWidth: 120 }} />
              <Typography variant="body2">{errorLogsDialogData.trace}</Typography>
            </Box>
          </ListItem>
          <Divider />
          <ListItem>
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
              <ListItemText primary={t('CreateDate')} sx={{ minWidth: 120 }} />
              <Typography variant="body2">{errorLogsDialogData.createDate}</Typography>
            </Box>
          </ListItem>
          <Divider />
          <ListItem>
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
              <ListItemText primary={t('QueryString')} sx={{ minWidth: 120 }} />
              <Typography variant="body2">{errorLogsDialogData.queryString}</Typography>
            </Box>
          </ListItem>
          <Divider />
          {errorLogsDialogData.innerMessage && (
            <>
              <ListItem>
                <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
                  <ListItemText primary={t('InnerMessage')} sx={{ minWidth: 120 }} />
                  <Typography variant="body2">{errorLogsDialogData.innerMessage}</Typography>
                </Box>
              </ListItem>
              <Divider />
            </>
          )}
          <ListItem>
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
              <ListItemText primary={t('Process')} sx={{ minWidth: 120 }} />
              <Typography variant="body2">{errorLogsDialogData.process}</Typography>
            </Box>
          </ListItem>
          <Divider />
          <ListItem>
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '1rem' }}>
              <ListItemText primary={t('ErrorCode')} sx={{ minWidth: 120 }} />
              <Typography variant="body2">{errorLogsDialogData.errorCode}</Typography>
            </Box>
          </ListItem>
          <Divider />
        </List>
      </nav>

      <footer>
        <DialogActions>
          <Button variant="outlined" color="secondary" onClick={handleClose}>
            {t('Close')}
          </Button>
        </DialogActions>
      </footer>
    </Dialog>
  );
}