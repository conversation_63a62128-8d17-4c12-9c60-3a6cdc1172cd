import dayjs, { type Dayjs } from "dayjs"
import { useState } from "react";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next"

import {
  Box,
  Button,
  Card,
  CardContent,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material"
import { DatePicker as DatePickerMUI } from '@mui/x-date-pickers'
import { FilterAlt, FilterAltOff, Search } from "@mui/icons-material";

import { ErrorsReportService } from "@/services/errorsReport";

import { ErrorLogsDialog, type ErrorLogsDialogData } from "./error-logs-dialog";

interface ErrorLogsContentProps {
  reportAccountId: string | undefined
  accountId: number
  employeeId: string
  employeeName: string
}

export const ErrorLogsContent = ({ reportAccountId, accountId, employeeId, employeeName }: ErrorLogsContentProps) => {
  const { t } = useTranslation()

  const [initialDate, setInitialDate] = useState<dayjs.Dayjs>(dayjs(new Date()))
  const [finalDate, setFinalDate] = useState<dayjs.Dayjs>(dayjs(new Date()).add(1, 'week'))
  const [requestPath, setRequestPath] = useState<string>("")
  const [process, setProcess] = useState<string>("")
  const [errorCode, setErrorCode] = useState<string>("")
  const [applyFilters, setApplyFilters] = useState(false)
  const [selectedErrorReport, setSelectedErrorReport] = useState<ErrorLogsDialogData | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);


  const { listErrorsReport } = ErrorsReportService

  const handleClearFilters = () => {
    setInitialDate(dayjs(new Date()))
    setFinalDate(dayjs(new Date()).add(1, 'week'))
    setRequestPath('')
    setProcess('')
    setErrorCode('')
    setApplyFilters((prevState) => !prevState)
  }

  const { data: errorsReportData } = useQuery({
    queryKey: ['errors-report', initialDate, finalDate, requestPath, process, errorCode, reportAccountId, accountId],
    queryFn: async () => listErrorsReport({
      initialDate: initialDate.format('YYYY-MM-DD'),
      finalDate: finalDate.format('YYYY-MM-DD'),
      requestPath,
      process,
      errorCode,
      reportAccountId,
      accountId
    }),
    enabled: !!applyFilters
  })

  const itHasErrorsReportData = errorsReportData && errorsReportData.length >= 1

  return (
    <>
      <Card>
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              gap: '2rem',
              alignItems: 'center',
              p: '0.5rem 1rem'
            }}
          >
            <TextField
              onChange={({ target }) => setRequestPath(target.value)}
              slotProps={{
                inputLabel: {
                  sx: {
                    '&.Mui-focused': {
                      color: 'black'
                    }
                  }
                }
              }}
              label={t('RequestPath')}
              variant="standard"
            />
            <TextField
              onChange={({ target }) => setProcess(target.value)}
              slotProps={{
                inputLabel: {
                  sx: {
                    '&.Mui-focused': {
                      color: 'black'
                    }
                  }
                }
              }}
              label={t('Process')}
              variant="standard"
            />
            <TextField
              onChange={({ target }) => setErrorCode(target.value)}
              slotProps={{
                inputLabel: {
                  sx: {
                    '&.Mui-focused': {
                      color: 'black'
                    }
                  }
                }
              }}
              label={t('ErrorCode')}
              variant="standard"
            />

            <DatePickerMUI
              label={t('InitialDate').toUpperCase()}
              value={initialDate}
              maxDate={finalDate}
              onChange={(date) => setInitialDate(date as Dayjs)}
              format="DD/MM/YYYY"
              slotProps={{
                popper: {
                  placement: 'bottom-end',
                  modifiers: [
                    {
                      name: "preventOverflow",
                      options: {
                        altAxis: true,
                        tether: true,
                        rootBoundary: "viewport",
                        padding: 8,
                      },
                    },
                  ],
                },
              }}
            />

            <DatePickerMUI
              label={t('FinalDate').toUpperCase()}
              value={finalDate}
              minDate={initialDate}
              maxDate={finalDate}
              onChange={(date) => setFinalDate(date as Dayjs)}
              format="DD/MM/YYYY"
              slotProps={{
                popper: {
                  placement: 'bottom-start',
                  modifiers: [
                    {
                      name: "preventOverflow",
                      options: {
                        altAxis: true,
                        tether: true,
                        rootBoundary: "viewport",
                        padding: 8,
                      },
                    },
                  ],
                },
              }}
            />
            <Box
              sx={{
                display: 'flex',
                gap: '0.5rem'
              }}
            >
              <Button
                startIcon={<FilterAlt />}
                color="success"
                variant="contained"
                onClick={() => setApplyFilters((prevState) => !prevState)}
              >
                {t('ApplyFilters')}
              </Button>
              <Button
                startIcon={<FilterAltOff />}
                color="secondary"
                variant="contained"
                onClick={handleClearFilters}
              >
                {t('ClearFilters')}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {itHasErrorsReportData && (
        <TableContainer
          component={Paper}
          elevation={2}
          sx={{
            marginTop: '2rem'
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="approvals table"
          >
            <TableHead>
              <TableRow>
                <TableCell>{t('EmployeeID')}</TableCell>
                <TableCell>{t('Name')}</TableCell>
                <TableCell>{t('StatusCode')}</TableCell>
                <TableCell>{t('Method')}</TableCell>
                <TableCell>{t('RequestPath')}</TableCell>
                <TableCell>{t('Message')}</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {errorsReportData.map(errorReport => (
                <TableRow key={errorReport.id}>
                  <TableCell>{employeeId}</TableCell>
                  <TableCell>{employeeName}</TableCell>
                  <TableCell>{errorReport.statusCode}</TableCell>
                  <TableCell>{errorReport.method}</TableCell>
                  <TableCell>{errorReport.requestPath}</TableCell>
                  <TableCell
                    sx={{
                      maxWidth: 200,
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'normal'
                    }}
                  >
                    {errorReport.message}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => {
                        setSelectedErrorReport({
                          trace: errorReport.stackTrace,
                          createDate: errorReport.created,
                          queryString: errorReport.queryString,
                          innerMessage: errorReport.innerMessage,
                          process: errorReport.process,
                          errorCode: errorReport.errorCode
                        });
                        setDialogOpen(true);
                      }}
                    >
                      <Search />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {selectedErrorReport && (
            <ErrorLogsDialog
              open={dialogOpen}
              onClose={() => setDialogOpen(false)}
              errorLogsDialogData={selectedErrorReport}
            />
          )}
        </TableContainer>
      )}
    </>
  )
}