import { useTranslation } from "react-i18next"

import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from "@mui/material"

import type { IPermissionsApproval } from "@/interfaces"

interface PermissionsContentProps {
  permissionsData: IPermissionsApproval[] | undefined
}

export const PermissionsContent = ({ permissionsData }: PermissionsContentProps) => {
  const { t } = useTranslation()

  const itHasPermissionsData = permissionsData && permissionsData.length >= 1

  return (
    <TableContainer component={Paper} elevation={2}>
      <Table sx={{ minWidth: 650 }} aria-label="permissions-table">
        <TableHead>
          <TableRow>
            <TableCell>{t('Processes')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {itHasPermissionsData && (
            <>
              {permissionsData.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell>
                    <Box style={{
                      display: 'flex',
                      width: '100%',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      {permission.process}
                      {!permission.active && (
                        <Button
                          color="secondary"
                          variant="outlined">{t('Reactivate')}
                        </Button>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  )
}