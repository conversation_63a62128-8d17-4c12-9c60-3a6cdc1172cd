import { ApprovalOrigin } from "@/api/approval/approval.service";
import { AllProcessesTypes } from "@/components/AccordionTable";
import Loading from "@/components/Loading";
import { MassApprovalReprovalButton } from "@/components/MassApprovalReprovalButton";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useCards } from "@/stores/cardsStore";
import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { Box, Card, CardContent, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { t } from "i18next";
import { JSX, useEffect, useState } from "react";
import { AccordionTable } from "../../components";
import { useAuth } from "../../hooks";

const sectionStyle = (isMobile: boolean) => ({
  padding: isMobile ? "2rem 1rem" : "2rem 0rem",
});

const EmptySection = ({ isMobile }: { isMobile: boolean }) => (
  <section style={sectionStyle(isMobile)}>
    <Card>
      <CardContent>
        <Typography component="span" sx={{ fontWeight: "bold" }}>
          {t("No.pending.items")}
        </Typography>
      </CardContent>
    </Card>
  </section>
);

const Home = (): JSX.Element => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { user } = useAuth();
  const { cards, loadingDataTable, handleCards, currentUser, setCurrentUser } =
    useCards(user);
  const { selectedRows, clearSelectedRows } = useSelectedRowsStore();

  console.log(selectedRows, "selectedRows");

  const { approveReproveDocument, isLoading, getApprovalStatus } =
    useApprovalDocument();

  const [reprovalModalState, setReprovalModalState] = useState<{
    open: boolean;
    rows: AllProcessesTypes[];
  }>({
    open: false,
    rows: [],
  });

  useEffect(() => {
    if (user !== currentUser) {
      setCurrentUser(user);
    }
  }, [user, currentUser]);

  useEffect(() => {
    if (user?.authenticated) {
      // Força o carregamento dos cards sempre que o usuário mudar
      // Isso inclui quando representando subordinados
      handleCards(user); // Force sem cache para garantir dados atualizados
    }
  }, [
    user?.authenticated,
    user?.accessToken,
    user?.userReading,
    user?.permissionsApproval,
    user,
    handleCards,
  ]);

  const handleOpenReprovalModal = (rows: AllProcessesTypes[]) => {
    setReprovalModalState({
      open: true,
      rows,
    });
  };

  const handleCloseReprovalModal = () => {
    setReprovalModalState({
      open: false,
      rows: [],
    });
  };

  const handleMassReproval = () => {
    if (!Object.keys(selectedRows)?.length) return;
    const flattenedRows = Object.values(selectedRows).flat();
    handleOpenReprovalModal(flattenedRows);
  };

  const handleConfirmMassReproval = async (reason: string) => {
    const rows = reprovalModalState.rows;
    if (!rows?.length) return;

    try {
      const promises = Object.values(selectedRows)
        .flat()
        .map(async (row) => {
          return approveReproveDocument({
            rowData: row,
            origin: row.origin as ApprovalOrigin,
            process: row.type,
            reason,
            status: getApprovalStatus(
              row.origin as ApprovalOrigin,
              false,
              row.origin === "SAP" && row.process === "NC"
            ),
          });
        });

      await Promise.all(promises);
    } catch (error) {
      console.error("Error during mass reproval:", error);
    }

    // Clear selection after processing
    clearSelectedRows();

    handleCloseReprovalModal();
  };

  const handleMassApproval = async () => {
    if (!Object.keys(selectedRows)?.length) return;

    try {
      const promises = Object.values(selectedRows)
        .flat()
        .map(async (row) => {
          return approveReproveDocument({
            rowData: row,
            origin: row.origin as ApprovalOrigin,
            process: row.type,
            status: getApprovalStatus(
              row.origin as ApprovalOrigin,
              true,
              false
            ),
          });
        });

      await Promise.all(promises);
    } catch (error) {
      console.error("Error during mass approval:", error);
    }

    clearSelectedRows();
  };

  return (
    <>
      {loadingDataTable ? (
        <Loading open={loadingDataTable} />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          {cards.length === 0 && <EmptySection isMobile={isMobile} />}
          {cards.length > 0 && (
            <section style={sectionStyle(isMobile)}>
              {cards?.map((card) => {
                return (
                  <AccordionTable
                    key={`${card.process}-${card.type}`}
                    process={card.process}
                    title={card.title}
                    headerData={card.headerData}
                    detailData={card.detailData}
                    total={card.total}
                    type={card.type}
                  />
                );
              })}
            </section>
          )}
        </Box>
      )}
      <MassApprovalReprovalButton
        handleMassReproval={handleMassReproval}
        handleConfirmMassReproval={handleConfirmMassReproval}
        handleMassApproval={handleMassApproval}
        reprovalModalState={reprovalModalState}
        handleCloseReprovalModal={handleCloseReprovalModal}
      />

      <Loading open={isLoading} />
    </>
  );
};

export default Home;
