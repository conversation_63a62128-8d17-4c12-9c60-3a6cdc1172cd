import {
  InteractionRequiredAuthError,
  SilentRequest,
} from "@azure/msal-browser";
import { useMsal } from "@azure/msal-react";
import { useCallback, useEffect, useState } from "react";

const useMsalToken = () => {
  const { instance, accounts, inProgress } = useMsal();
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Verificar quando a inicialização estiver concluída
  useEffect(() => {
    if (inProgress === "none") {
      setIsInitialized(true);

      // Verificar se já temos contas e token
      if (accounts && accounts.length > 0) {
        // Apenas verificar se já temos um token disponível
        const request: SilentRequest = {
          account: accounts[0],
          scopes: [],
        };

        // Tentar obter token silenciosamente, mas não iniciar login
        // O login é iniciado no main.tsx
        instance
          .acquireTokenSilent(request)
          .then((response) => {
            console.log("Token obtido silenciosamente no hook");
            setToken(response.accessToken);
          })
          .catch((err) => {
            console.error("Erro ao obter token silenciosamente no hook:", err);
            // Não tratamos o erro aqui, deixamos para o getToken lidar com isso
          });
      }
    }
  }, [inProgress, instance, accounts]);

  const handleError = (err: unknown) => {
    setError(err as Error);
    return null;
  };

  const getToken = useCallback(async () => {
    // Verificar se o MSAL está inicializado
    if (!isInitialized) {
      console.log("MSAL ainda não foi inicializado");
      return null;
    }

    if (!accounts || accounts.length === 0) {
      setError(new Error("Nenhuma conta MSAL encontrada"));
      try {
        const response = await instance.loginPopup();
        setToken(response.accessToken);
        return response.accessToken;
      } catch (loginError) {
        return handleError(loginError);
      }
    }

    const request: SilentRequest = {
      account: accounts[0],
      scopes: [],
    };

    try {
      // This will no longer throw this error since initialize completed before this was invoked
      const response = await instance.acquireTokenSilent(request);
      setToken(response.accessToken);
      return response.accessToken;
    } catch (error) {
      if (error instanceof InteractionRequiredAuthError) {
        try {
          const response = await instance.acquireTokenPopup(request);
          setToken(response.accessToken);
          return response.accessToken;
        } catch (popupError) {
          return handleError(popupError);
        }
      } else {
        return handleError(error);
      }
    }
  }, [instance, accounts, isInitialized]);

  return { token, error, getToken, isInitialized };
};

export default useMsalToken;
