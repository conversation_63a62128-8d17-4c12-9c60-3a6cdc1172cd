import React, { createContext, useContext, useState } from "react";

export interface IListErrorsProps {
  title: string;
  type: string;
  subtitle?: string;
  cod?: string;
}

interface ErrorContextProps {
  listErrors: IListErrorsProps[];
  handleSetListErrors(
    title: string,
    type: string,
    subtitle?: string,
    cod?: string
  ): void;
}

export const ErrorContext = createContext<ErrorContextProps>(
  {} as ErrorContextProps
);

export const ErrorProvider: React.FC = ({ children }) => {
  const [listErrors, setListErrors] = useState<IListErrorsProps[]>([]);

  const handleSetListErrors = (
    title: string,
    subtitle: string,
    cod: string,
    type: string
  ) => {
    setListErrors((prev) => [...prev, { title, subtitle, cod, type }]);
  };

  const context = {
    listErrors,
    handleSetListErrors,
  };

  return (
    <ErrorContext.Provider value={context}>{children}</ErrorContext.Provider>
  );
};

export const useError = (): ErrorContextProps => {
  const context = useContext(ErrorContext);

  if (!context) {
    throw new Error("useError must be used within an ErrorProvider");
  }

  return context;
};
