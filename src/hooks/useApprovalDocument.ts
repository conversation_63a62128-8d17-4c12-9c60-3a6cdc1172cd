import {
  ApprovalOrigin,
  ApprovalService,
  IApprovalService,
} from "@/api/approval/approval.service";
import { handleApiError } from "@/services";
import { useCards } from "@/stores/cardsStore";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { toast } from "react-toastify";

interface ApprovalDocumentReturn {
  approveReproveDocument: (data: IApprovalService) => void;
  isLoading: boolean;
  getApprovalStatus: (
    origin: ApprovalOrigin,
    isApproved: boolean,
    isNcProcess: boolean
  ) => string;
}

export const useApprovalDocument = <T>() => {
  const { t } = useTranslation();
  const { updateDataAndCounter } = useCards();

  const { mutate, isLoading } = useMutation({
    mutationFn: async (data: IApprovalService) => {
      const apiService = new ApprovalService<T>(data);
      return apiService.approveReproveDocument();
    },
    onSuccess: async (response, variables) => {
      console.log("Hook Response:", response);
      
      if (response.success) {
        toast.success(response.message);
        updateDataAndCounter(
          variables.process ?? "",
          response?.data.header?.length,
          response?.data.header || [],
          response?.data.detail || []
        );
      } else {
        // Não exibe toast de erro se a mensagem indica que já foi processado
        // (evita duplicação com toast de warning do serviço)
        const isAlreadyProcessed = response.message?.includes("already been processed") || 
                                  response.message?.includes("já foi processado") ||
                                  response.message?.includes("Request has already been processed") ||
                                  response.message?.includes("already processed") ||
                                  response.message?.includes("já processado") ||
                                  response.message?.includes("064") ||
                                  response.message?.includes("tipo E");
        
        console.log("Is Already Processed:", isAlreadyProcessed, "Message:", response.message);
        
        if (!isAlreadyProcessed) {
          toast.error(response.message);
        }
      }
    },
    onError: (error, variables) => {
      handleApiError(error, t, variables.origin, variables.process);
    },
  });

  return {
    approveReproveDocument: mutate,
    isLoading,
    getApprovalStatus: (
      origin: ApprovalOrigin,
      isApproved: boolean,
      isNcProcess: boolean = false
    ): string =>
      ApprovalService.getApprovalStatus(origin, isApproved, isNcProcess),
  } as ApprovalDocumentReturn;
};

