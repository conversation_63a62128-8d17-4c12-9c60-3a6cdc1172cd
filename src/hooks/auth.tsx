import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

import { jwtDecode } from "jwt-decode";

// import api from "../services/api";
// import { useError } from "./error";
import { t } from "i18next";
import { toast } from "react-toastify";
import { APIAuth, APIRepresentateAccount } from "../api";
import type { IPermissionsApproval, IUser } from "../interfaces";
import { AuthService } from "../services/authService";
import {
  getLocalStorageItem,
  removeLocalStorageItem,
  setLocalStorageItem,
} from "../utils/storage";
import i18n from "./translation";

export interface GetTokenForThisUser {
  employeeId: string;
  action?: "subordinate" | "reports" | "admin";
  permissionsApproval: IPermissionsApproval[];
  userReading: string;
}

interface AuthContextData {
  user: IUser;
  login(token?: string): Promise<IUser>;
  getTokenForThisUser(params: GetTokenForThisUser): Promise<string | null>;
  getTokenInitial(): void;
  setEmployeeRepresentedId(employeeRepresentedID: string): void;
  tutorialDone(): void;
  goBackToUserIntials(): Promise<IUser>;
  isLoading: boolean;
  isSessionExpired: boolean;
  setIsSessionExpired(isSessionExpired: boolean): void;
  showErrorModal: boolean;
  setShowErrorModal(showErrorModal: boolean): void;
  handleSessionExpired(): void;
  msalExpiration: Date | null;
}

export const AuthContext = createContext<AuthContextData>(
  {} as AuthContextData
);

interface AuthProviderProps {
  children: ReactNode;
}

const initialState = {
  accountId: 0,
  originalAccountId: "",
  tutorialDone: false,
  initials: "",
  employeeID: "",
  opid: "",
  employeeName: "",
  authenticated: false,
  accessToken: "",
  email: "",
  language: "",
  languageOption: "",
  exibitName: "",
  departmentId: "",
  showSilentNotification: false,
  created: "",
  expiration: "",
  permissionsApproval: [],
  permissionsReport: [],
  processApproval: [],
  accountIdinitial: "",
  employeeRepresentedID: "",
  hasFilter: false,
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<IUser>(initialState);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSessionExpired, setIsSessionExpired] = useState<boolean>(false);
  const [msalExpiration, setMsalExpiration] = useState<Date | null>(null);
  const [showErrorModal, setShowErrorModal] = useState(false);

  useEffect(() => {
    if (user && user.authenticated === false) {
      setShowErrorModal(true);
    }
    if (user && user.authenticated === true) {
      setShowErrorModal(false);
    }
  }, [user]);

  const setEmployeeRepresentedId = (employeeRepresentedID: string) => {
    setUser((prev) => ({ ...prev, employeeRepresentedID }));
  };

  const goBackToUserIntials = useCallback(async () => {
    try {
      toast.info(
        `${t(
          "You.tried.to.impersonate.a.user.who.does.not.have.permissions.we.will.return.you.to.the.user"
        )} ${user.accountId}`
      );
      setIsLoading(true);
      const accountIdOriginal = getLocalStorageItem("@BRFApprovalsInitial");
      if (accountIdOriginal) {
        setLocalStorageItem("@BRFApprovalsRepresent", "true");
        await APIRepresentateAccount.representateAccount({
          accountIdRepresentate: user.accountId,
          accountIdOriginal: Number(accountIdOriginal),
        });
      }
      toast.success(`${t("Welcome.back")} ${user.employeeName.split(" ")[0]}`);

      return user;
    } catch {
      toast.error(t("We.were.unable.to.go.back.to.the.previous.user"));
      return user;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const login = useCallback(async (token: string): Promise<IUser> => {
    try {
      setIsLoading(true);

      let fetchedToken = token;

      if (!fetchedToken) {
        const idToken = getLocalStorageItem("msalToken");
        if (!idToken) {
          throw new Error("Failed to get ID token");
        }
        fetchedToken = idToken;
      }

      const { data: userData } = await APIAuth.login(fetchedToken); // Usar fetchedToken em vez de token

      const usersSpecialPermissions = import.meta.env.VITE_PERMISSION_REPORT;

      setLocalStorageItem("employeeToken", userData.accessToken);
      setLocalStorageItem("employeeid", userData.employeeID);

      const updatedUserData = {
        ...userData,
        hasFilter:
          (usersSpecialPermissions &&
            usersSpecialPermissions?.includes(userData.employeeID)) ||
          (userData.permissionsReport && !!userData.permissionsReport.length),
      };

      setUser(updatedUserData);

      if (userData.authenticated) {
        // Primeiro, recarregar traduções com o token atual se necessário
        try {
          const { reloadTranslationsWithToken } = await import(
            "../hooks/translation"
          );
          if (
            userData.accessToken &&
            Object.keys(i18n.store.data).length === 0
          ) {
            await reloadTranslationsWithToken(userData.accessToken);
          }
        } catch {
          // Silently handle translation loading errors
        }

        // Depois, configurar idioma usando languageOption ou language como fallback
        const userLanguage = userData.languageOption || userData.language;

        if (userData.authenticated && userLanguage) {
          try {
            await i18n.changeLanguage(userLanguage);
          } catch {
            // Silently handle language change errors
          }
        }
      }

      if (!getLocalStorageItem("@BRFApprovalsRepresent")) {
        setLocalStorageItem("@BRFApprovalsInitial", userData.originalAccountId);
      } else {
        removeLocalStorageItem("@BRFApprovalsRepresent");
      }

      return updatedUserData;
    } catch (e: unknown) {
      if (e && typeof e === "object" && "response" in e) {
        const error = e as { response?: { status?: number } };
        if (error.response?.status === 401 && user.accountId) {
          return goBackToUserIntials();
        }
      }
      setUser(initialState);
      throw e;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Callback functions for session management
  const handleSessionExpired = useCallback(
    (isExpired?: boolean, expDate?: Date | null) => {
      if (expDate) {
        setMsalExpiration(expDate);
      }

      if (isExpired) {
        setIsSessionExpired(isExpired);
      }
      setIsLoading(false);
    },
    []
  );

  // Registrar callback no AuthService para receber dados do MSAL
  useEffect(() => {
    const handleLoading = async (isLoading: boolean) => {
      setIsLoading(isLoading || false);
    };

    const handleLogin = async (token: string): Promise<IUser> => {
      return login(token);
    };

    // Configurar callbacks primeiro
    AuthService.setNotifyAuthProgress(handleLoading);
    AuthService.setAuthLogin(handleLogin);
    AuthService.setSessionExpiredCallback(handleSessionExpired);

    // Cleanup callback on unmount
    return () => {
      AuthService.setNotifyAuthProgress(() => {});
      AuthService.setSessionExpiredCallback(() => {});
      AuthService.setAuthLogin(() => Promise.resolve({} as IUser));
    };
  }, []); // Remover dependências para executar apenas uma vez

  const getTokenForThisUser = useCallback(
    async ({
      employeeId,
      action,
      permissionsApproval,
      userReading,
    }: GetTokenForThisUser): Promise<string> => {
      try {
        setIsLoading(true);

        const { data } = await APIAuth.loginReporting(employeeId);

        const token = data.token;

        if (!token) {
          throw new Error("Token not found in API response");
        }

        const tokenDecoded: { AccountId: string } = jwtDecode(token);

        setUser((oldUser) => {
          setLocalStorageItem("employeeToken", token);

          const permissionsAllowed: IPermissionsApproval[] | null = [];

          if (
            oldUser.permissionsReport &&
            oldUser.permissionsReport.length >= 1
          ) {
            oldUser.permissionsReport.forEach((report: any) => {
              permissionsApproval.filter((permission: any) => {
                if (permission.process == report.process) {
                  return permissionsAllowed?.push(permission);
                }
              });
            });
          }

          const updatedUser = {
            ...oldUser,
            accessToken: token,
            accessTokenInitial:
              oldUser.accessTokenInitial || oldUser.accessToken,
            permissionsApprovalInitial:
              oldUser.permissionsApprovalInitial || oldUser.permissionsApproval,
            permissionsAllowed,
            onlyReading: action === "reports", //admin
            // processApproval: data.processApproval,
            permissionsApproval,
            userReading,
            reportAccountId: tokenDecoded.AccountId,
            employeeRepresentedID: employeeId,
            isRepresentingSubordinate: true,
          };

          return updatedUser;
        });

        return token;
      } catch (error) {
        console.error("Error in getTokenForThisUser:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const getTokenInitial = useCallback(() => {
    try {
      setIsLoading(true);

      setUser((oldUser) => {
        setLocalStorageItem(
          "employeeToken",
          oldUser.accessTokenInitial || oldUser.accessToken
        );

        return {
          ...oldUser,
          accessToken: oldUser.accessTokenInitial || oldUser.accessToken,
          permissionsApproval:
            oldUser.permissionsApprovalInitial || oldUser.permissionsApproval,
          accessTokenInitial: undefined,
          permissionsApprovalInitial: undefined,
          permissionsAllowed: undefined,
          permissionsReport: oldUser.permissionsReport,
          processApproval: oldUser.processApproval,
          onlyReading: false,
          userReading: undefined,
          reportAccountId: undefined,
        };
      });
    } catch {
      toast.error(t("We.were.unable.to.go.back.to.the.previous.user"));
      return user;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const tutorialDone = useCallback(() => {
    setUser((prev) => ({
      ...prev,
      tutorialDone: true,
    }));
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        getTokenForThisUser,
        getTokenInitial,
        setEmployeeRepresentedId,
        tutorialDone,
        goBackToUserIntials,
        isLoading,
        isSessionExpired,
        setIsSessionExpired,
        handleSessionExpired,
        msalExpiration,
        showErrorModal,
        setShowErrorModal,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth(): AuthContextData {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}
