import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { getLocalStorageItem } from "../utils/storage";

export const translationsCache: Record<
  string,
  Record<string, string | null>
> = {};

export const loadTranslations = async (language: string, token: string) => {
  const { APITranslation } = await import("../api/index");
  const { data, success } = await APITranslation.getTranslation(
    language,
    token
  );

  if (success) {
    const translations = data.reduce(
      (acc: Record<string, string | null>, item: { key: string; value: string | null }) => {
        acc[item.key] = item.value || null;
        return acc;
      },
      {}
    );

    return translations;
  }
};

export const loadAllLanguages = async (languages: string[], token: string) => {
  try {
    const promises = languages.map((language) =>
      loadTranslations(language, token)
    );
    const results = await Promise.all(promises);

    languages.forEach((language, index) => {
      const translations = results[index];
      if (translations) {
        translationsCache[language] = translations;
        i18n.addResourceBundle(
          language,
          "translation",
          translations,
          true,
          true
        );
      }
    });
  } catch {
    // Silently handle errors
  }
};

// Função para recarregar traduções com um token específico
export const reloadTranslationsWithToken = async (token: string) => {
  if (token) {
    const languages = ["pt-BR", "es-ES", "en-US"];
    await loadAllLanguages(languages, token);
    return true;
  } else {
    return false;
  }
};

// Função para verificar se traduções estão carregadas e carregar se necessário
export const ensureTranslationsLoaded = async () => {
  const hasTranslations = Object.keys(i18n.store.data).length > 0;
  
  if (!hasTranslations) {
    const token = getLocalStorageItem("employeeToken");
    if (token) {
      return await reloadTranslationsWithToken(token);
    } else {
      return false;
    }
  } else {
    return true;
  }
};

i18n.use(initReactI18next).init({
  lng: "pt-BR",
  fallbackLng: "pt-BR",
  debug: false,
  ns: ["translation"],
  defaultNS: "translation",
  keySeparator: false,
  interpolation: {
    escapeValue: false,
  },
  resources: {},
});

export const initializeTranslations = async () => {
  const token = getLocalStorageItem("employeeToken");

  if (token) {
    const languages = ["pt-BR", "es-ES", "en-US"];
    await loadAllLanguages(languages, token);
    return true;
  } else {
    return false;
  }
};

// Não inicializar automaticamente - será chamado quando necessário
// initializeTranslations();

i18n.on("languageChanged", async (language) => {
  if (!translationsCache[language]) {
    const token = getLocalStorageItem("employeeToken");
    if (token) {
      await loadTranslations(language, token);
    }
  }
});

export const translate = (key: string) => {
  return i18n.t(key);
};

export default i18n;
