import { useState } from "react";


import type { ISearchUser } from "@/interfaces";

import type { Option } from "@/components/ModalNewDelegation/components/Select";

export interface UseAdminReturn {
  currentTab: string;
  selectedValue: ISearchUser.SearchUsersAccounts[];

  handleSelectedValue: (value: Option[]) => void
  handleTabChange: (value: string) => void
};


export const useAdmin = (): UseAdminReturn => {
  const [currentTab, setCurrentTab] = useState("1");
  const [selectedValue, setSelectedValue] = useState<ISearchUser.SearchUsersAccounts[]>([]);

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };

  const handleSelectedValue = (value: Option[]) => {
    setSelectedValue(value)
  }

  return {
    currentTab,
    selectedValue,
    handleTabChange,
    handleSelectedValue,
  };
}