import { useCallback, useState } from "react";

type ModalState<T> = {
  open: boolean;
  data: T | T[];
};

type ApprovalModalState<T> = {
  open: boolean;
  type: "approve" | "disapprove" | "return" | null;
  row: T | null;
};

export function useDataTableCommons<T>() {
  const [modalState, setModalState] = useState<ModalState<T>>({
    open: false,
    data: [] as T[],
  });

  const [approvalModalState, setApprovalModalState] = useState<
    ApprovalModalState<T>
  >({
    open: false,
    type: null,
    row: null,
  });

  const handleOpenModal = useCallback((data: T | T[]) => {
    setModalState({ open: true, data });
  }, []);

  const handleCloseModal = useCallback(() => {
    setModalState({ open: false, data: [] as T[] });
  }, []);

  const handleOpenApprovalModal = useCallback(
    (row: T, type: "approve" | "disapprove" | "return") => {
      setApprovalModalState({ open: true, type, row });
    },
    []
  );

  const handleCloseApprovalModal = useCallback(() => {
    setApprovalModalState({ open: false, type: null, row: null });
  }, []);

  const handleModalReject = useCallback(
    (data: T | T[]) => {
      const row = Array.isArray(data) ? data[0] : data;
      handleOpenApprovalModal(row, "disapprove");
      handleCloseModal();
    },
    [handleOpenApprovalModal, handleCloseModal]
  );

  return {
    modalState,
    setModalState,
    approvalModalState,
    setApprovalModalState,
    handleOpenModal,
    handleCloseModal,
    handleOpenApprovalModal,
    handleCloseApprovalModal,
    handleModalReject,
  };
}
