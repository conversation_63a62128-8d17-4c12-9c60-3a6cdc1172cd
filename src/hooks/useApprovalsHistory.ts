import { useState, ChangeEvent } from "react"
import { useQuery } from "react-query"
import dayjs, { type Dayjs } from "dayjs";

import { ApprovalsHistoryService, type ApprovalHistoryResponse, type ExportApprovalsHistoryFormat } from "@/services/approvalsHistory"

type ApprovalStatus = 'All' | 'A' | 'R' | 'P';

interface RequestTypes {
  label: string;
}

interface UseApprovalsHistoryReturn {
  approvalsHistoryData: ApprovalHistoryResponse | undefined;
  isLoadingApprovalsHistoryData: boolean;

  initialDate: Dayjs;
  finalDate: Dayjs;
  requestTypes: RequestTypes;
  approvalStatus: string;

  handlePageChange: (newPage: number) => void;
  handleRowsPerPageChange: (newPageSize: string) => void

  handleInitialDateChange: (date: Dayjs) => void;
  handleFinalDateChange: (date: Dayjs) => void;
  handleRequestTypeChange: (value: string) => void;
  handleApprovalStatusChange: (event: ChangeEvent<{ value: string }>) => void;

  handleClearFilters: () => void;

  exportFormat: ExportApprovalsHistoryFormat;
  handleChooseExportFormat: (exportFormat: ExportApprovalsHistoryFormat) => void;
  refetchExportApprovalsHistory: () => void;
  isExporting: boolean;
}

export const useApprovalsHistory = (): UseApprovalsHistoryReturn => {
  const [initialDate, setInitialDate] = useState<Dayjs>(dayjs(new Date()).subtract(1, 'month'))
  const [finalDate, setFinalDate] = useState<Dayjs>(dayjs(new Date()))
  const [requestTypes, setRequestTypes] = useState({
    label: '',
  })
  const [approvalStatus, setApprovalStatus] = useState<ApprovalStatus>('All')
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [exportFormat, setExportFormat] = useState<ExportApprovalsHistoryFormat>('' as ExportApprovalsHistoryFormat);

  const { getApprovalsHistory, exportApprovalsHistory } = ApprovalsHistoryService

  const handleInitialDateChange = (date: Dayjs) => {
    setInitialDate(date);
  }

  const handleFinalDateChange = (date: Dayjs) => {
    setFinalDate(date);
  }

  const handleRequestTypeChange = (value: string) => {
    setRequestTypes({
      label: value
    })
  }

  const handleApprovalStatusChange = (event: ChangeEvent<{ value: string }>) => {
    setApprovalStatus(event.target.value as ApprovalStatus);
  }

  const handleClearFilters = () => {
    setInitialDate(dayjs(new Date()).subtract(1, 'month'))
    setFinalDate(dayjs(new Date()))
    setRequestTypes({ label: '' })
    setApprovalStatus('All')
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  }

  const handleRowsPerPageChange = (newPageSize: string) => {
    setPageSize(parseInt(newPageSize, 10));
    setPage(0);
  }

  const handleChooseExportFormat = (exportFormat: ExportApprovalsHistoryFormat) => {
    setExportFormat(exportFormat)
  }

  const splittedRequestTypes = requestTypes.label.split(' - ')[0]

  const { data: approvalsHistoryData, isLoading: isLoadingApprovalsHistoryData } = useQuery({
    queryKey: ['approvals-history', initialDate, finalDate, splittedRequestTypes, approvalStatus, page, pageSize],

    queryFn: async () => getApprovalsHistory({
      initialDate: initialDate?.format('YYYY-MM-DD'),
      finalDate: finalDate?.format('YYYY-MM-DD'),
      process: splittedRequestTypes,
      finalStatus: approvalStatus === 'All' ? '' : approvalStatus,
      page: page + 1,
      pageSize,
    }),
  })

  const {
    isFetching: isExporting,
    refetch: refetchExportApprovalsHistory
  } = useQuery({
    queryKey: ['export-approvals-history', initialDate, finalDate, splittedRequestTypes, approvalStatus, exportFormat],

    queryFn: async () => exportApprovalsHistory({
      initialDate: initialDate?.toISOString(),
      finalDate: finalDate?.toISOString(),
      process: splittedRequestTypes,
      finalStatus: approvalStatus === 'All' ? '' : approvalStatus,
      exportFormat,
    }),

    enabled: exportFormat !== '' as ExportApprovalsHistoryFormat,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    onSuccess: () => setExportFormat('' as ExportApprovalsHistoryFormat)
  })

  return {
    approvalsHistoryData,
    isLoadingApprovalsHistoryData,

    initialDate,
    finalDate,
    requestTypes,
    approvalStatus,

    handlePageChange,
    handleRowsPerPageChange,

    handleInitialDateChange,
    handleFinalDateChange,
    handleRequestTypeChange,
    handleApprovalStatusChange,

    handleClearFilters,

    exportFormat,
    handleChooseExportFormat,
    refetchExportApprovalsHistory,
    isExporting,
  }
}