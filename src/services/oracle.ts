import { ApprovalResponse } from "@/api/approval/approval.service";
import { RouteOracle } from "@/api/routes";
import { oracleTypeRoute } from "@/api/routes/oracle";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import { api } from "../api/api";
import { IOracle } from "../interfaces";
import { IResponse } from "../interfaces/IResponse";

export const OracleService = {
  async approvalReprovalDocument(
    params: IOracle.ApprovalReprovalParams
  ): Promise<ApprovalResponse<IOracle.OracleItemType[]>> {
    try {
      const { data, status } = await api.post(
        RouteOracle.approvalReprovalDocument,
        params
      );

      if (data && status === 200) {
        const { updatedHeaderData } =
          await OracleService.updateDataAfterApproval(
            Object.keys(oracleTypeRoute).find(
              (key) => oracleTypeRoute[key] === params.type
            ) as IOracle.OracleTypesProcess
          );

        return {
          data: { header: updatedHeaderData },
          success: true,
          message: `${
            params.statusCode === "APPROVED"
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.id}`,
        };
      }
      return {
        data: { header: [] },
        success: false,

        message: `${t("Error.in.approval")} ${t("Document")} ${params.id}`,
      };
    } catch (error) {
      return {
        data: { header: [] as IOracle.OracleItemType[] },
        success: false,
      };
    }
  },

  async prepareTableData(
    headerData: IOracle.OracleResponse,
    typeProcess: string
  ): Promise<IOracle.OracleItemType[]> {
    if (!headerData) return [];

    const propertyName = oracleTypeRoute[typeProcess];
    if (!propertyName) return [];

    const data = headerData[propertyName as keyof IOracle.OracleResponse] as
      | IOracle.OracleItemType[]
      | undefined;

    const filteredData = data?.filter((item) => item && item !== undefined);

    return filteredData || [];
  },
  async fetchAndPreparePendingApprovals(
    processType: IOracle.OracleTypesProcess,
    cache: boolean = true
  ): Promise<IResponse<IOracle.OracleItemType[]>> {
    try {
      const { data, status } = await api.get(
        RouteOracle.getPendingApprovals(processType),
        {
          params: { cache },
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );

      const preparedTableData = await this.prepareTableData(data, processType);

      return {
        data:
          preparedTableData.map((item) => ({
            ...item,
            origin: "ORACLE",
            type: processType,
            email: data.email,
          })) || [],
        detailData: data,
        success: status === 200,
      };
    } catch (error) {
      return {
        data: [],
        success: false,
      };
    }
  },
  async updateDataAfterApproval(
    process: IOracle.OracleTypesProcess
  ): Promise<{ updatedHeaderData: IOracle.OracleItemType[] }> {
    try {
      const { data } = await this.fetchAndPreparePendingApprovals(process);

      if (!data) {
        return {
          updatedHeaderData: [],
        };
      }

      const headerData = data as IOracle.OracleItemType[];

      return {
        updatedHeaderData: headerData.map((item: IOracle.OracleItemType) => ({
          ...item,
          origin: "ORACLE",
          type: process,
        })),
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      // Return current data in case of error to maintain state
      return {
        updatedHeaderData: [],
      };
    }
  },
};
