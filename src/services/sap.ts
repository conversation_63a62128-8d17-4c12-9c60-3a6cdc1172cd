import { ApprovalResponse } from "@/api/approval/approval.service";
import { ExtraParamsType } from "@/api/sap";
import { IResponse } from "@/interfaces/IResponse";
import { t } from "i18next";
import { toast } from "react-toastify";
import { APISap } from "../api";
import i18n from "../hooks/translation";
import { ISap } from "../interfaces";
import { getUniqueDocs } from "../utils/UniqueDocs";

export const SapService = {
  /**
   * Busca todas as aprovações pendentes da API SAP e prepara os dados para a tabela
   * @param permission - Identificador do processo
   * @returns Dados preparados para exibição na tabela
   */
  fetchAndPreparePendingApprovals: async ({
    permission,
    extraParams,
  }: {
    permission: string;
    extraParams: ExtraParamsType;
  }): Promise<IResponse<ISap.ItemProps[]>> => {
    try {
      const { data, success } = await APISap.getPendingApprovals({
        permission,
        extraParams,
      });

      if (!data.retorno || !data.retorno.length) {
        return { data, detailData: [], success };
      }

      const headerData = data.retorno;

      const dataPrepared = SapService.prepareTableData(headerData);

      return {
        data: dataPrepared.map((item: ISap.ItemProps) => ({
          ...item,
          origin: "SAP",
          type: permission,
        })),
        detailData: headerData.map((item: ISap.ItemProps) => ({
          ...item,
          origin: "SAP",
          type: permission,
        })),
        success,
      };
    } catch (error) {
      console.error("Error fetching pending approvals:", error);
      toast.error(i18n.t("common.errorFetchingData"));
      throw error;
    }
  },

  /**
   * Busca detalhes de documentos da API
   */
  getDocumentDetails: async (
    documentNumber: string,
    process: string,
    row: ISap.ItemProps,
    additional1?: string
  ): Promise<ISap.ItemProps | ISap.ItemProps[] | undefined> => {
    try {
      const { data } = await APISap.getDocumentDetails(
        documentNumber,
        process,
        additional1
      );

      const details =
        process === "VX" || process === "NC" || process === "NF"
          ? [row, ...data.retorno]
          : data.retorno;

      if (details) {
        return details;
      }
      if (!details?.length) {
        toast.error("Nenhum detalhe encontrado para o documento.");
      }
    } catch (error) {
      console.error("Error fetching document details:", error);
      throw error;
    }
  },

  /**
   * Filtra os detalhes de um documento específico
   */
  filterDocumentDetails: (
    detailData: ISap.ItemProps[],
    documentNumber: string
  ): ISap.ItemProps[] => {
    if (!detailData?.length) return [];

    return detailData.filter(
      (detail: ISap.ItemProps) =>
        detail.documento?.toString().trim() ===
        documentNumber?.toString().trim()
    );
  },

  /**
   * Prepara os dados para a tabela principal
   */
  prepareTableData: (headerData: ISap.ItemProps[]): ISap.ItemProps[] => {
    if (!headerData) return headerData;

    return getUniqueDocs(headerData).map((doc) => {
      const foundItem = headerData?.find(
        (resp: ISap.ItemProps) => resp.documento === doc
      );
      return {
        ...foundItem,
        adicionais: foundItem?.adicionais || [],
      } as ISap.ItemProps;
    });
  },

  createApprovalParams: (
    row: ISap.ItemProps,
    status: "A" | "R" | "B",
    reason: string | undefined,
    process: string,
    isDetail: boolean,
    additional1: (row: ISap.ItemProps) => string,
    additional2: (row: ISap.ItemProps) => string
  ): ISap.ApprovalReprovalParams => {
    return {
      document: row.documento,
      item: isDetail ? row.item : "",
      status,
      additional1: additional1 ? additional1(row) : "",
      additional2: additional2 ? additional2(row) : "",
      reason: reason || "",
      language: i18n.language,
      process,
      ecp: row.ecp ?? !!["H5", "AM"].includes(process),
    };
  },

  /**
   * Atualiza os dados após aprovação/reprovação
   */
  updateDataAfterApproval: async (
    process: string
  ): Promise<{
    updatedHeaderData: ISap.ItemProps[];
    updatedDetailData: ISap.ItemProps[];
  }> => {
    try {
      const sapParams = {
        permission: process || "",
        extraParams: {
          ecp: ["H5", "AM"].includes(process ?? "") ? "true" : "false",
          cacheActive: "true",
          language: i18n.language,
          cacheLiveTime: 15,
        },
      };

      const { data, detailData } =
        await SapService.fetchAndPreparePendingApprovals(sapParams);

      if (!data?.length) {
        return {
          updatedHeaderData: [],
          updatedDetailData: [],
        };
      }

      return {
        updatedHeaderData: data,
        updatedDetailData: detailData ?? [],
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },

  /**
   * Executa a aprovação/reprovação de um documento
   */
  approvalReprovalDocument: async (
    params: ISap.ApprovalReprovalParams
  ): Promise<ApprovalResponse<ISap.ItemProps[]>> => {
    try {
      const response = await APISap.approvalReprovalDocument(params);

      if (response.success) {
        if (response.data?.msg[0]) {
          if (
            response.data.msg[0].tipo === "E" ||
            response.data.msg[0].codigo === "064"
          ) {
            toast.warning(response.data.msg[0].mensagem);
            return {
              success: false,
              data: {
                header: [],
                detail: [],
                ...response.data,
              },
            };
          }
        }
        // Se a aprovação/reprovação foi bem sucedida, atualiza os dados
        const { updatedHeaderData, updatedDetailData } =
          await SapService.updateDataAfterApproval(params.process);

        return {
          message: `${
            params.status === "A"
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.document}`,
          success: true,
          data: {
            ...response.data,
            header: updatedHeaderData,
            detail: updatedDetailData,
          },
        };
      }

      return {
        success: false,
        data: {
          header: [],
          detail: [],
          ...response.data,
        },
        message: `${t("Error.in.approval")} ${t("Document")} ${
          params.document
        }`,
      };
    } catch (error) {
      console.error("Error in approval/reproval:", error);
      throw error;
    }
  },
} as const;
