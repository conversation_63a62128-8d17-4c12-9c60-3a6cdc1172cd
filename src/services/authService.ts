import { IUser } from "@/interfaces";
import {
  AccountInfo,
  AuthenticationResult,
  EventType,
  PublicClientApplication,
} from "@azure/msal-browser";
import { loginRequest, pca } from "../config/msalConfig";

// Global variables
let initializationPromise: Promise<boolean> | null = null;
let notifyAuthProgress: ((isLoading: boolean) => void | Promise<void>) | null =
  null;
let sessionExpiredCallback:
  | ((isExpired?: boolean, expDate?: Date | null) => void | Promise<void>)
  | null = null;

let authLogin: (token: string) => Promise<IUser>;

// Control flags to prevent duplicate token processing
let isProcessingToken = false;
let lastProcessedToken: string | null = null;
let tokenProcessingPromise: Promise<IUser> | null = null;
let sessionTokenProcessed = false;

export class AuthService {
  // Set callback function to be called when authentication is successful
  static setNotifyAuthProgress(
    callback: (isLoading: boolean) => void | Promise<void>
  ) {
    notifyAuthProgress = callback;
  }

  // Set callback function to be called when session expires
  static setSessionExpiredCallback(
    callback: (
      isExpired?: boolean,
      expDate?: Date | null
    ) => void | Promise<void>
  ) {
    sessionExpiredCallback = callback;
  }

  static setAuthLogin(callback: (token: string) => Promise<IUser>) {
    authLogin = callback;
  }

  // Method to trigger loading state during authentication
  static async notifyAuthProgress(isLoading: boolean) {
    if (notifyAuthProgress) {
      await notifyAuthProgress(isLoading);
    }
  }
  // Process token obtained from MSAL authentication
  static async processToken(token: string) {
    try {
      console.log(
        "🔄 processToken called with token:",
        token.substring(0, 20) + "..."
      );

      // Check if we already processed a token for this session
      if (sessionTokenProcessed) {
        console.log("🚫 Token already processed for this session, skipping");
        return null;
      }

      // Prevent duplicate processing of the same token
      if (isProcessingToken) {
        console.log(
          "🚫 Token already being processed, returning existing promise"
        );
        return tokenProcessingPromise;
      }

      if (lastProcessedToken === token) {
        console.log("🚫 Exact same token already processed, skipping");
        return null;
      }

      console.log(
        "🔄 Starting token processing for token:",
        token.substring(0, 20) + "..."
      );
      isProcessingToken = true;
      lastProcessedToken = token;

      // Store the processing promise to return it for concurrent calls
      tokenProcessingPromise = (async () => {
        try {
          const response = await authLogin(token);
          console.log(
            "✅ Token processing completed successfully:",
            response?.employeeName || "Unknown user"
          );

          // Mark session as having processed a token
          sessionTokenProcessed = true;

          return response;
        } catch (error) {
          console.error("❌ Token processing failed:", error);
          // Reset on error so it can be retried, but don't reset sessionTokenProcessed
          lastProcessedToken = null;
          throw error;
        } finally {
          this.notifyAuthProgress(false);
          isProcessingToken = false;
          tokenProcessingPromise = null;
        }
      })();

      return await tokenProcessingPromise;
    } catch (error) {
      console.error("❌ Error in processToken:", error);
      this.notifyAuthProgress(false);
      isProcessingToken = false;
      tokenProcessingPromise = null;
      return null;
    }
  }

  // Handle redirect from Azure AD authentication
  static async handleRedirect() {
    // Azure Best Practice: Ensure initialization before calling MSAL methods
    await this.ensureInitialized();

    try {
      console.log("🔍 Checking for redirect response...");
      const response = await pca.handleRedirectPromise();

      if (response && response.idToken) {
        console.log("✅ Redirect response found with idToken");
        localStorage.setItem("msalToken", response.idToken);
        return this.processToken(response.idToken);
      } else {
        console.log("ℹ️ No redirect response or no idToken in response");
        return null;
      }
    } catch (error) {
      console.error("❌ Error handling redirect:", error);
      return null;
    }
  }

  // Setup event handlers for MSAL events
  static setupEventHandlers() {
    pca.addEventCallback((event) => {
      switch (event.eventType) {
        case EventType.LOGIN_SUCCESS:
          // Só processar token no LOGIN_SUCCESS se não foi processado via redirect
          if (event.payload) {
            const authResult = event.payload as AuthenticationResult;
            if (authResult.idToken) {
              console.log("📢 LOGIN_SUCCESS event received");
              // Only process if not already processing the same token and session hasn't processed a token yet
              if (
                !sessionTokenProcessed &&
                lastProcessedToken !== authResult.idToken
              ) {
                console.log("🔄 Processing token from LOGIN_SUCCESS event");
                this.processToken(authResult.idToken);
              } else {
                console.log(
                  "🚫 Token already processed for this session or duplicate token from LOGIN_SUCCESS event"
                );
              }
            }
          }
          break;
        case EventType.ACQUIRE_TOKEN_SUCCESS:
          console.log("✅ ACQUIRE_TOKEN_SUCCESS event received");
          break;
        case EventType.LOGIN_FAILURE:
          console.log("❌ LOGIN_FAILURE event received");
          break;
        case EventType.ACQUIRE_TOKEN_FAILURE:
          console.log("❌ ACQUIRE_TOKEN_FAILURE event received");
          break;
      }
    });
  }

  // Azure Best Practice: Ensure MSAL is initialized before any operation
  static async ensureInitialized(): Promise<boolean> {
    if (!initializationPromise) {
      // Create the initialization promise only once
      initializationPromise = (async () => {
        try {
          // Azure Best Practice: Explicitly initialize MSAL
          if (pca instanceof PublicClientApplication) {
            await pca.initialize();

            // Setup event handlers after initialization
            this.setupEventHandlers();
            return true;
          } else {
            return false;
          }
        } catch {
          // Reset initialization promise on failure
          initializationPromise = null;
          return false;
        }
      })();
    }

    return initializationPromise;
  }

  // Initialize MSAL and handle authentication
  static async initialize() {
    try {
      console.log("🚀 Starting MSAL initialization...");

      // Ensure MSAL is initialized
      const initialized = await this.ensureInitialized();

      if (!initialized) {
        console.log("❌ MSAL initialization failed");
        return null;
      }

      console.log("✅ MSAL initialized successfully");
      this.notifyAuthProgress(true);

      // Handle any pending redirects first
      const redirectResult = await this.handleRedirect();
      if (redirectResult) {
        console.log(
          "✅ Redirect handled successfully, stopping further initialization"
        );
        return redirectResult;
      }

      // Azure Best Practice: Check accounts only after initialization
      const accounts = pca.getAllAccounts();
      console.log("🔍 Found accounts:", accounts.length);

      if (accounts.length === 0) {
        // No accounts found, clear any stale tokens first
        localStorage.removeItem("msalToken");
        localStorage.removeItem("employeeToken");
        localStorage.removeItem("employeeid");
        console.log("❌ No accounts found - redirecting to login");

        // Reset session token processing since we're starting over
        this.resetSessionTokenProcessing();

        // No need to call ensureInitialized again as we already did it above
        return this.loginRedirect(); // Return the promise to prevent further execution
      } else {
        const account = accounts[0];
        const idTokenClaims = account.idTokenClaims;

        if (idTokenClaims && idTokenClaims.exp) {
          const tokenExpOn = new Date(idTokenClaims.exp * 1000);
          const isTokenExpired =
            new Date().getTime() >= tokenExpOn.getTime() - 120000;

          if (isTokenExpired) {
            // Token is expired, clear storage before redirecting
            localStorage.removeItem("msalToken");
            localStorage.removeItem("employeeToken");
            localStorage.removeItem("employeeid");
            console.log("⏰ Token expired - redirecting to login");

            // Reset session token processing since token is expired
            this.resetSessionTokenProcessing();

            // No need to call ensureInitialized again
            return this.loginRedirect(); // Return the promise to prevent further execution
          } else {
            console.log("✅ Token is still valid - acquiring token silently");

            // Token is still valid
            sessionExpiredCallback?.(false, tokenExpOn);
            return this.acquireTokenSilently(account);
          }
        }

        // Return a default value if no token claims are available
        console.log("⚠️ No token claims available");
        return null;
      }
    } catch (error) {
      console.error("❌ Error during initialization:", error);
      return null;
    }
  }

  // Redirect to login page
  static async loginRedirect() {
    // Azure Best Practice: Ensure initialization before calling MSAL methods
    await this.ensureInitialized();

    try {
      await pca.loginRedirect(loginRequest);
      return true;
    } catch {
      return false;
    }
  }

  // Acquire token silently, with popup fallback
  static async acquireTokenSilently(account?: AccountInfo) {
    // Azure Best Practice: Ensure initialization before calling MSAL methods
    await this.ensureInitialized();

    try {
      // If no account is provided, get the first available account
      if (!account) {
        const accounts = pca.getAllAccounts();
        if (accounts.length === 0) {
          console.log("❌ No accounts found in cache, redirecting to login");
          await this.loginRedirect();
          return null;
        }
        account = accounts[0];
      }

      console.log(
        "🔄 Attempting silent token acquisition for:",
        account.username
      );

      // Azure Best Practice: Try silent token acquisition
      const response = await pca.acquireTokenSilent({
        account,
        scopes: loginRequest.scopes,
      });

      if (response.idToken) {
        console.log("✅ Silent token acquisition successful");

        // Check token expiration using account information
        const idTokenClaims = account.idTokenClaims;
        if (idTokenClaims && idTokenClaims.exp) {
          const tokenExpOn = new Date(idTokenClaims.exp * 1000);
          const isTokenExpired =
            new Date().getTime() >= tokenExpOn.getTime() - 120000;

          if (isTokenExpired) {
            console.log(
              "⏰ Token expired after acquisition - redirecting to login"
            );

            // Reset session token processing since token is expired
            this.resetSessionTokenProcessing();

            await this.loginRedirect();
          } else {
            console.log("✅ Token is valid, processing...");
            sessionExpiredCallback?.(false, tokenExpOn);

            // Only process token if it hasn't been processed for this session
            if (
              !sessionTokenProcessed &&
              lastProcessedToken !== response.idToken
            ) {
              await this.processToken(response.idToken);
              localStorage.setItem("msalToken", response.idToken);
            } else {
              console.log(
                "🚫 Token already processed for this session or duplicate token"
              );
            }
          }
        }
      }

      return response;
    } catch (error) {
      console.error("❌ Silent token acquisition failed:", error);
      // Azure Best Practice: Notify session expiration
      sessionExpiredCallback?.(true);

      // Reset session token processing since authentication failed
      this.resetSessionTokenProcessing();

      localStorage.removeItem("msalToken");
      localStorage.removeItem("employeeToken");
      localStorage.removeItem("employeeid");
      return null;
    }
  }

  // Manual login method for when user explicitly wants to login
  static async manualLogin() {
    try {
      await this.ensureInitialized();
      return this.forceNewLogin();
    } catch (error) {
      console.error("Manual login failed:", error);
      return null;
    }
  }

  // Force new login to get idToken with correct scopes
  static async forceNewLogin() {
    try {
      // Ensure MSAL is initialized
      await this.ensureInitialized();

      // Clear all cached tokens
      await pca.clearCache();

      // Clear localStorage tokens as well
      localStorage.removeItem("employeeToken");
      localStorage.removeItem("employeeid");
      localStorage.removeItem("msalToken");

      // Reset session token processing since we're forcing new login
      this.resetSessionTokenProcessing();

      // Force redirect login to get fresh tokens
      return this.loginRedirect();
    } catch {
      return null;
    }
  }

  // Debug method to check session state
  static getSessionState() {
    return {
      sessionTokenProcessed,
      isProcessingToken,
      lastProcessedToken: lastProcessedToken
        ? lastProcessedToken.substring(0, 20) + "..."
        : null,
      hasTokenProcessingPromise: !!tokenProcessingPromise,
      initializationPromise: !!initializationPromise,
    };
  }

  // Reset session token processing flag (for logout or token expiration)
  static resetSessionTokenProcessing() {
    console.log("🔄 Resetting session token processing flag");
    sessionTokenProcessed = false;
    isProcessingToken = false;
    lastProcessedToken = null;
    tokenProcessingPromise = null;
  }
}
