import { APICommercial } from "@/api";
import {
  ApprovalOrigin,
  ApprovalResponse,
} from "@/api/approval/approval.service";
import { t } from "i18next";
import { toast } from "react-toastify";
import { ICommercial } from "../interfaces";

export const CommercialService = {
  async updateDataAfterApproval(
    process: string,
    currentHeaderData: ICommercial.ICommercialProps[]
  ): Promise<{
    updatedHeaderData: ICommercial.ICommercialProps[];
    success: boolean;
  }> {
    try {
      const { data, success } = await APICommercial.pendingApprovals(
        process,
        false
      );

      if (!success || !data) {
        return {
          updatedHeaderData: currentHeaderData,
          success: false,
        };
      }

      return {
        updatedHeaderData: data.map((item) => ({
          ...item,
          type: process,
          origin: "COMMERCIAL" as ApprovalOrigin,
        })),
        success: true,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      return {
        updatedHeaderData: currentHeaderData,
        success: false,
      };
    }
  },
  createApprovalParams(
    params: ICommercial.ApprovalReprovalParams & {
      row: ICommercial.ICommercialProps;
    }
  ) {
    return {
      ...params,
      document: params.preCapNumber || params.row.id,
      rowData: params.row,
      process: params.row.type,
      type: params.row.type,
      isDetail: false,
    };
  },

  approvalReprovalDocument: async (
    process: string,
    params: ICommercial.IGCCParams | ICommercial.IPreCapParams
  ): Promise<ApprovalResponse<ICommercial.ICommercialProps[]>> => {
    try {
      const { data, success } =
        process === "GCC"
          ? await APICommercial.approvalReprovalDocumentGcc(
              params as unknown as ICommercial.IGCCParams
            )
          : await APICommercial.approvalReprovalDocumentSales(
              params as unknown as ICommercial.IPreCapParams
            );

      if (success) {
        if (data?.msg) {
          if (data.msg.type === "E" || data.msg.code === "064") {
            toast.warning(data.msg.message);
            return {
              success: false,
              data: {
                header: [],
                detail: [],
                ...data,
              },
            };
          }
        }

        const result = await CommercialService.updateDataAfterApproval(
          process,
          []
        );

        const document =
          process === "GCC"
            ? (params as ICommercial.IGCCParams).documentCode || ""
            : (params as ICommercial.IPreCapParams).preCapNumber || "";

        const message =
          process === "GCC"
            ? `${
                (params as ICommercial.IGCCParams).params.status === "A"
                  ? t("Success.in.approving")
                  : t("Failure.success")
              } ${t("Document")} ${document}`
            : `${
                (params as ICommercial.IPreCapParams).operation === "A"
                  ? t("Success.in.approving")
                  : t("Failure.success")
              } ${t("Document")} ${document}`;

        return {
          success: result.success,
          message,
          data: {
            header: result.updatedHeaderData,
            detail: [],
          },
        };
      }

      return {
        success: false,
        data: { header: [] },
        message: `${t("Error.in.approval")} ${t("Document")} ${document}`,
      };
    } catch (error) {
      console.error("Error in commercial approval/reproval:", error);
      throw error;
    }
  },
} as const;
