import { api } from "@/api/api";

const baseUrl = import.meta.env.VITE_ANALYTICS
const keyJobs = import.meta.env.VITE_KEY_JOBS

export interface ListErrorsReportParams {
  initialDate: string
  finalDate: string
  process: string
  errorCode: string
  requestPath: string
  reportAccountId: string | undefined
  accountId: number
}

export interface ListErrorsReportResponse {
  id: number;
  statusCode: number;
  method: string;
  requestPath: string;
  message: string;
  stackTrace: string;
  created: string;
  queryString: string;
  innerMessage: string | null;
  accountId: number;
  process: string;
  errorCode: string;
  name: string;
}

export const ErrorsReportService = {
  async listErrorsReport({ initialDate, finalDate, process, errorCode, requestPath, reportAccountId, accountId }: ListErrorsReportParams) {
    const params = new URLSearchParams();

    console.log('keyJobs: ', keyJobs);

    params.append('keyJobs', keyJobs);
    params.append('process', process);
    params.append('errorCode', errorCode);
    params.append('requestPath', requestPath);
    params.append('accountId', reportAccountId || String(accountId));

    const response = await api.get<ListErrorsReportResponse[]>(
      `${baseUrl}/ErrorReport/ErrorsReport/${initialDate}/${finalDate}?${params.toString()}`
    );

    return response.data
  }
}