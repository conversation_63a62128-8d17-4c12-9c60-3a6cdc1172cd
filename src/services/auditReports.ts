import { api } from "@/api/api";

const baseUrl = import.meta.env.VITE_ANALYTICS
const keyJobs = import.meta.env.VITE_KEY_JOBS

export interface ListAuditReportParams {
  initialDate: string
  finalDate: string
  process: string
  reportAccountId: string | undefined
  accountId: number
}

export interface ListAuditReportResponse {
  id: number;
  accountId: number;
  process: string;
  document: string;
  action: string;
  origin: string;
  ip: string;
  created: string;
  employeeId: string;
}

export const AuditReportService = {
  async listAuditReport({ initialDate, finalDate, process, reportAccountId, accountId }: ListAuditReportParams) {
    const params = new URLSearchParams();

    params.append('keyJobs', keyJobs);
    params.append('process', process);
    params.append('accountId', reportAccountId || String(accountId));

    const response = await api.get<ListAuditReportResponse[]>(
      `${baseUrl}/AuditReport/AuditReport/${initialDate}/${finalDate}?${params.toString()}`
    );

    return response.data
  }
}