import { processesProps } from "@/constant";
import { toast } from "react-toastify";

/**
 * Trata erros da API
 */
export const handleApiError = (
  error: any,
  t: (key: string) => string,
  origin?: string,
  process?: string
) => {
  const { title } = process ? processesProps(process)[0] : {};
  if (error.request && error.request.status === 500) {
    toast.error(
      <div>
        <div>{`${t("Error.Internal.Server")} ${t(title ?? "")} ${
          origin ? `${t("Of")} ${origin}` : ""
        }`}</div>
        <div style={{ fontSize: "12px", marginTop: "5px" }}>
          {t("Try.again.later")}
        </div>
      </div>
    );
  } else if (error.request && error.request.status === 401) {
    toast.error(
      <div>
        <div>{`${t("Process.Loading.Failed")} ${t(title ?? "")}`}</div>
        <div style={{ fontSize: "12px", marginTop: "5px" }}>
          {t("Unauthorized.refresh.the.screen.and.try.again")}
        </div>
      </div>
    );
  } else {
    toast.error(
      <div>
        <div>{`${t("Connection.Service.Error")}${
          origin ? ` ${origin}` : ""
        }`}</div>
        <div style={{ fontSize: "12px", marginTop: "5px" }}>
          {error.message ? `${error.message}. ` : ""}
          {t("Try.again.later")}
        </div>
      </div>
    );
  }
};
