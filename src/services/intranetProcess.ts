import { toast } from "react-toastify";
import * as intranetService from "./intranet";
import { ItemProps } from "../interfaces/intranet";

/**
 * Obtém os documentos pendentes por processo
 */
export const getPendingDocuments = async (process: string): Promise<ItemProps[]> => {
  return await intranetService.getPendingApprovals(process);
};

/**
 * Obtém os detalhes de um documento pelo número, processo e item atual
 */
export const getDocumentDetails = async (
  documentNumber: string,
  process: string,
  currentDocument: ItemProps
): Promise<ItemProps | ItemProps[] | undefined> => {
  return await intranetService.getDocumentDetails(documentNumber, process, currentDocument);
};

/**
 * Aprova um documento
 */
export const approveDocument = async (
  item: ItemProps,
  process: string
): Promise<boolean> => {
  const response = await intranetService.approveOrReproveDocument(
    item,
    true,
    "",
    process
  );
  return response.success;
};

/**
 * Reprova um documento com justificativa
 */
export const reproveDocument = async (
  item: ItemProps,
  justification: string,
  process: string
): Promise<boolean> => {
  const response = await intranetService.approveOrReproveDocument(
    item,
    false,
    justification,
    process
  );
  return response.success;
};

/**
 * Aprovação em massa de documentos
 */
export const massApproveDocuments = async (
  items: ItemProps[],
  process: string
): Promise<boolean> => {
  if (!items.length) {
    toast.warning("Nenhum documento foi selecionado para aprovação.");
    return false;
  }

  const response = await intranetService.massApproveOrReprove(
    items,
    true,
    "",
    process
  );
  return response.success;
};

/**
 * Reprovação em massa de documentos
 */
export const massReproveDocuments = async (
  items: ItemProps[],
  justification: string,
  process: string
): Promise<boolean> => {
  if (!items.length) {
    toast.warning("Nenhum documento foi selecionado para reprovação.");
    return false;
  }

  const response = await intranetService.massApproveOrReprove(
    items,
    false,
    justification,
    process
  );
  return response.success;
};

/**
 * Prepara os dados do documento para exibição nas tabelas
 */
export const prepareDocumentData = (data: ItemProps[]): ItemProps[] => {
  return intranetService.prepareDataTable(data);
};

/**
 * Atualiza a lista de documentos pendentes após aprovação/reprovação
 */
export const updatePendingDocuments = async (
  currentItems: ItemProps[],
  processedItems: ItemProps[],
  process: string
): Promise<ItemProps[]> => {
  return await intranetService.updatePendingList(
    currentItems,
    processedItems,
    process
  );
};
