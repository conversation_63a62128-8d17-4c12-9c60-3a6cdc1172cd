import { ApprovalResponse } from "@/api/approval/approval.service";
import { IResponse, IServiceNow } from "@/interfaces";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import { toast } from "react-toastify";
import { api, objectCatch } from "../api/api";
import { RouteServiceNow } from "../api/routes";
import i18n from "../hooks/translation";
import { handleApiError } from "./error";

export const ServiceNowService = {
  async getPendingApprovals(
    cache = true
  ): Promise<IResponse.Default<IServiceNow.ItemProps[]>> {
    try {
      const { data, status } = await api.get(RouteServiceNow.getDocuments, {
        params: { cache },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      });

      return { data, success: status === 200 };
    } catch (error) {
      handleApiError(error, t, "Service Now");
      return { data: [], success: false };
    }
  },

  async getDocumentDetails(
    documentNumber: string
  ): Promise<IResponse.Default<any>> {
    try {
      const { data, status } = await api.get(
        RouteServiceNow.getDocumentDetails(documentNumber)
      );

      return { data, success: status === 200 };
    } catch {
      return { ...objectCatch, data: "" };
    }
  },

  async approvalReprovalDocument(
    params: IServiceNow.ApprovalReprovalParams
  ): Promise<ApprovalResponse<IServiceNow.ItemProps[]>> {
    try {
      const { data, status } = await api.put(
        RouteServiceNow.approvalReprovalDocument(
          params.registerId,
          params.status,
          params.comment
        )
      );

      if (status === 200) {
        const { updatedHeaderData } = await this.updateDataAfterApproval(data);
        return {
          data: { header: updatedHeaderData },
          success: true,
          message: `${
            params.status === "A"
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.registerId}`,
        };
      }

      return {
        data: { header: [] as IServiceNow.ItemProps[] },
        success: false,
      };
    } catch {
      return {
        data: { header: [] as IServiceNow.ItemProps[] },
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${
          params.registerId
        }`,
      };
    }
  },

  prepareTableData(
    headerData: IServiceNow.ItemProps[]
  ): IServiceNow.ItemProps[] {
    if (!headerData) return [];

    // Como ServiceNow usa Registro como identificador, precisamos adaptar
    const uniqueRegistros = new Set<string>();
    headerData.forEach((item) => {
      if (item.Registro) uniqueRegistros.add(item.Registro);
    });

    return Array.from(uniqueRegistros).map((registro) => {
      const foundItem = headerData?.find(
        (resp: IServiceNow.ItemProps) => resp.Registro === registro
      );
      return foundItem as IServiceNow.ItemProps;
    });
  },

  async fetchAndPreparePendingApprovals(
    cache: boolean = true
  ): Promise<IResponse.Default<IServiceNow.ItemProps[]>> {
    try {
      const { data, success } = await this.getPendingApprovals(cache);

      if (!data) {
        return { data, success };
      }

      const dataPrepared = this.prepareTableData(data).map(
        (item: IServiceNow.ItemProps) => ({
          ...item,
          origin: "SERVICENOW",
          type: "SNOW",
        })
      );

      return {
        data: dataPrepared,
        success,
      };
    } catch (error) {
      console.error("Error fetching pending approvals:", error);
      toast.error(i18n.t("common.errorFetchingData"));
      throw error;
    }
  },

  async updateDataAfterApproval(
    currentHeaderData: IServiceNow.ItemProps[]
  ): Promise<{
    updatedHeaderData: IServiceNow.ItemProps[];
  }> {
    try {
      const { data } = await this.fetchAndPreparePendingApprovals(false);

      if (!data || !Array.isArray(data) || data.length === 0) {
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      return { updatedHeaderData: data };
    } catch (error) {
      console.error("Erro ao atualizar dados após aprovação:", error);
      toast.error("Erro ao atualizar dados após aprovação");
      throw error;
    }
  },
} as const;
