import { APIAriba } from "@/api";
import {
  Approval<PERSON><PERSON>in,
  ApprovalResponse,
} from "@/api/approval/approval.service";
import { IAriba } from "@/interfaces";
import { t } from "i18next";
import { getUniqueDocs } from "../utils/UniqueDocs";

export const AribaService = {
  /**
   * Prepara os dados para a tabela principal
   */
  prepareTableData: (
    headerData: IAriba.AribaItemType[]
  ): IAriba.AribaItemType[] => {
    if (!headerData || !headerData.length) return [];

    return getUniqueDocs(headerData).map((doc) => {
      const foundItem = headerData?.find(
        (resp: IAriba.AribaItemType) => resp.id === doc
      );
      return {
        ...foundItem,
      } as IAriba.AribaItemType;
    });
  },

  /**
   * Ordena as requisições por ordem de prioridade
   */
  sortApprovalRequests(
    requests: IAriba.ApprovalRequest[]
  ): IAriba.ApprovalRequest[] {
    if (!requests || !Array.isArray(requests)) {
      return [];
    }
    const map = new Map(requests.map((request) => [request.id, request]));
    const sortedRequests: IAriba.ApprovalRequest[] = [];
    const visited = new Set<string>();

    const visit = (request: IAriba.ApprovalRequest) => {
      if (!visited.has(request.id)) {
        visited.add(request.id);
        request.dependencyApprovalRequestsUID.forEach((dependencyId) => {
          const dependency = map.get(dependencyId);
          if (dependency) visit(dependency);
        });
        sortedRequests.push(request);
      }
    };

    requests.forEach(visit);

    return sortedRequests;
  },

  /**
   * Filtra os detalhes de um documento específico
   */
  filterDocumentDetails: (
    detailData: IAriba.AribaItemType[],
    documentNumber: string
  ): IAriba.AribaItemType[] => {
    if (!detailData?.length) return [];

    return detailData.filter(
      (detail: IAriba.AribaItemType) =>
        detail.task?.toString().trim() === documentNumber?.toString().trim()
    );
  },

  /**
   * Atualiza os dados após aprovação/reprovação
   */
  updateDataAfterApproval: async ({
    process,
    currentHeaderData,
  }: {
    process: string;
    currentHeaderData: IAriba.AribaItemType[];
  }): Promise<{
    updatedHeaderData: IAriba.AribaItemType[];
  }> => {
    try {
      const { data } = (await APIAriba.getPendingApprovals(false)) as {
        data: IAriba.PendingApprovalsProps;
      };

      if (!data) {
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      const aribaProcessMapping = {
        AS: "sourcing",
        AP: "project",
        AT: "contract",
        AR: "requisition",
      } as const;

      type AribaProcessType = keyof typeof aribaProcessMapping;
      const dataSource = aribaProcessMapping[process as AribaProcessType];

      const tasksData =
        data[dataSource]?.tasks.map((task: any) => ({
          ...task.detail,
          description: task.description,
          type: process,
          origin: "ARIBA" as ApprovalOrigin,
          aribaProcessType: process, // Garantindo que o processo também seja preservado
        })) || [];

      return {
        updatedHeaderData: tasksData,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },
  /**
   * Executa a aprovação/reprovação de um documento
   */
  approvalReprovalDocument: async (params: {
    itemCode: string;
    action: string;
    comment: string;
    aribaUser: string;
    typeProcess: string;
  }): Promise<ApprovalResponse<IAriba.AribaItemType[]>> => {
    try {
      const { data, success } = await APIAriba.approvalReprovalDocument(params);

      if (success) {
        const { updatedHeaderData } =
          await AribaService.updateDataAfterApproval({
            process: params.typeProcess,
            currentHeaderData: [],
          });

        return {
          message: `${
            params.action === "A"
              ? t("Success.in.approving")
              : t("Failure.success")
          } ${t("Document")} ${params.itemCode}`,
          data: {
            header: updatedHeaderData,
          },
          success: true,
        };
      }

      return {
        success,
        data: {
          header: data,
        },
        message: `${t("Error.in.approval")} ${t("Document")} ${
          params.itemCode
        }`,
      };
    } catch (error) {
      console.error("Error in Ariba approval/reproval:", error);
      throw error;
    }
  },
} as const;
