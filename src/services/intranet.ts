import { IIntranet } from "@/interfaces";
import { getUniqueDocs } from "@/utils/UniqueDocs";
import { t } from "i18next";
import * as APIIntranet from "../api/intranet";
import { IResponse } from "../interfaces/IResponse";
import { handleApiError } from "./error";
import { ApprovalResponse } from "@/api/approval/approval.service";

export const IntranetService = {
  /**
   * Prepara os dados para a tabela de aprovações
   */
  prepareDataTable(data: IIntranet.IntranetProps[]): IIntranet.IntranetProps[] {
    if (!data.length) return [];
    if (!data) return data;
    return getUniqueDocs(data).map((doc) => {
      const foundItem = data?.find(
        (resp: IIntranet.IntranetProps) => resp.CODIGO === doc
      );
      return {
        ...foundItem,
      } as IIntranet.IntranetProps;
    });
  },

  /**
   * Busca documentos pendentes da Intranet
   */
  async fetchAndPreparePendingApprovals(
    processType: IIntranet.ProcessKey,
    cache: boolean = true
  ): Promise<IResponse<IIntranet.IntranetProps[]>> {
    try {
      const { data, success } = await APIIntranet.getPendingApprovals(
        processType,
        cache
      );

      return {
        data: this.prepareDataTable(data),
        success,
      };
    } catch (error) {
      handleApiError(error, t, `Intranet ${processType || ""}`.trim());
      return { data: [], success: false };
    }
  },

  async updateDataAfterApproval(
    process: string,
    currentHeaderData: IIntranet.IntranetProps[]
  ): Promise<{
    updatedHeaderData: IIntranet.IntranetProps[];
  }> {
    try {
      const { data } = await this.fetchAndPreparePendingApprovals(
        process as IIntranet.ProcessKey,
        true
      );

      if (!data?.length) {
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      return {
        updatedHeaderData: data,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      throw error;
    }
  },

  /**
   * Executa a aprovação/reprovação de um documento
   * @param params - Parâmetros da aprovação/reprovação
   * @param process - Tipo do processo
   */
  approvalReprovalDocument: async (
    params: {
      docId: string;
      status: "A" | "R";
      comments?: string;
    },
    process: IIntranet.ProcessKey
  ): Promise<ApprovalResponse<IIntranet.IntranetProps[]>> => {
    try {
      const response = await APIIntranet.approvalReprovalDocument(
        {
          docId: params.docId,
          status: params.status,
          comments: params.comments,
        },
        process
      );

      if (response.success) {
        const { updatedHeaderData } =
          await IntranetService.updateDataAfterApproval(process, response.data);

        return {
          success: response.success,
          data: { header: updatedHeaderData },
        };
      }

      return {
        success: false,
        data: { header: [] },
      };
    } catch (error) {
      handleApiError(error, t, `Intranet ${process || ""}`.trim());
      throw error;
    }
  },
} as const;
