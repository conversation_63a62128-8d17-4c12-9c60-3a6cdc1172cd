import React, { Suspense } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

import Loading from "@/components/Loading";
import { Box, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { Header, Navigation } from "../components";
import { AdminPage } from "../pages/admin";
import DelegationPage from "../pages/delegation";
import Home from "../pages/home";
import Notification from "../pages/notification";
import { ApprovalsHistory } from "@/pages/approvalsHistory";

const AppRoutes: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  return (
    <>
      <Header />
      <Box
        sx={{
          padding: isMobile ? "0rem" : "2rem",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Navigation />
        <Suspense fallback={<Loading open={true} />}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/approvals-history" element={<ApprovalsHistory />} />
            <Route path="/delegation" element={<DelegationPage />} />
            <Route path="/notification" element={<Notification />} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/null" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </Box>
    </>
  );
};

export default AppRoutes;
