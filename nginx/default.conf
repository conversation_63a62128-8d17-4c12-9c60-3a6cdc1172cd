## nginx/default.conf
server {
  # Nginx listens on port 80 by default. You can change this if needed.
  listen 9090;

  # Specifies your domain. Use "localhost" for local development or your domain name for production.
  server_name localhost;

  # The root directory that contains the `dist` folder generated after building your app.
  root /usr/share/nginx/html;
  index index.html;

  # Serve all routes and pages
  # Use the base name to serve all pages. In this case, the base name is "/".
  location / {
    try_files $uri /index.html =404;
  }

  # Static assets configuration
  # Serve static files from the `public` folder
  location /public/ {
    root /usr/share/nginx/html;
    autoindex off;
  }

  # Example: If your base name is "/example", the location block will look like this:
  # location /example {
  #   rewrite ^/example(/.*) $1 break;
  #   try_files $uri /index.html =404;
  # }

  # Error handling
  error_page 404 /index.html;
}