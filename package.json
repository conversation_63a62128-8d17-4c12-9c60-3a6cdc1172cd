{"name": "brf-central-aprovacoes-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --open", "build": "tsc && vite build --mode development", "build:dev": "tsc && vite build --mode development", "build:test": "tsc && vite build --mode test", "build:prd": "tsc && vite build --mode production", "test": "jest --config jest.config.ts --coverage", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.7.0", "@azure/msal-react": "^3.0.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@mui/system": "^5.15.14", "@mui/x-date-pickers": "^8.3.1", "@testing-library/dom": "^10.0.0", "ag-grid-community": "^28.2.1", "ag-grid-react": "^28.2.1", "axios": "^1.8.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "i18next": "^24.2.3", "jest-summary-reporter": "^0.0.2", "jwt-decode": "^4.0.0", "primereact": "^10.9.7", "react": "18", "react-data-table-component": "^7.7.0", "react-dom": "18", "react-i18next": "^15.4.1", "react-iframe": "^1.8.5", "react-joyride": "2.6.0", "react-query": "^3.39.3", "react-router-dom": "^7.5.2", "react-toastify": "^11.0.5", "text-encoding": "^0.7.0", "util": "^0.12.5", "uuid": "^11.1.0", "zustand": "5.0.6"}, "resolutions": {"brace-expansion": "^1.1.12"}, "devDependencies": {"@eslint/js": "^9.21.0", "@swc/jest": "^0.2.38", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/testing-library__jest-dom": "^6.0.0", "@types/text-encoding": "^0.0.40", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "styled-components": "^6.1.17", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": ">=6.2.7", "vite-tsconfig-paths": "^5.1.4", "whatwg-fetch": "^3.6.20"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}