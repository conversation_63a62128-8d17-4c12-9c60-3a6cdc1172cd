{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 15, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 15, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1742923060258, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["<PERSON>th Hook"], "duration": 106, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should initialize with default user state", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should initialize with default user state"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should update employeeRepresentedID when setEmployeeRepresentedId is called", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should update employeeRepresentedID when setEmployeeRepresentedId is called"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should mark tutorial as done when tutorialDone is called", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should mark tutorial as done when tutorialDone is called"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 20, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should reset user state on authentication error during login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reset user state on authentication error during login"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 32, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle successful login and update user state", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should handle successful login and update user state"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle login response with status 401 and reset user state", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle login response with status 401 and reset user state"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 102, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle goBackToUserIntials and update user state", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle goBackToUserIntials and update user state"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should return the current user if no accountIdOriginal is found", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return the current user if no accountIdOriginal is found"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should set hasFilter to true if user has special permissions or permissionsReport", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should set hasFilter to true if user has special permissions or permissionsReport"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should not set hasFilter to true if user lacks special permissions and permissionsReport is empty", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should not set hasFilter to true if user lacks special permissions and permissionsReport is empty"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle getTokenForThisUser and update user state correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle getTokenForThisUser and update user state correctly"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle getTokenForThisUser and update user state when permissionsReport exists", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle getTokenForThisUser and update user state when permissionsReport exists"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle getTokenForThisUser and update user state when processApproval exists on report admin page login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle getTokenForThisUser and update user state when processApproval exists on report admin page login"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should handle getTokenForThisUser and update user state when processApproval exists on report admin page login", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle getTokenForThisUser and update user state when processApproval exists on report admin page login"}, {"ancestorTitles": ["<PERSON>th Hook"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "Auth Hook should reset user state correctly when getTokenInitial is called", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reset user state correctly when getTokenInitial is called"}], "endTime": 1742923064621, "message": "", "name": "C:\\Users\\<USER>\\OneDrive - BRF S.A\\Documents\\CENTRAL GITHUB\\brf-central-aprovacoes-web\\src\\__tests__\\hooks\\auth.test.tsx", "startTime": 1742923061983, "status": "passed", "summary": ""}], "wasInterrupted": false}