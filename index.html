<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Central de Aprovações - BRF" />
  <style>
    @font-face {
      font-family: "CoTextCorp";
      src: url("/fonts/Co-Text-Corp-Regular.woff2") format("woff2"),
        url("/fonts/Co-Text-Corp-Regular.woff") format("woff");
      font-weight: normal;
      font-style: normal;
    }
  </style>
  <script>
    if (window.location.href.indexOf('https://aprovacoes.brf.com/') !== -1) {
      window.location.href = window.location.href.replace('aprovacoes.brf.com', 'approvals.brf.com');
      // window.location.replace(allUrl);
    }
  </script>
  <script>
    if (window.location.href.indexOf('/iframeClose') > -1) {
      sessionStorage.setItem('docusignRouteWithParams', window.location.search);
      sessionStorage.setItem('closingModalDocusign', true);
    }
    function isIE() {
      var ua = window.navigator.userAgent; //Check the userAgent property of the window.navigator object
      var msie = ua.indexOf('MSIE '); // IE 10 or older
      var trident = ua.indexOf('Trident/'); //IE 11
      return (msie > 0 || trident > 0);
    }
    if (isIE()) {
      var style = document.createElement('link');
      style.rel = 'stylesheet';
      style.href = '/style-ie.css'
      document.head.appendChild(style);
    }
  </script>
  <title>Central de Aprovações - BRF</title>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>