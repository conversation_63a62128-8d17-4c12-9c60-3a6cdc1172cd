name: Build & Publish Central Aprovacoes Web

on:
  workflow_dispatch:
  push:
    branches: [dev, qas, main]

jobs:    
  # CI: Build e Push para ACR
  CI:
     name: Build and Publish - ${{ github.ref_name }}    
     runs-on: ubuntu-latest

     steps:
       - name: Checkout code
         uses: actions/checkout@v2

       - name: Setup Node.js
         uses: actions/setup-node@v4
         with:
           node-version: '22'
           cache: 'yarn'

       - name: Install dependencies
         run: yarn install --frozen-lockfile

       - name: Login to ACR
         run: |
           echo ${{ secrets.DOCKER_PASSWORD }} | docker login brfacrcentralaprovacoes.azurecr.io -u ${{ secrets.DOCKER_USERNAME }} --password-stdin

       - name: Build e Publish para Dev
         if: github.ref_name == 'dev'
         run: |
           docker build -f Dockerfile.dev -t brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-dev:latest .
           docker push brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-dev:latest
           docker logout brfacrcentralaprovacoes.azurecr.io

       - name: Build e Publish para QAS
         if: github.ref_name == 'qas'
         run: |
           docker build -f Dockerfile.qas -t brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-qas:latest .
           docker push brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-qas:latest
           docker logout brfacrcentralaprovacoes.azurecr.io

       - name: Build e Publish para Produção
         if: github.ref_name == 'main'
         run: |
           docker build -f Dockerfile.main -t brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-main:latest .
           docker push brfacrcentralaprovacoes.azurecr.io/brf-central-aprovacoes-web-main:latest
           docker logout brfacrcentralaprovacoes.azurecr.io
